'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { 
  User, 
  Building, 
  Search, 
  Filter, 
  AlertCircle, 
  Phone, 
  Mail, 
  MapPin,
  Briefcase,
  Plus,
  ExternalLink
} from 'lucide-react'
import { useGetPropertyOwnersQuery } from '@/store/api/propertyOwnersApi'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface OwnerDeveloperData {
  ownerId?: string
  developerId?: string
}

interface OwnerDeveloperSelectorProps {
  value: OwnerDeveloperData
  onChange: (data: OwnerDeveloperData) => void
  errors?: {
    ownerId?: string[]
    developerId?: string[]
  }
}

interface PropertyOwner {
  _id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  company?: string
  isOwner: boolean
  isDeveloper: boolean
  isInvestor: boolean
  address: {
    city: string
    state: string
  }
  developerDetails?: {
    experience: number
    completedProjects: number
    specialization: string[]
  }
}

export default function OwnerDeveloperSelector({ value, onChange, errors }: OwnerDeveloperSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState<'all' | 'owner' | 'developer'>('all')
  const [selectedOwner, setSelectedOwner] = useState<PropertyOwner | null>(null)
  const [selectedDeveloper, setSelectedDeveloper] = useState<PropertyOwner | null>(null)

  // Fetch property owners/developers
  const { data: ownersData, isLoading, error } = useGetPropertyOwnersQuery({
    page: 1,
    limit: 100,
    search: searchTerm,
    includeProperties: false
  })

  const owners = ownersData?.data?.data || []

  // Filter owners based on role and search
  const filteredOwners = owners.filter((owner: PropertyOwner) => {
    const matchesSearch = searchTerm === '' || 
      `${owner.firstName} ${owner.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
      owner.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      owner.company?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesRole = roleFilter === 'all' || 
      (roleFilter === 'owner' && owner.isOwner) ||
      (roleFilter === 'developer' && owner.isDeveloper)

    return matchesSearch && matchesRole
  })

  // Get owners only
  const propertyOwners = filteredOwners.filter((owner: PropertyOwner) => owner.isOwner)
  
  // Get developers only
  const developers = filteredOwners.filter((owner: PropertyOwner) => owner.isDeveloper)

  // Update selected owner/developer when IDs change
  useEffect(() => {
    if (value.ownerId) {
      const owner = owners.find((o: PropertyOwner) => o._id === value.ownerId)
      setSelectedOwner(owner || null)
    }
    if (value.developerId) {
      const developer = owners.find((o: PropertyOwner) => o._id === value.developerId)
      setSelectedDeveloper(developer || null)
    }
  }, [value.ownerId, value.developerId, owners])

  const handleOwnerSelect = (ownerId: string) => {
    onChange({
      ...value,
      ownerId: ownerId
    })
  }

  const handleDeveloperSelect = (developerId: string) => {
    onChange({
      ...value,
      developerId: developerId
    })
  }

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
  }

  const getRoleBadges = (owner: PropertyOwner) => {
    const roles = []
    if (owner.isOwner) roles.push({ label: 'Owner', icon: Building, color: 'bg-blue-500' })
    if (owner.isDeveloper) roles.push({ label: 'Developer', icon: Briefcase, color: 'bg-green-500' })
    if (owner.isInvestor) roles.push({ label: 'Investor', icon: User, color: 'bg-purple-500' })
    
    return roles.map((role, index) => (
      <Badge key={index} variant="secondary" className="text-xs">
        <role.icon className="h-3 w-3 mr-1" />
        {role.label}
      </Badge>
    ))
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-2">Loading owners and developers...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-500">
            <AlertCircle className="h-8 w-8 mx-auto mb-2" />
            <p>Failed to load owners and developers</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Property Owner & Developer
        </CardTitle>
        <CardDescription>
          Select existing property owner and developer for this property
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name, email, or company..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <Select value={roleFilter} onValueChange={(value: any) => setRoleFilter(value)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="owner">Owners Only</SelectItem>
                <SelectItem value="developer">Developers Only</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Property Owner Selection */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-base font-medium">Property Owner (Optional)</Label>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open('/properties/owners/add', '_blank')}
              className="text-xs"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add New Owner
              <ExternalLink className="h-3 w-3 ml-1" />
            </Button>
          </div>
          
          {selectedOwner ? (
            <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarFallback className="bg-blue-500 text-white">
                      {getInitials(selectedOwner.firstName, selectedOwner.lastName)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-medium">{selectedOwner.firstName} {selectedOwner.lastName}</h4>
                    <p className="text-sm text-muted-foreground">{selectedOwner.email}</p>
                    <div className="flex items-center gap-2 mt-1">
                      {getRoleBadges(selectedOwner)}
                    </div>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleOwnerSelect('')}
                >
                  Change
                </Button>
              </div>
            </div>
          ) : (
            <Select value={value.ownerId || ''} onValueChange={handleOwnerSelect}>
              <SelectTrigger className={errors?.ownerId ? 'border-red-500' : ''}>
                <SelectValue placeholder="Select property owner" />
              </SelectTrigger>
              <SelectContent>
                {propertyOwners.map((owner: PropertyOwner) => (
                  <SelectItem key={owner._id} value={owner._id}>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">
                          {getInitials(owner.firstName, owner.lastName)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{owner.firstName} {owner.lastName}</div>
                        <div className="text-xs text-muted-foreground">{owner.company || owner.email}</div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
          
          {errors?.ownerId && (
            <p className="text-sm text-red-500 flex items-center gap-1">
              <AlertCircle className="h-4 w-4" />
              {errors.ownerId[0]}
            </p>
          )}
        </div>

        {/* Developer Selection */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-base font-medium">Developer Information *</Label>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open('/properties/owners/add', '_blank')}
              className="text-xs"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add New Developer
              <ExternalLink className="h-3 w-3 ml-1" />
            </Button>
          </div>
          
          {selectedDeveloper ? (
            <div className="p-4 border rounded-lg bg-green-50 border-green-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarFallback className="bg-green-500 text-white">
                      {getInitials(selectedDeveloper.firstName, selectedDeveloper.lastName)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-medium">{selectedDeveloper.firstName} {selectedDeveloper.lastName}</h4>
                    <p className="text-sm text-muted-foreground">{selectedDeveloper.company || selectedDeveloper.email}</p>
                    <div className="flex items-center gap-2 mt-1">
                      {getRoleBadges(selectedDeveloper)}
                      {selectedDeveloper.developerDetails && (
                        <Badge variant="outline" className="text-xs">
                          {selectedDeveloper.developerDetails.experience}+ years exp
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDeveloperSelect('')}
                >
                  Change
                </Button>
              </div>
            </div>
          ) : (
            <Select value={value.developerId || ''} onValueChange={handleDeveloperSelect}>
              <SelectTrigger className={errors?.developerId ? 'border-red-500' : ''}>
                <SelectValue placeholder="Select developer" />
              </SelectTrigger>
              <SelectContent>
                {developers.map((developer: PropertyOwner) => (
                  <SelectItem key={developer._id} value={developer._id}>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">
                          {getInitials(developer.firstName, developer.lastName)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{developer.firstName} {developer.lastName}</div>
                        <div className="text-xs text-muted-foreground">
                          {developer.company || developer.email}
                          {developer.developerDetails && (
                            <span className="ml-2">• {developer.developerDetails.experience}+ years</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
          
          {errors?.developerId && (
            <p className="text-sm text-red-500 flex items-center gap-1">
              <AlertCircle className="h-4 w-4" />
              {errors.developerId[0]}
            </p>
          )}
        </div>

        {/* Summary */}
        {(selectedOwner || selectedDeveloper) && (
          <div className="p-4 bg-muted/50 rounded-lg">
            <h4 className="font-medium text-sm mb-2">Selection Summary</h4>
            <div className="space-y-2 text-sm">
              {selectedOwner && (
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-blue-500" />
                  <span>Owner: {selectedOwner.firstName} {selectedOwner.lastName}</span>
                </div>
              )}
              {selectedDeveloper && (
                <div className="flex items-center gap-2">
                  <Briefcase className="h-4 w-4 text-green-500" />
                  <span>Developer: {selectedDeveloper.firstName} {selectedDeveloper.lastName}</span>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
