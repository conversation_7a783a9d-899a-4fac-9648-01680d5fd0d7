'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { MapPin, Navigation, Loader2, CheckCircle, AlertCircle } from 'lucide-react'
import { toast } from 'sonner'

interface LocationData {
  address: string
  city: string
  state: string
  pincode: string
  coordinates?: {
    latitude: number
    longitude: number
  }
}

interface LocationPickerProps {
  value: LocationData
  onChange: (location: LocationData) => void
  errors?: {
    address?: string[]
    city?: string[]
    state?: string[]
    pincode?: string[]
    coordinates?: string[]
  }
}

export default function LocationPicker({ value, onChange, errors }: LocationPickerProps) {
  const [isDetectingLocation, setIsDetectingLocation] = useState(false)
  const [locationError, setLocationError] = useState<string | null>(null)

  const handleInputChange = (field: keyof LocationData, inputValue: string) => {
    onChange({
      ...value,
      [field]: inputValue
    })
  }

  const detectCurrentLocation = async () => {
    if (!navigator.geolocation) {
      setLocationError('Geolocation is not supported by this browser')
      toast.error('Geolocation not supported')
      return
    }

    setIsDetectingLocation(true)
    setLocationError(null)

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const { latitude, longitude } = position.coords
          
          // Update coordinates
          onChange({
            ...value,
            coordinates: { latitude, longitude }
          })

          // Try to get address from coordinates using reverse geocoding
          // This would typically use a geocoding service like Google Maps API
          toast.success('Location detected successfully')
          
        } catch (error) {
          console.error('Error getting address:', error)
          toast.error('Could not get address from location')
        } finally {
          setIsDetectingLocation(false)
        }
      },
      (error) => {
        setIsDetectingLocation(false)
        let errorMessage = 'Failed to get location'
        
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied by user'
            break
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information unavailable'
            break
          case error.TIMEOUT:
            errorMessage = 'Location request timed out'
            break
        }
        
        setLocationError(errorMessage)
        toast.error(errorMessage)
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    )
  }

  const validatePincode = async (pincode: string) => {
    if (pincode.length === 6) {
      // Here you could integrate with a pincode API to get city/state
      // For now, we'll just validate the format
      const isValid = /^[0-9]{6}$/.test(pincode)
      if (!isValid) {
        toast.error('Invalid pincode format')
      }
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Property Location
        </CardTitle>
        <CardDescription>
          Enter the complete address details for the property
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Address Field */}
        <div className="space-y-2">
          <Label htmlFor="address">Complete Address *</Label>
          <Input
            id="address"
            placeholder="Enter complete address with landmarks"
            value={value.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            className={errors?.address ? 'border-red-500' : ''}
          />
          {errors?.address && (
            <p className="text-sm text-red-500 flex items-center gap-1">
              <AlertCircle className="h-4 w-4" />
              {errors.address[0]}
            </p>
          )}
        </div>

        {/* City and State Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="city">City *</Label>
            <Input
              id="city"
              placeholder="Enter city"
              value={value.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              className={errors?.city ? 'border-red-500' : ''}
            />
            {errors?.city && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.city[0]}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="state">State *</Label>
            <Input
              id="state"
              placeholder="Enter state"
              value={value.state}
              onChange={(e) => handleInputChange('state', e.target.value)}
              className={errors?.state ? 'border-red-500' : ''}
            />
            {errors?.state && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.state[0]}
              </p>
            )}
          </div>
        </div>

        {/* Pincode Field */}
        <div className="space-y-2">
          <Label htmlFor="pincode">Pincode *</Label>
          <Input
            id="pincode"
            placeholder="Enter 6-digit pincode"
            value={value.pincode}
            onChange={(e) => {
              handleInputChange('pincode', e.target.value)
              if (e.target.value.length === 6) {
                validatePincode(e.target.value)
              }
            }}
            maxLength={6}
            className={errors?.pincode ? 'border-red-500' : ''}
          />
          {errors?.pincode && (
            <p className="text-sm text-red-500 flex items-center gap-1">
              <AlertCircle className="h-4 w-4" />
              {errors.pincode[0]}
            </p>
          )}
        </div>

        {/* Location Detection */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label>GPS Coordinates</Label>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={detectCurrentLocation}
              disabled={isDetectingLocation}
              className="flex items-center gap-2"
            >
              {isDetectingLocation ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Navigation className="h-4 w-4" />
              )}
              {isDetectingLocation ? 'Detecting...' : 'Detect Location'}
            </Button>
          </div>

          {value.coordinates && (
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="flex items-center gap-1">
                <CheckCircle className="h-3 w-3" />
                Lat: {value.coordinates.latitude.toFixed(6)}
              </Badge>
              <Badge variant="secondary" className="flex items-center gap-1">
                <CheckCircle className="h-3 w-3" />
                Lng: {value.coordinates.longitude.toFixed(6)}
              </Badge>
            </div>
          )}

          {locationError && (
            <p className="text-sm text-red-500 flex items-center gap-1">
              <AlertCircle className="h-4 w-4" />
              {locationError}
            </p>
          )}
        </div>

        {/* Manual Coordinates Input */}
        {!value.coordinates && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="latitude">Latitude (Optional)</Label>
              <Input
                id="latitude"
                type="number"
                step="any"
                placeholder="e.g., 28.6139"
                onChange={(e) => {
                  const lat = parseFloat(e.target.value)
                  if (!isNaN(lat)) {
                    onChange({
                      ...value,
                      coordinates: {
                        latitude: lat,
                        longitude: value.coordinates?.longitude || 0
                      }
                    })
                  }
                }}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="longitude">Longitude (Optional)</Label>
              <Input
                id="longitude"
                type="number"
                step="any"
                placeholder="e.g., 77.2090"
                onChange={(e) => {
                  const lng = parseFloat(e.target.value)
                  if (!isNaN(lng)) {
                    onChange({
                      ...value,
                      coordinates: {
                        latitude: value.coordinates?.latitude || 0,
                        longitude: lng
                      }
                    })
                  }
                }}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
