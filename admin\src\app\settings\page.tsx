'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import DashboardLayout from '@/components/layout/DashboardLayout'
import {
  Settings as SettingsIcon,
  Plus,
  Edit,
  Trash2,
  RefreshCw,
  Search,
  Loader2
} from 'lucide-react'
import { toast } from 'sonner'
import {
  useGetAdminSettingsQuery,
  useCreateAdminSettingMutation,
  useUpdateAdminSettingMutation,
  useToggleAdminSettingStatusMutation,
  useDeleteAdminSettingMutation,
  AdminSetting
} from '@/store/api/settingsApi'

// Types
type SettingCategory = 'authentication' | 'notification' | 'payment' | 'security' | 'system'
type SettingType = 'boolean' | 'string' | 'number' | 'json'

interface FormData {
  key: string
  name: string
  description: string
  category: SettingCategory
  type: SettingType
  value: any
  defaultValue: any
  isActive: boolean
}

export default function SettingsPage() {
  // State
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [editingSetting, setEditingSetting] = useState<AdminSetting | null>(null)
  const [formData, setFormData] = useState<FormData>({
    key: '',
    name: '',
    description: '',
    category: 'system',
    type: 'boolean',
    value: '',
    defaultValue: '',
    isActive: true
  })

  // RTK Query hooks
  const {
    data: settingsResponse,
    isLoading,
    error,
    refetch
  } = useGetAdminSettingsQuery({})

  const [createSetting, { isLoading: isCreating }] = useCreateAdminSettingMutation()
  const [updateSetting, { isLoading: isUpdating }] = useUpdateAdminSettingMutation()
  const [toggleStatus, { isLoading: isToggling }] = useToggleAdminSettingStatusMutation()
  const [deleteSetting, { isLoading: isDeleting }] = useDeleteAdminSettingMutation()

  // Extract settings from response
  const settings = settingsResponse?.data?.settings || []

  // Categories for filtering
  const categories = [
    { value: 'all', label: 'All Categories', color: 'bg-gray-100' },
    { value: 'authentication', label: 'Authentication', color: 'bg-sky-100' },
    { value: 'notification', label: 'Notification', color: 'bg-yellow-100' },
    { value: 'payment', label: 'Payment', color: 'bg-green-100' },
    { value: 'security', label: 'Security', color: 'bg-red-100' },
    { value: 'system', label: 'System', color: 'bg-purple-100' }
  ]

  // Filter settings based on category and search
  const filteredSettings = settings.filter(setting => {
    const matchesCategory = selectedCategory === 'all' || setting.category === selectedCategory
    const matchesSearch = !searchTerm ||
      setting.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      setting.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
      setting.description.toLowerCase().includes(searchTerm.toLowerCase())

    return matchesCategory && matchesSearch
  })

  // Handle API errors
  useEffect(() => {
    if (error) {
      console.error('Settings API Error:', error)
      toast.error('Error loading settings: ' + (error as any)?.data?.message || 'Network error')
    }
  }, [error])

  // Reset form data
  const resetForm = () => {
    setFormData({
      key: '',
      name: '',
      description: '',
      category: 'system',
      type: 'boolean',
      value: '',
      defaultValue: '',
      isActive: true
    })
  }

  // Handle create setting
  const handleCreate = async () => {
    try {
      let processedValue = formData.value
      let processedDefaultValue = formData.defaultValue

      // Process values based on type
      if (formData.type === 'boolean') {
        processedValue = processedValue === 'true' || processedValue === true
        processedDefaultValue = processedDefaultValue === 'true' || processedDefaultValue === true
      } else if (formData.type === 'number') {
        processedValue = Number(processedValue)
        processedDefaultValue = Number(processedDefaultValue)
      } else if (formData.type === 'json') {
        try {
          processedValue = JSON.parse(processedValue)
          processedDefaultValue = JSON.parse(processedDefaultValue)
        } catch (e) {
          toast.error('Invalid JSON format')
          return
        }
      }

      await createSetting({
        ...formData,
        value: processedValue,
        defaultValue: processedDefaultValue
      }).unwrap()

      toast.success('Setting created successfully')
      setShowCreateDialog(false)
      resetForm()
      refetch()
    } catch (error: any) {
      console.error('Error creating setting:', error)
      toast.error('Failed to create setting: ' + (error?.data?.message || 'Unknown error'))
    }
  }

  // Handle edit setting
  const handleEdit = (setting: AdminSetting) => {
    setEditingSetting(setting)
    setFormData({
      key: setting.key,
      name: setting.name,
      description: setting.description,
      category: setting.category,
      type: setting.type,
      value: setting.type === 'json' ? JSON.stringify(setting.value, null, 2) : String(setting.value),
      defaultValue: setting.type === 'json' ? JSON.stringify(setting.defaultValue, null, 2) : String(setting.defaultValue),
      isActive: setting.isActive
    })
    setShowEditDialog(true)
  }

  // Handle update setting
  const handleUpdate = async () => {
    if (!editingSetting) return

    try {
      let processedValue = formData.value
      let processedDefaultValue = formData.defaultValue

      // Process values based on type
      if (formData.type === 'boolean') {
        processedValue = processedValue === 'true' || processedValue === true
        processedDefaultValue = processedDefaultValue === 'true' || processedDefaultValue === true
      } else if (formData.type === 'number') {
        processedValue = Number(processedValue)
        processedDefaultValue = Number(processedDefaultValue)
      } else if (formData.type === 'json') {
        try {
          processedValue = JSON.parse(processedValue)
          processedDefaultValue = JSON.parse(processedDefaultValue)
        } catch (e) {
          toast.error('Invalid JSON format')
          return
        }
      }

      await updateSetting({
        id: editingSetting._id || '',
        settingData: {
          key: formData.key,
          name: formData.name,
          description: formData.description,
          category: formData.category,
          type: formData.type,
          defaultValue: processedDefaultValue,
          isActive: formData.isActive
        },
        value: processedValue
      }).unwrap()

      toast.success('Setting updated successfully')
      setShowEditDialog(false)
      setEditingSetting(null)
      resetForm()
      refetch()
    } catch (error: any) {
      console.error('Error updating setting:', error)
      toast.error('Failed to update setting: ' + (error?.data?.message || 'Unknown error'))
    }
  }

  // Handle toggle status
  const handleToggleStatus = async (setting: AdminSetting) => {
    try {
      await toggleStatus({
        id: setting._id || '',
        isActive: !setting.isActive
      }).unwrap()

      toast.success(`Setting ${!setting.isActive ? 'enabled' : 'disabled'} successfully`)
      refetch()
    } catch (error: any) {
      console.error('Error toggling setting status:', error)
      toast.error('Failed to toggle setting status: ' + (error?.data?.message || 'Unknown error'))
    }
  }

  // Handle delete setting
  const handleDelete = async (setting: AdminSetting) => {
    if (!confirm('Are you sure you want to delete this setting? This action cannot be undone.')) {
      return
    }

    try {
      await deleteSetting(setting._id || '').unwrap()
      toast.success('Setting deleted successfully')
      refetch()
    } catch (error: any) {
      console.error('Error deleting setting:', error)
      toast.error('Failed to delete setting: ' + (error?.data?.message || 'Unknown error'))
    }
  }



  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Settings Management</h1>
            <p className="text-muted-foreground">
              Manage system settings and configurations
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => setShowCreateDialog(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Setting
            </Button>

            <Button
              onClick={() => refetch()}
              variant="outline"
              size="icon"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Label htmlFor="search">Search Settings</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Search by name, key, or description..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-full sm:w-48">
                <Label htmlFor="category">Category</Label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Settings Table */}
        <Card>
          <CardHeader>
            <CardTitle>Settings ({filteredSettings.length})</CardTitle>
            <CardDescription>
              Manage your application settings and configurations
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading settings...</span>
              </div>
            ) : filteredSettings.length === 0 ? (
              <div className="text-center py-8">
                <SettingsIcon className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No settings found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm || selectedCategory !== 'all'
                    ? 'No settings match your current filters.'
                    : 'Get started by creating your first setting.'}
                </p>
                <Button onClick={() => setShowCreateDialog(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Setting
                </Button>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Key</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Value</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSettings.map((setting) => (
                      <TableRow key={setting._id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{setting.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {setting.description}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <code className="bg-muted px-2 py-1 rounded text-sm">
                            {setting.key}
                          </code>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant="secondary"
                            className={categories.find(c => c.value === setting.category)?.color}
                          >
                            {setting.category}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{setting.type}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="max-w-32 truncate">
                            {setting.type === 'boolean'
                              ? (setting.value ? 'true' : 'false')
                              : setting.type === 'json'
                              ? JSON.stringify(setting.value)
                              : String(setting.value)
                            }
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Switch
                              checked={setting.isActive}
                              onCheckedChange={() => handleToggleStatus(setting)}
                              disabled={isToggling}
                            />
                            <span className="text-sm">
                              {setting.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleEdit(setting)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDelete(setting)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Create Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create New Setting</DialogTitle>
            <DialogDescription>
              Add a new system setting with the specified configuration.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="key">Key *</Label>
                <Input
                  id="key"
                  placeholder="SETTING_KEY"
                  value={formData.key}
                  onChange={(e) => setFormData(prev => ({ ...prev, key: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  placeholder="Setting Name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Setting description..."
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, category: value as SettingCategory }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="authentication">Authentication</SelectItem>
                    <SelectItem value="notification">Notification</SelectItem>
                    <SelectItem value="payment">Payment</SelectItem>
                    <SelectItem value="security">Security</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">Type</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, type: value as SettingType }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="boolean">Boolean</SelectItem>
                    <SelectItem value="string">String</SelectItem>
                    <SelectItem value="number">Number</SelectItem>
                    <SelectItem value="json">JSON</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="value">Value *</Label>
                {formData.type === 'boolean' ? (
                  <Select
                    value={String(formData.value)}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select value" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="true">True</SelectItem>
                      <SelectItem value="false">False</SelectItem>
                    </SelectContent>
                  </Select>
                ) : formData.type === 'json' ? (
                  <Textarea
                    id="value"
                    placeholder='{"key": "value"}'
                    value={formData.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                    rows={3}
                  />
                ) : (
                  <Input
                    id="value"
                    type={formData.type === 'number' ? 'number' : 'text'}
                    placeholder="Enter value"
                    value={formData.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                  />
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="defaultValue">Default Value *</Label>
                {formData.type === 'boolean' ? (
                  <Select
                    value={String(formData.defaultValue)}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, defaultValue: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select default value" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="true">True</SelectItem>
                      <SelectItem value="false">False</SelectItem>
                    </SelectContent>
                  </Select>
                ) : formData.type === 'json' ? (
                  <Textarea
                    id="defaultValue"
                    placeholder='{"key": "value"}'
                    value={formData.defaultValue}
                    onChange={(e) => setFormData(prev => ({ ...prev, defaultValue: e.target.value }))}
                    rows={3}
                  />
                ) : (
                  <Input
                    id="defaultValue"
                    type={formData.type === 'number' ? 'number' : 'text'}
                    placeholder="Enter default value"
                    value={formData.defaultValue}
                    onChange={(e) => setFormData(prev => ({ ...prev, defaultValue: e.target.value }))}
                  />
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
              />
              <Label htmlFor="isActive">Active</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreate} disabled={isCreating}>
              {isCreating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Setting
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Setting</DialogTitle>
            <DialogDescription>
              Update the setting configuration.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-key">Key *</Label>
                <Input
                  id="edit-key"
                  placeholder="SETTING_KEY"
                  value={formData.key}
                  onChange={(e) => setFormData(prev => ({ ...prev, key: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-name">Name *</Label>
                <Input
                  id="edit-name"
                  placeholder="Setting Name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                placeholder="Setting description..."
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-category">Category</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, category: value as SettingCategory }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="authentication">Authentication</SelectItem>
                    <SelectItem value="notification">Notification</SelectItem>
                    <SelectItem value="payment">Payment</SelectItem>
                    <SelectItem value="security">Security</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-type">Type</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, type: value as SettingType }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="boolean">Boolean</SelectItem>
                    <SelectItem value="string">String</SelectItem>
                    <SelectItem value="number">Number</SelectItem>
                    <SelectItem value="json">JSON</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-value">Value *</Label>
                {formData.type === 'boolean' ? (
                  <Select
                    value={String(formData.value)}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select value" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="true">True</SelectItem>
                      <SelectItem value="false">False</SelectItem>
                    </SelectContent>
                  </Select>
                ) : formData.type === 'json' ? (
                  <Textarea
                    id="edit-value"
                    placeholder='{"key": "value"}'
                    value={formData.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                    rows={3}
                  />
                ) : (
                  <Input
                    id="edit-value"
                    type={formData.type === 'number' ? 'number' : 'text'}
                    placeholder="Enter value"
                    value={formData.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                  />
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-defaultValue">Default Value *</Label>
                {formData.type === 'boolean' ? (
                  <Select
                    value={String(formData.defaultValue)}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, defaultValue: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select default value" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="true">True</SelectItem>
                      <SelectItem value="false">False</SelectItem>
                    </SelectContent>
                  </Select>
                ) : formData.type === 'json' ? (
                  <Textarea
                    id="edit-defaultValue"
                    placeholder='{"key": "value"}'
                    value={formData.defaultValue}
                    onChange={(e) => setFormData(prev => ({ ...prev, defaultValue: e.target.value }))}
                    rows={3}
                  />
                ) : (
                  <Input
                    id="edit-defaultValue"
                    type={formData.type === 'number' ? 'number' : 'text'}
                    placeholder="Enter default value"
                    value={formData.defaultValue}
                    onChange={(e) => setFormData(prev => ({ ...prev, defaultValue: e.target.value }))}
                  />
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="edit-isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
              />
              <Label htmlFor="edit-isActive">Active</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdate} disabled={isUpdating}>
              {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Update Setting
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  )
}