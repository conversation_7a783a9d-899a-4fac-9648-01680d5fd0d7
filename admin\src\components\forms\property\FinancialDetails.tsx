'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DollarSign, TrendingUp, Calculator, AlertCircle, Info, Users, Percent } from 'lucide-react'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface FinancialData {
  expectedReturns: number
  maturityPeriodMonths: number
  totalStocks: number
  pricePerStock: number
  availableStocks: number
  minimumInvestment?: number
  maximumInvestment?: number
  stockPrefix: string
  stockStartNumber: number
  referralCommissionRate: number
  salesCommissionRate: number
  referralCommissionPerStock: number
  salesCommissionPerStock: number
  commissionType: 'percentage' | 'fixed'
}

interface FinancialDetailsProps {
  value: FinancialData
  onChange: (financial: FinancialData) => void
  errors?: {
    expectedReturns?: string[]
    maturityPeriodMonths?: string[]
    totalStocks?: string[]
    pricePerStock?: string[]
    availableStocks?: string[]
    minimumInvestment?: string[]
    maximumInvestment?: string[]
    stockPrefix?: string[]
    stockStartNumber?: string[]
    referralCommissionRate?: string[]
    salesCommissionRate?: string[]
    referralCommissionPerStock?: string[]
    salesCommissionPerStock?: string[]
    commissionType?: string[]
  }
}

export default function FinancialDetails({ value, onChange, errors }: FinancialDetailsProps) {
  const handleInputChange = (field: keyof FinancialData, inputValue: string | number) => {
    if (field === 'stockPrefix' || field === 'commissionType') {
      onChange({
        ...value,
        [field]: inputValue
      })
    } else {
      const numericValue = typeof inputValue === 'string' ? parseFloat(inputValue) || 0 : inputValue
      onChange({
        ...value,
        [field]: numericValue
      })
    }
  }

  // Calculate derived values
  const totalPropertyValue = value.totalStocks * value.pricePerStock
  const minimumInvestmentAmount = value.minimumInvestment || value.pricePerStock
  const maximumInvestmentAmount = value.maximumInvestment || totalPropertyValue
  const maturityYears = value.maturityPeriodMonths / 12

  // Calculate commission amounts
  const totalReferralCommission = value.commissionType === 'percentage'
    ? (value.referralCommissionRate / 100) * totalPropertyValue
    : value.referralCommissionPerStock * value.totalStocks

  const totalSalesCommission = value.commissionType === 'percentage'
    ? (value.salesCommissionRate / 100) * totalPropertyValue
    : value.salesCommissionPerStock * value.totalStocks

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          Financial Details
        </CardTitle>
        <CardDescription>
          Configure investment parameters and expected returns
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Returns and Maturity */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Label htmlFor="expectedReturns">Expected Returns (% p.a.) *</Label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="h-4 w-4 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Annual percentage return expected from this investment</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="relative">
              <Input
                id="expectedReturns"
                type="number"
                step="0.1"
                min="0"
                max="100"
                placeholder="e.g., 12.5"
                value={value.expectedReturns || ''}
                onChange={(e) => handleInputChange('expectedReturns', e.target.value)}
                className={errors?.expectedReturns ? 'border-red-500' : ''}
              />
              <TrendingUp className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
            </div>
            {errors?.expectedReturns && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.expectedReturns[0]}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Label htmlFor="maturityPeriodMonths">Maturity Period (Months) *</Label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="h-4 w-4 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Duration of the investment in months</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <Input
              id="maturityPeriodMonths"
              type="number"
              min="1"
              max="600"
              placeholder="e.g., 36"
              value={value.maturityPeriodMonths || ''}
              onChange={(e) => handleInputChange('maturityPeriodMonths', e.target.value)}
              className={errors?.maturityPeriodMonths ? 'border-red-500' : ''}
            />
            {errors?.maturityPeriodMonths && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.maturityPeriodMonths[0]}
              </p>
            )}
            {value.maturityPeriodMonths > 0 && (
              <p className="text-sm text-muted-foreground">
                ≈ {maturityYears.toFixed(1)} years
              </p>
            )}
          </div>
        </div>

        {/* Stock Configuration */}
        <div className="space-y-4">
          <h4 className="font-medium flex items-center gap-2">
            <Calculator className="h-4 w-4" />
            Stock Configuration
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="totalStocks">Total Stocks *</Label>
              <Input
                id="totalStocks"
                type="number"
                min="1"
                placeholder="e.g., 1000"
                value={value.totalStocks || ''}
                onChange={(e) => handleInputChange('totalStocks', e.target.value)}
                className={errors?.totalStocks ? 'border-red-500' : ''}
              />
              {errors?.totalStocks && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.totalStocks[0]}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="pricePerStock">Price per Stock (₹) *</Label>
              <Input
                id="pricePerStock"
                type="number"
                step="0.01"
                min="0.01"
                placeholder="e.g., 1000"
                value={value.pricePerStock || ''}
                onChange={(e) => handleInputChange('pricePerStock', e.target.value)}
                className={errors?.pricePerStock ? 'border-red-500' : ''}
              />
              {errors?.pricePerStock && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.pricePerStock[0]}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="availableStocks">Available Stocks *</Label>
              <Input
                id="availableStocks"
                type="number"
                min="0"
                max={value.totalStocks}
                placeholder="e.g., 800"
                value={value.availableStocks || ''}
                onChange={(e) => handleInputChange('availableStocks', e.target.value)}
                className={errors?.availableStocks ? 'border-red-500' : ''}
              />
              {errors?.availableStocks && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.availableStocks[0]}
                </p>
              )}
              {value.availableStocks > value.totalStocks && (
                <p className="text-sm text-amber-600">
                  Available stocks cannot exceed total stocks
                </p>
              )}
            </div>
          </div>

          {/* Stock Numbering */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="stockPrefix">Stock Prefix *</Label>
              <Input
                id="stockPrefix"
                placeholder="e.g., PROP, STK, INV"
                value={value.stockPrefix || ''}
                onChange={(e) => handleInputChange('stockPrefix', e.target.value.toUpperCase())}
                className={errors?.stockPrefix ? 'border-red-500' : ''}
                maxLength={10}
              />
              {errors?.stockPrefix && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.stockPrefix[0]}
                </p>
              )}
              <p className="text-xs text-muted-foreground">
                Stock numbers will be: {value.stockPrefix || 'PREFIX'}-{String(value.stockStartNumber || 1).padStart(4, '0')} to {value.stockPrefix || 'PREFIX'}-{String((value.stockStartNumber || 1) + (value.totalStocks || 1) - 1).padStart(4, '0')}
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="stockStartNumber">Starting Stock Number *</Label>
              <Input
                id="stockStartNumber"
                type="number"
                min="1"
                placeholder="e.g., 1"
                value={value.stockStartNumber || ''}
                onChange={(e) => handleInputChange('stockStartNumber', e.target.value)}
                className={errors?.stockStartNumber ? 'border-red-500' : ''}
              />
              {errors?.stockStartNumber && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.stockStartNumber[0]}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Investment Limits */}
        <div className="space-y-4">
          <h4 className="font-medium flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Investment Limits
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Label htmlFor="minimumInvestment">Minimum Investment (₹)</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Minimum amount required to invest. Defaults to price per stock if not specified.</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="minimumInvestment"
                type="number"
                step="0.01"
                min={value.pricePerStock}
                placeholder={`Default: ₹${value.pricePerStock || 0}`}
                value={value.minimumInvestment || ''}
                onChange={(e) => handleInputChange('minimumInvestment', e.target.value)}
                className={errors?.minimumInvestment ? 'border-red-500' : ''}
              />
              {errors?.minimumInvestment && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.minimumInvestment[0]}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Label htmlFor="maximumInvestment">Maximum Investment (₹)</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Maximum amount a single investor can invest. Defaults to total property value.</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="maximumInvestment"
                type="number"
                step="0.01"
                min={value.minimumInvestment || value.pricePerStock}
                max={totalPropertyValue}
                placeholder={`Default: ₹${totalPropertyValue.toLocaleString('en-IN')}`}
                value={value.maximumInvestment || ''}
                onChange={(e) => handleInputChange('maximumInvestment', e.target.value)}
                className={errors?.maximumInvestment ? 'border-red-500' : ''}
              />
              {errors?.maximumInvestment && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.maximumInvestment[0]}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Commission Configuration */}
        <div className="space-y-4">
          <h4 className="font-medium flex items-center gap-2">
            <Users className="h-4 w-4" />
            Commission Configuration
          </h4>

          <div className="space-y-4">
            {/* Commission Type */}
            <div className="space-y-2">
              <Label htmlFor="commissionType">Commission Type *</Label>
              <Select
                value={value.commissionType || 'percentage'}
                onValueChange={(val) => handleInputChange('commissionType', val)}
              >
                <SelectTrigger className={errors?.commissionType ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select commission type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="percentage">Percentage Based</SelectItem>
                  <SelectItem value="fixed">Fixed Amount per Stock</SelectItem>
                </SelectContent>
              </Select>
              {errors?.commissionType && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.commissionType[0]}
                </p>
              )}
            </div>

            {/* Commission Rates */}
            {value.commissionType === 'percentage' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="referralCommissionRate">Referral Commission (%)</Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Percentage commission for referral partners</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <div className="relative">
                    <Input
                      id="referralCommissionRate"
                      type="number"
                      step="0.1"
                      min="0"
                      max="100"
                      placeholder="e.g., 2.5"
                      value={value.referralCommissionRate || ''}
                      onChange={(e) => handleInputChange('referralCommissionRate', e.target.value)}
                      className={errors?.referralCommissionRate ? 'border-red-500' : ''}
                    />
                    <Percent className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                  </div>
                  {errors?.referralCommissionRate && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.referralCommissionRate[0]}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="salesCommissionRate">Sales Commission (%)</Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Percentage commission for sales team</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <div className="relative">
                    <Input
                      id="salesCommissionRate"
                      type="number"
                      step="0.1"
                      min="0"
                      max="100"
                      placeholder="e.g., 1.5"
                      value={value.salesCommissionRate || ''}
                      onChange={(e) => handleInputChange('salesCommissionRate', e.target.value)}
                      className={errors?.salesCommissionRate ? 'border-red-500' : ''}
                    />
                    <Percent className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                  </div>
                  {errors?.salesCommissionRate && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.salesCommissionRate[0]}
                    </p>
                  )}
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="referralCommissionPerStock">Referral Commission per Stock (₹)</Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Fixed amount commission per stock for referral partners</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Input
                    id="referralCommissionPerStock"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="e.g., 25"
                    value={value.referralCommissionPerStock || ''}
                    onChange={(e) => handleInputChange('referralCommissionPerStock', e.target.value)}
                    className={errors?.referralCommissionPerStock ? 'border-red-500' : ''}
                  />
                  {errors?.referralCommissionPerStock && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.referralCommissionPerStock[0]}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="salesCommissionPerStock">Sales Commission per Stock (₹)</Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Fixed amount commission per stock for sales team</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Input
                    id="salesCommissionPerStock"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="e.g., 15"
                    value={value.salesCommissionPerStock || ''}
                    onChange={(e) => handleInputChange('salesCommissionPerStock', e.target.value)}
                    className={errors?.salesCommissionPerStock ? 'border-red-500' : ''}
                  />
                  {errors?.salesCommissionPerStock && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.salesCommissionPerStock[0]}
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Calculated Values */}
        {(value.totalStocks > 0 && value.pricePerStock > 0) && (
          <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
            <h4 className="font-medium text-sm">Calculated Values</h4>

            {/* Property Values */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <div className="text-center">
                <Badge variant="secondary" className="w-full justify-center">
                  Total Value
                </Badge>
                <p className="text-sm font-medium mt-1">
                  ₹{totalPropertyValue.toLocaleString('en-IN')}
                </p>
              </div>

              <div className="text-center">
                <Badge variant="secondary" className="w-full justify-center">
                  Min Investment
                </Badge>
                <p className="text-sm font-medium mt-1">
                  ₹{minimumInvestmentAmount.toLocaleString('en-IN')}
                </p>
              </div>

              <div className="text-center">
                <Badge variant="secondary" className="w-full justify-center">
                  Max Investment
                </Badge>
                <p className="text-sm font-medium mt-1">
                  ₹{maximumInvestmentAmount.toLocaleString('en-IN')}
                </p>
              </div>

              <div className="text-center">
                <Badge variant="secondary" className="w-full justify-center">
                  Sold Percentage
                </Badge>
                <p className="text-sm font-medium mt-1">
                  {value.totalStocks > 0 ? (((value.totalStocks - value.availableStocks) / value.totalStocks) * 100).toFixed(1) : 0}%
                </p>
              </div>
            </div>

            {/* Commission Values */}
            {(value.commissionType && (value.referralCommissionRate > 0 || value.salesCommissionRate > 0 || value.referralCommissionPerStock > 0 || value.salesCommissionPerStock > 0)) && (
              <div>
                <h5 className="font-medium text-sm mb-2">Commission Breakdown</h5>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <div className="text-center">
                    <Badge variant="outline" className="w-full justify-center">
                      Referral Commission
                    </Badge>
                    <p className="text-sm font-medium mt-1">
                      ₹{totalReferralCommission.toLocaleString('en-IN')}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {value.commissionType === 'percentage'
                        ? `${value.referralCommissionRate}% of total`
                        : `₹${value.referralCommissionPerStock} × ${value.totalStocks} stocks`
                      }
                    </p>
                  </div>

                  <div className="text-center">
                    <Badge variant="outline" className="w-full justify-center">
                      Sales Commission
                    </Badge>
                    <p className="text-sm font-medium mt-1">
                      ₹{totalSalesCommission.toLocaleString('en-IN')}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {value.commissionType === 'percentage'
                        ? `${value.salesCommissionRate}% of total`
                        : `₹${value.salesCommissionPerStock} × ${value.totalStocks} stocks`
                      }
                    </p>
                  </div>

                  <div className="text-center">
                    <Badge variant="outline" className="w-full justify-center">
                      Total Commission
                    </Badge>
                    <p className="text-sm font-medium mt-1">
                      ₹{(totalReferralCommission + totalSalesCommission).toLocaleString('en-IN')}
                    </p>
                  </div>

                  <div className="text-center">
                    <Badge variant="outline" className="w-full justify-center">
                      Net Property Value
                    </Badge>
                    <p className="text-sm font-medium mt-1">
                      ₹{(totalPropertyValue - totalReferralCommission - totalSalesCommission).toLocaleString('en-IN')}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Stock Range Preview */}
            {value.stockPrefix && value.stockStartNumber && value.totalStocks && (
              <div>
                <h5 className="font-medium text-sm mb-2">Stock Number Range</h5>
                <div className="flex items-center justify-center gap-2">
                  <Badge variant="outline">
                    {value.stockPrefix}-{String(value.stockStartNumber).padStart(4, '0')}
                  </Badge>
                  <span className="text-muted-foreground">to</span>
                  <Badge variant="outline">
                    {value.stockPrefix}-{String(value.stockStartNumber + value.totalStocks - 1).padStart(4, '0')}
                  </Badge>
                  <span className="text-muted-foreground">({value.totalStocks} stocks)</span>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
