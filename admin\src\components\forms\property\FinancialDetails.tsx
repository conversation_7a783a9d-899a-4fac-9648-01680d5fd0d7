'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { DollarSign, TrendingUp, Calculator, AlertCircle, Info } from 'lucide-react'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface FinancialData {
  expectedReturns: number
  maturityPeriodMonths: number
  totalStocks: number
  pricePerStock: number
  availableStocks: number
  minimumInvestment?: number
}

interface FinancialDetailsProps {
  value: FinancialData
  onChange: (financial: FinancialData) => void
  errors?: {
    expectedReturns?: string[]
    maturityPeriodMonths?: string[]
    totalStocks?: string[]
    pricePerStock?: string[]
    availableStocks?: string[]
    minimumInvestment?: string[]
  }
}

export default function FinancialDetails({ value, onChange, errors }: FinancialDetailsProps) {
  const handleInputChange = (field: keyof FinancialData, inputValue: string | number) => {
    const numericValue = typeof inputValue === 'string' ? parseFloat(inputValue) || 0 : inputValue
    onChange({
      ...value,
      [field]: numericValue
    })
  }

  // Calculate derived values
  const totalPropertyValue = value.totalStocks * value.pricePerStock
  const minimumInvestmentAmount = value.minimumInvestment || value.pricePerStock
  const maxPossibleInvestment = value.availableStocks * value.pricePerStock
  const annualizedReturn = value.expectedReturns
  const maturityYears = value.maturityPeriodMonths / 12

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          Financial Details
        </CardTitle>
        <CardDescription>
          Configure investment parameters and expected returns
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Returns and Maturity */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Label htmlFor="expectedReturns">Expected Returns (% p.a.) *</Label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="h-4 w-4 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Annual percentage return expected from this investment</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="relative">
              <Input
                id="expectedReturns"
                type="number"
                step="0.1"
                min="0"
                max="100"
                placeholder="e.g., 12.5"
                value={value.expectedReturns || ''}
                onChange={(e) => handleInputChange('expectedReturns', e.target.value)}
                className={errors?.expectedReturns ? 'border-red-500' : ''}
              />
              <TrendingUp className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
            </div>
            {errors?.expectedReturns && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.expectedReturns[0]}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Label htmlFor="maturityPeriodMonths">Maturity Period (Months) *</Label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="h-4 w-4 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Duration of the investment in months</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <Input
              id="maturityPeriodMonths"
              type="number"
              min="1"
              max="600"
              placeholder="e.g., 36"
              value={value.maturityPeriodMonths || ''}
              onChange={(e) => handleInputChange('maturityPeriodMonths', e.target.value)}
              className={errors?.maturityPeriodMonths ? 'border-red-500' : ''}
            />
            {errors?.maturityPeriodMonths && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.maturityPeriodMonths[0]}
              </p>
            )}
            {value.maturityPeriodMonths > 0 && (
              <p className="text-sm text-muted-foreground">
                ≈ {maturityYears.toFixed(1)} years
              </p>
            )}
          </div>
        </div>

        {/* Stock Configuration */}
        <div className="space-y-4">
          <h4 className="font-medium flex items-center gap-2">
            <Calculator className="h-4 w-4" />
            Stock Configuration
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="totalStocks">Total Stocks *</Label>
              <Input
                id="totalStocks"
                type="number"
                min="1"
                placeholder="e.g., 1000"
                value={value.totalStocks || ''}
                onChange={(e) => handleInputChange('totalStocks', e.target.value)}
                className={errors?.totalStocks ? 'border-red-500' : ''}
              />
              {errors?.totalStocks && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.totalStocks[0]}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="pricePerStock">Price per Stock (₹) *</Label>
              <Input
                id="pricePerStock"
                type="number"
                step="0.01"
                min="0.01"
                placeholder="e.g., 1000"
                value={value.pricePerStock || ''}
                onChange={(e) => handleInputChange('pricePerStock', e.target.value)}
                className={errors?.pricePerStock ? 'border-red-500' : ''}
              />
              {errors?.pricePerStock && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.pricePerStock[0]}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="availableStocks">Available Stocks *</Label>
              <Input
                id="availableStocks"
                type="number"
                min="0"
                max={value.totalStocks}
                placeholder="e.g., 800"
                value={value.availableStocks || ''}
                onChange={(e) => handleInputChange('availableStocks', e.target.value)}
                className={errors?.availableStocks ? 'border-red-500' : ''}
              />
              {errors?.availableStocks && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.availableStocks[0]}
                </p>
              )}
              {value.availableStocks > value.totalStocks && (
                <p className="text-sm text-amber-600">
                  Available stocks cannot exceed total stocks
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Minimum Investment */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="minimumInvestment">Minimum Investment (₹)</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <Info className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Minimum amount required to invest. Defaults to price per stock if not specified.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Input
            id="minimumInvestment"
            type="number"
            step="0.01"
            min={value.pricePerStock}
            placeholder={`Default: ₹${value.pricePerStock || 0}`}
            value={value.minimumInvestment || ''}
            onChange={(e) => handleInputChange('minimumInvestment', e.target.value)}
            className={errors?.minimumInvestment ? 'border-red-500' : ''}
          />
          {errors?.minimumInvestment && (
            <p className="text-sm text-red-500 flex items-center gap-1">
              <AlertCircle className="h-4 w-4" />
              {errors.minimumInvestment[0]}
            </p>
          )}
        </div>

        {/* Calculated Values */}
        {(value.totalStocks > 0 && value.pricePerStock > 0) && (
          <div className="space-y-3 p-4 bg-muted/50 rounded-lg">
            <h4 className="font-medium text-sm">Calculated Values</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <div className="text-center">
                <Badge variant="secondary" className="w-full justify-center">
                  Total Value
                </Badge>
                <p className="text-sm font-medium mt-1">
                  ₹{totalPropertyValue.toLocaleString('en-IN')}
                </p>
              </div>
              
              <div className="text-center">
                <Badge variant="secondary" className="w-full justify-center">
                  Min Investment
                </Badge>
                <p className="text-sm font-medium mt-1">
                  ₹{minimumInvestmentAmount.toLocaleString('en-IN')}
                </p>
              </div>
              
              <div className="text-center">
                <Badge variant="secondary" className="w-full justify-center">
                  Max Investment
                </Badge>
                <p className="text-sm font-medium mt-1">
                  ₹{maxPossibleInvestment.toLocaleString('en-IN')}
                </p>
              </div>
              
              <div className="text-center">
                <Badge variant="secondary" className="w-full justify-center">
                  Sold Percentage
                </Badge>
                <p className="text-sm font-medium mt-1">
                  {value.totalStocks > 0 ? (((value.totalStocks - value.availableStocks) / value.totalStocks) * 100).toFixed(1) : 0}%
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
