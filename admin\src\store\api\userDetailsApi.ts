import { baseApi } from './baseApi'

// Types for user-specific data
export interface UserInvestment {
  _id: string
  propertyId: string
  propertyName: string
  location: string
  stocksOwned: number
  stockPrice: number
  totalInvestment: number
  currentValue: number
  returns: number
  roi: number
  purchaseDate: string
  status: 'active' | 'sold'
  expectedAnnualReturn: number
  propertyImage?: string
}

export interface UserTransaction {
  _id: string
  type: 'deposit' | 'withdrawal' | 'investment' | 'return' | 'refund'
  amount: number
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  paymentMethod?: string
  description: string
  createdAt: string
  updatedAt: string
  propertyId?: string
  propertyName?: string
  reference?: string
}

export interface UserActivity {
  _id: string
  action: string
  entity: string
  entityId?: string
  description: string
  ipAddress?: string
  userAgent?: string
  location?: string
  createdAt: string
  metadata?: any
}

export interface UserStats {
  totalInvestments: number
  totalReturns: number
  activeInvestments: number
  totalTransactions: number
  walletBalance: number
  portfolioValue: number
  roi: number
  joinDate: string
  lastActivity: string
}

// API endpoints for user details
export const userDetailsApi = baseApi.injectEndpoints({
  overrideExisting: true,
  endpoints: (builder) => ({
    // Get user investments
    getUserInvestments: builder.query<{
      success: boolean
      data: {
        investments: UserInvestment[]
        pagination: {
          currentPage: number
          totalPages: number
          totalItems: number
          itemsPerPage: number
        }
      }
    }, {
      userId: string
      page?: number
      limit?: number
      status?: string
      sortBy?: string
      sortOrder?: 'asc' | 'desc'
    }>({
      query: ({ userId, ...params }) => ({
        url: `/admin/users/${userId}/investments`,
        params,
      }),
      providesTags: (result, error, { userId }) => [
        { type: 'User', id: userId },
        'Investment'
      ],
    }),

    // Get user transactions
    getUserTransactions: builder.query<{
      success: boolean
      data: {
        transactions: UserTransaction[]
        pagination: {
          currentPage: number
          totalPages: number
          totalItems: number
          itemsPerPage: number
        }
      }
    }, {
      userId: string
      page?: number
      limit?: number
      type?: string
      status?: string
      startDate?: string
      endDate?: string
      sortBy?: string
      sortOrder?: 'asc' | 'desc'
    }>({
      query: ({ userId, ...params }) => ({
        url: `/admin/users/${userId}/transactions`,
        params,
      }),
      providesTags: (result, error, { userId }) => [
        { type: 'User', id: userId },
        'Transaction'
      ],
    }),

    // Get user activity log
    getUserActivity: builder.query<{
      success: boolean
      data: {
        activities: UserActivity[]
        pagination: {
          currentPage: number
          totalPages: number
          totalItems: number
          itemsPerPage: number
        }
      }
    }, {
      userId: string
      page?: number
      limit?: number
      action?: string
      entity?: string
      startDate?: string
      endDate?: string
      sortBy?: string
      sortOrder?: 'asc' | 'desc'
    }>({
      query: ({ userId, ...params }) => ({
        url: `/admin/users/${userId}/activity`,
        params,
      }),
      providesTags: (result, error, { userId }) => [
        { type: 'User', id: userId },
        'Activity'
      ],
    }),

    // Get user statistics
    getUserStats: builder.query<{
      success: boolean
      data: UserStats
    }, string>({
      query: (userId) => `/admin/users/${userId}/stats`,
      providesTags: (result, error, userId) => [
        { type: 'User', id: userId },
        'UserStats'
      ],
    }),

    // Get user portfolio performance
    getUserPortfolioPerformance: builder.query<{
      success: boolean
      data: {
        chartData: Array<{
          date: string
          value: number
          invested: number
          returns: number
        }>
        summary: {
          totalInvested: number
          currentValue: number
          totalReturns: number
          roi: number
          bestPerforming: UserInvestment
          worstPerforming: UserInvestment
        }
      }
    }, {
      userId: string
      period?: '1M' | '3M' | '6M' | '1Y' | 'ALL'
    }>({
      query: ({ userId, period = '1Y' }) => ({
        url: `/admin/users/${userId}/portfolio-performance`,
        params: { period },
      }),
      providesTags: (result, error, { userId }) => [
        { type: 'User', id: userId },
        'Portfolio'
      ],
    }),
  }),
})

export const {
  useGetUserInvestmentsQuery,
  useGetUserTransactionsQuery,
  useGetUserActivityQuery,
  useGetUserStatsQuery,
  useGetUserPortfolioPerformanceQuery,
} = userDetailsApi
