import { z } from 'zod'

// Property Type Enum
export const PropertyTypeEnum = z.enum([
  'residential',
  'commercial', 
  'industrial',
  'land',
  'mixed',
  'luxury',
  'eco_friendly'
])

// Construction Status Enum
export const ConstructionStatusEnum = z.enum([
  'planning',
  'foundation', 
  'structure',
  'finishing',
  'completed'
])

// Property Status Enum
export const PropertyStatusEnum = z.enum([
  'active',
  'inactive',
  'sold_out',
  'coming_soon'
])

// Location Schema
export const LocationSchema = z.object({
  address: z.string()
    .min(5, 'Address must be at least 5 characters')
    .max(200, 'Address cannot exceed 200 characters'),
  city: z.string()
    .min(2, 'City must be at least 2 characters')
    .max(100, 'City cannot exceed 100 characters'),
  state: z.string()
    .min(2, 'State must be at least 2 characters')
    .max(100, 'State cannot exceed 100 characters'),
  pincode: z.string()
    .regex(/^[0-9]{6}$/, 'Pincode must be 6 digits'),
  coordinates: z.object({
    latitude: z.number()
      .min(-90, 'Latitude must be between -90 and 90')
      .max(90, 'Latitude must be between -90 and 90'),
    longitude: z.number()
      .min(-180, 'Longitude must be between -180 and 180')
      .max(180, 'Longitude must be between -180 and 180')
  }).optional()
})

// Developer Schema
export const DeveloperSchema = z.object({
  name: z.string()
    .min(2, 'Developer name must be at least 2 characters')
    .max(100, 'Developer name cannot exceed 100 characters'),
  contact: z.string()
    .regex(/^[0-9]{10,15}$/, 'Contact must be 10-15 digits'),
  email: z.string()
    .email('Invalid email format'),
  experience: z.number()
    .min(0, 'Experience cannot be negative')
    .max(100, 'Experience cannot exceed 100 years')
    .optional()
})

// File Upload Schema
export const FileUploadSchema = z.object({
  key: z.string().min(1, 'File key is required'),
  name: z.string().min(1, 'File name is required'),
  type: z.string().min(1, 'File type is required'),
  url: z.string().url('Invalid file URL'),
  uploadedAt: z.date().optional()
})

// Property Form Schema
export const PropertyFormSchema = z.object({
  // Basic Information
  name: z.string()
    .min(3, 'Property name must be at least 3 characters')
    .max(255, 'Property name cannot exceed 255 characters'),
  description: z.string()
    .min(10, 'Description must be at least 10 characters')
    .max(2000, 'Description cannot exceed 2000 characters'),
  propertyType: PropertyTypeEnum,
  
  // Location
  location: LocationSchema,
  
  // Financial Details
  expectedReturns: z.number()
    .min(0, 'Expected returns cannot be negative')
    .max(100, 'Expected returns cannot exceed 100%'),
  maturityPeriodMonths: z.number()
    .min(1, 'Maturity period must be at least 1 month')
    .max(600, 'Maturity period cannot exceed 600 months'),
  
  // Stock Information
  totalStocks: z.number()
    .min(1, 'Total stocks must be at least 1'),
  pricePerStock: z.number()
    .min(0.01, 'Price per stock must be greater than 0'),
  availableStocks: z.number()
    .min(0, 'Available stocks cannot be negative'),
  minimumInvestment: z.number()
    .min(1, 'Minimum investment must be at least 1')
    .optional(),
  maximumInvestment: z.number()
    .min(1, 'Maximum investment must be at least 1')
    .optional(),

  // Stock Configuration
  stockPrefix: z.string()
    .min(1, 'Stock prefix is required')
    .max(10, 'Stock prefix cannot exceed 10 characters'),
  stockStartNumber: z.number()
    .min(1, 'Stock start number must be at least 1'),

  // Commission Configuration
  referralCommissionRate: z.number()
    .min(0, 'Referral commission rate cannot be negative')
    .max(100, 'Referral commission rate cannot exceed 100%'),
  salesCommissionRate: z.number()
    .min(0, 'Sales commission rate cannot be negative')
    .max(100, 'Sales commission rate cannot exceed 100%'),
  referralCommissionPerStock: z.number()
    .min(0, 'Referral commission per stock cannot be negative'),
  salesCommissionPerStock: z.number()
    .min(0, 'Sales commission per stock cannot be negative'),
  commissionType: z.enum(['percentage', 'fixed']),
  
  // Construction Details
  constructionStatus: ConstructionStatusEnum,
  launchDate: z.string()
    .min(1, 'Launch date is required'),
  expectedCompletion: z.string()
    .min(1, 'Expected completion date is required'),
  actualCompletion: z.string().optional(),
  constructionTimeline: z.string().optional(),
  
  // Owner and Developer Information
  ownerId: z.string().optional(),
  developerId: z.string()
    .min(1, 'Developer is required'),

  // Property Features
  amenities: z.array(z.string()).default([]),
  features: z.array(z.string()).default([]),
  specifications: z.record(z.any()).optional(),

  // Media Files
  images: z.array(FileUploadSchema).default([]),
  documents: z.array(FileUploadSchema).default([]),
  videos: z.array(FileUploadSchema).default([]),
  legalDocuments: z.array(z.string()).default([]),

  // Administrative
  status: PropertyStatusEnum.default('active'),
  featured: z.boolean().default(false),
  priorityOrder: z.number().default(0)
}).refine((data) => {
  // Validate that available stocks don't exceed total stocks
  return data.availableStocks <= data.totalStocks
}, {
  message: 'Available stocks cannot exceed total stocks',
  path: ['availableStocks']
}).refine((data) => {
  // Validate minimum investment is not greater than maximum investment
  if (data.minimumInvestment && data.maximumInvestment) {
    return data.minimumInvestment <= data.maximumInvestment
  }
  return true
}, {
  message: 'Minimum investment cannot be greater than maximum investment',
  path: ['minimumInvestment']
})

export type PropertyFormData = z.infer<typeof PropertyFormSchema>

// Validation helper functions
export const validatePropertyForm = (data: unknown) => {
  return PropertyFormSchema.safeParse(data)
}

export const getPropertyFormErrors = (data: unknown) => {
  const result = PropertyFormSchema.safeParse(data)
  if (!result.success) {
    return result.error.flatten()
  }
  return null
}
