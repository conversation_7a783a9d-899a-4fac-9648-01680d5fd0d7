import { z } from 'zod'

// Address Schema
export const AddressSchema = z.object({
  street: z.string()
    .min(5, 'Street address must be at least 5 characters')
    .max(200, 'Street address cannot exceed 200 characters'),
  city: z.string()
    .min(2, 'City must be at least 2 characters')
    .max(100, 'City cannot exceed 100 characters'),
  state: z.string()
    .min(2, 'State must be at least 2 characters')
    .max(100, 'State cannot exceed 100 characters'),
  country: z.string()
    .min(2, 'Country must be at least 2 characters')
    .max(100, 'Country cannot exceed 100 characters'),
  pincode: z.string()
    .regex(/^[0-9]{6}$/, 'Pincode must be 6 digits')
})

// Bank Details Schema
export const BankDetailsSchema = z.object({
  accountNumber: z.string()
    .min(8, 'Account number must be at least 8 characters')
    .max(20, 'Account number cannot exceed 20 characters'),
  ifscCode: z.string()
    .regex(/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Invalid IFSC code format'),
  bankName: z.string()
    .min(2, 'Bank name must be at least 2 characters')
    .max(100, 'Bank name cannot exceed 100 characters'),
  accountHolderName: z.string()
    .min(2, 'Account holder name must be at least 2 characters')
    .max(100, 'Account holder name cannot exceed 100 characters')
})

// Developer Portfolio Project Schema
export const PortfolioProjectSchema = z.object({
  projectName: z.string()
    .min(2, 'Project name must be at least 2 characters')
    .max(100, 'Project name cannot exceed 100 characters'),
  location: z.string()
    .min(2, 'Location must be at least 2 characters')
    .max(100, 'Location cannot exceed 100 characters'),
  completionYear: z.number()
    .min(1950, 'Completion year must be after 1950')
    .max(new Date().getFullYear() + 10, 'Completion year cannot be more than 10 years in future'),
  projectValue: z.number()
    .min(0, 'Project value cannot be negative'),
  description: z.string()
    .max(500, 'Description cannot exceed 500 characters')
    .optional()
})

// Developer Details Schema
export const DeveloperDetailsSchema = z.object({
  companyName: z.string()
    .min(2, 'Company name must be at least 2 characters')
    .max(100, 'Company name cannot exceed 100 characters')
    .optional(),
  experience: z.number()
    .min(0, 'Experience cannot be negative')
    .max(100, 'Experience cannot exceed 100 years'),
  completedProjects: z.number()
    .min(0, 'Completed projects cannot be negative'),
  ongoingProjects: z.number()
    .min(0, 'Ongoing projects cannot be negative'),
  specialization: z.array(z.enum([
    'Residential',
    'Commercial', 
    'Industrial',
    'Mixed-Use',
    'Infrastructure',
    'Hospitality',
    'Retail'
  ])).default([]),
  licenseNumber: z.string()
    .max(50, 'License number cannot exceed 50 characters')
    .optional(),
  certifications: z.array(z.string()).default([]),
  portfolio: z.array(PortfolioProjectSchema).default([])
})

// Document Schema
export const DocumentSchema = z.object({
  taxIdDocument: z.string().optional(),
  nationalIdDocument: z.string().optional(),
  passportDocument: z.string().optional(),
  bankStatement: z.string().optional(),
  ownershipProof: z.string().optional(),
  businessRegistrationDocument: z.string().optional(),
  otherDocuments: z.array(z.string()).default([])
})

// Owner/Developer Form Schema
export const OwnerFormSchema = z.object({
  // Personal Information
  firstName: z.string()
    .min(2, 'First name must be at least 2 characters')
    .max(100, 'First name cannot exceed 100 characters'),
  lastName: z.string()
    .min(2, 'Last name must be at least 2 characters')
    .max(100, 'Last name cannot exceed 100 characters'),
  email: z.string()
    .email('Invalid email format'),
  phone: z.string()
    .regex(/^[0-9]{10,15}$/, 'Phone number must be 10-15 digits'),
  dateOfBirth: z.string()
    .min(1, 'Date of birth is required'),
  
  // Address Information
  address: AddressSchema,
  
  // Role Information
  roles: z.object({
    isOwner: z.boolean().default(true),
    isDeveloper: z.boolean().default(false),
    isInvestor: z.boolean().default(false)
  }),
  
  // Business Information
  company: z.string()
    .max(100, 'Company name cannot exceed 100 characters')
    .optional(),
  businessType: z.string()
    .max(50, 'Business type cannot exceed 50 characters')
    .optional(),
  gstNumber: z.string()
    .regex(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, 'Invalid GST number format')
    .optional(),
  panNumber: z.string()
    .regex(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Invalid PAN number format')
    .optional(),
  
  // Bank Details
  bankDetails: BankDetailsSchema,
  
  // Developer Specific Details
  developerDetails: DeveloperDetailsSchema.optional(),
  
  // Documents
  documents: DocumentSchema.default({}),
  
  // Administrative
  notes: z.string()
    .max(1000, 'Notes cannot exceed 1000 characters')
    .optional(),
  status: z.enum(['active', 'inactive', 'pending']).default('active'),
  verificationStatus: z.enum(['pending', 'verified', 'rejected']).default('pending')
})

export type OwnerFormData = z.infer<typeof OwnerFormSchema>

// Validation helper functions
export const validateOwnerForm = (data: unknown) => {
  return OwnerFormSchema.safeParse(data)
}

export const getOwnerFormErrors = (data: unknown) => {
  const result = OwnerFormSchema.safeParse(data)
  if (!result.success) {
    return result.error.flatten()
  }
  return null
}

// Conditional validation for developer details
export const validateDeveloperDetails = (data: OwnerFormData) => {
  if (data.roles.isDeveloper && !data.developerDetails) {
    return {
      success: false,
      error: 'Developer details are required when developer role is selected'
    }
  }
  return { success: true }
}
