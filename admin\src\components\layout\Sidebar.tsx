'use client'

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useAppSelector } from '@/store'
import { selectUser } from '@/store/slices/authSlice'
import { UserRole } from '@/types'
import { cn } from '@/lib/utils'
import Logo from '@/components/ui/logo'
import {
  LayoutDashboard,
  Users,
  Building,
  TrendingUp,
  Target,
  DollarSign,
  Headphones,
  Settings,
  Plus,
  UserPlus,
  UserCheck,
  BarChart3,
  Shield,
  Bell,
  PieChart,
  CheckSquare,
  CalendarDays,
  UserCog,
  Award,
  Minus,
  Receipt,
  CreditCard,
  Sparkles
} from 'lucide-react'

interface SidebarProps {
  collapsed?: boolean
  onToggle?: () => void
}

export default function Sidebar({ collapsed = false, onToggle }: SidebarProps) {
  const pathname = usePathname()
  const user = useAppSelector((state) => selectUser(state as any))

  console.log('Sidebar rendering...', { collapsed, pathname, user })

  const menuSections = [
    {
      title: "Dashboard",
      items: [
        {
          id: "main-dashboard",
          label: "Overview",
          icon: LayoutDashboard,
          href: "/dashboard",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES, UserRole.USER]
        }
        
      ]
    },
    {
      title: "User Management",
      items: [
        {
          id: "users-overview",
          label: "All Users",
          icon: Users,
          href: "/users",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        },
        {
          id: "user-management-comprehensive",
          label: "User Management",
          icon: Users,
          href: "/user-management",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        },
        {
          id: "add-user",
          label: "Add User",
          icon: UserPlus,
          href: "/users/add",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        },
        {
          id: "role-management",
          label: "Role Management",
          icon: UserCheck,
          href: "/users/roles",
          roles: [UserRole.ADMIN]
        }
      ]
    },
    {
      title: "Property Management",
      items: [
        {
          id: "properties-overview",
          label: "All Properties",
          icon: Building,
          href: "/properties",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        },
        {
          id: "add-property",
          label: "Add Property (Basic)",
          icon: Plus,
          href: "/properties/add",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        },
        {
          id: "add-property-enhanced",
          label: "Add Property (Enhanced)",
          icon: Sparkles,
          href: "/properties/add-enhanced",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        },
        {
          id: "property-owners",
          label: "Property Owners",
          icon: UserCheck,
          href: "/property-owners",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        }
      ]
    },
    {
      title: "Stock Investments",
      items: [
        {
          id: "stocks-overview",
          label: "All Stocks",
          icon: TrendingUp,
          href: "/stocks",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        },
        {
          id: "create-stock",
          label: "Create Stock",
          icon: Plus,
          href: "/stocks/create",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        }
      ]
    },
    {
      title: "Lead Management",
      items: [
        {
          id: "leads-overview",
          label: "Lead Management",
          icon: Target,
          href: "/leads",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES]
        },
        {
          id: "sales-analytics",
          label: "Sales Analytics",
          icon: BarChart3,
          href: "/sales-analytics",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        }
      ]
    },
    {
      title: "Sales Management",
      items: [
        {
          id: "sales-team",
          label: "Sales Team",
          icon: UserCog,
          href: "/sales-team",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        },
        {
          id: "sales-tasks",
          label: "Sales Tasks",
          icon: CheckSquare,
          href: "/sales-tasks",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        },
        {
          id: "sales-calendar",
          label: "Sales Calendar",
          icon: CalendarDays,
          href: "/sales-calendar",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        },
        {
          id: "sales-targets",
          label: "Sales Targets",
          icon: Award,
          href: "/sales-targets",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        },
        {
          id: "commissions",
          label: "Commissions",
          icon: DollarSign,
          href: "/commissions",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        }
      ]
    },
    {
      title: "Financial Management",
      items: [
        {
          id: "finance-overview",
          label: "Financial Management",
          icon: DollarSign,
          href: "/finance",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        },
        {
          id: "add-funds",
          label: "Add Funds",
          icon: Plus,
          href: "/add-funds",
          roles: [UserRole.ADMIN]
        },
        {
          id: "deduct-funds",
          label: "Deduct Funds",
          icon: Minus,
          href: "/deduct-funds",
          roles: [UserRole.ADMIN]
        },
        {
          id: "admin-transactions",
          label: "All Transactions",
          icon: Receipt,
          href: "/admin-transactions",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        },
        {
          id: "withdrawal-requests",
          label: "Withdrawal Requests",
          icon: CreditCard,
          href: "/withdrawal-requests",
          roles: [UserRole.ADMIN]
        }
      ]
    },
    {
      title: "Support Management",
      items: [
        {
          id: "support-dashboard",
          label: "Support Management",
          icon: Headphones,
          href: "/support",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        }
      ]
    },
    {
      title: "System & Settings",
      items: [
        {
          id: "system-settings",
          label: "Settings Management",
          icon: Settings,
          href: "/settings",
          roles: [UserRole.ADMIN]
        }
      ]
    },
    {
      title: "Reports & Analytics",
      items: [
        {
          id: "analytics-dashboard",
          label: "Analytics Dashboard",
          icon: BarChart3,
          href: "/analytics",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        },
        {
          id: "user-reports",
          label: "User Reports",
          icon: Users,
          href: "/reports/users",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        },
        {
          id: "property-reports",
          label: "Property Reports",
          icon: Building,
          href: "/reports/properties",
          roles: [UserRole.ADMIN, UserRole.SUBADMIN]
        },
        {
          id: "financial-reports",
          label: "Financial Reports",
          icon: DollarSign,
          href: "/reports/financial",
          roles: [UserRole.ADMIN]
        },
       
      ]
    }
  ]

  // Filter menu sections based on user role
  const userRole = user?.role as UserRole || UserRole.USER
  const filteredSections = menuSections.map(section => ({
    ...section,
    items: section.items.filter(item => item.roles.includes(userRole))
  })).filter(section => section.items.length > 0)

  // Debug logging
  console.log('User:', user)
  console.log('User Role:', user?.role)
  console.log('UserRole enum:', userRole)
  console.log('Menu Sections:', menuSections.length)
  console.log('Filtered Sections:', filteredSections.length)

  // Use filtered sections based on user role
  const sectionsToShow = filteredSections

  const isActive = (href: string) => {
    if (!pathname) return false
    if (href === '/dashboard') {
      return pathname === '/dashboard'
    }
    return pathname.startsWith(href)
  }

  return (
    <div className={cn(
      "h-screen bg-white border-r border-sky-200 flex flex-col transition-all duration-300 ease-in-out shadow-lg",
      collapsed ? "w-20" : "w-72"
    )}>
      {/* Logo */}
      <div className="flex items-center space-x-3 p-4 border-b border-sky-200 flex-shrink-0 bg-gradient-to-r from-sky-50 to-sky-100">
        <Logo size={collapsed ? "lg" : "xl"} variant={collapsed ? "icon" : "full"} />
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-sky-300 scrollbar-track-sky-100">
        <div className="space-y-2">
          {sectionsToShow.length === 0 ? (
            <div className="text-center text-red-500 p-4">
              <p>No menu items found!</p>
              <p>User: {user?.firstName || 'Not logged in'}</p>
              <p>Role: {user?.role || 'No role'}</p>
            </div>
          ) : (
            sectionsToShow.map((section) => (
              <div key={section.title} className="space-y-1 mb-6">
                {!collapsed && (
                  <h3 className="px-3 py-2 text-xs font-semibold text-sky-600 uppercase tracking-wider">
                    {section.title}
                  </h3>
                )}

                {section.items.map((item) => (
                  <Link
                    key={item.id}
                    href={item.href || '#'}
                    className={cn(
                      "flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-200",
                      isActive(item.href || '')
                        ? "bg-sky-600 text-white shadow-md"
                        : "text-gray-700 hover:text-sky-600 hover:bg-sky-50"
                    )}
                  >
                    <item.icon className="h-5 w-5 flex-shrink-0" />
                    {!collapsed && (
                      <span className="flex-1 truncate">{item.label}</span>
                    )}
                  </Link>
                ))}
              </div>
            ))
          )}
        </div>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-sky-200 bg-gradient-to-r from-sky-50 to-sky-100">
        {!collapsed && (
          <div className="text-xs text-sky-600 text-center font-medium">
            © 2025 SGM. All rights reserved.
          </div>
        )}
      </div>
    </div>
  )
}
