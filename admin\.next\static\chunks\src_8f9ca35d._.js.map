{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Format currency\nexport function formatCurrency(amount: number, currency = \"₹\"): string {\n  return `${currency}${amount.toLocaleString('en-IN', { \n    minimumFractionDigits: 2, \n    maximumFractionDigits: 2 \n  })}`\n}\n\n// Format date\nexport function formatDate(date: string | Date, format = \"dd/MM/yyyy\"): string {\n  const d = new Date(date)\n  \n  if (format === \"dd/MM/yyyy\") {\n    return d.toLocaleDateString('en-GB')\n  }\n  \n  if (format === \"relative\") {\n    const now = new Date()\n    const diffInMs = now.getTime() - d.getTime()\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))\n    \n    if (diffInDays === 0) return \"Today\"\n    if (diffInDays === 1) return \"Yesterday\"\n    if (diffInDays < 7) return `${diffInDays} days ago`\n    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`\n    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`\n    return `${Math.floor(diffInDays / 365)} years ago`\n  }\n  \n  return d.toLocaleDateString()\n}\n\n// Truncate text\nexport function truncateText(text: string, length = 50): string {\n  if (text.length <= length) return text\n  return text.substring(0, length) + \"...\"\n}\n\n// Generate initials\nexport function getInitials(firstName?: string, lastName?: string, email?: string): string {\n  if (firstName && lastName) {\n    return `${firstName[0]}${lastName[0]}`.toUpperCase()\n  }\n  \n  if (firstName) {\n    return firstName.substring(0, 2).toUpperCase()\n  }\n  \n  if (email) {\n    return email.substring(0, 2).toUpperCase()\n  }\n  \n  return \"U\"\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n// Sleep function\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\n// Generate random ID\nexport function generateId(prefix = \"\"): string {\n  const timestamp = Date.now().toString(36)\n  const random = Math.random().toString(36).substring(2, 8)\n  return `${prefix}${timestamp}${random}`.toUpperCase()\n}\n\n// Validate email\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// Validate phone\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\n// Format phone number\nexport function formatPhone(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '')\n  \n  if (cleaned.length === 10) {\n    return `+91 ${cleaned.substring(0, 5)} ${cleaned.substring(5)}`\n  }\n  \n  if (cleaned.length === 12 && cleaned.startsWith('91')) {\n    return `+${cleaned.substring(0, 2)} ${cleaned.substring(2, 7)} ${cleaned.substring(7)}`\n  }\n  \n  return phone\n}\n\n// Calculate percentage\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0\n  return Math.round((value / total) * 100)\n}\n\n// Get status color\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    active: \"text-green-600 bg-green-100\",\n    inactive: \"text-gray-600 bg-gray-100\",\n    pending: \"text-yellow-600 bg-yellow-100\",\n    suspended: \"text-red-600 bg-red-100\",\n    completed: \"text-green-600 bg-green-100\",\n    failed: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    new: \"text-blue-600 bg-blue-100\",\n    contacted: \"text-purple-600 bg-purple-100\",\n    qualified: \"text-indigo-600 bg-indigo-100\",\n    converted: \"text-green-600 bg-green-100\",\n    lost: \"text-red-600 bg-red-100\",\n  }\n  \n  return statusColors[status.toLowerCase()] || \"text-gray-600 bg-gray-100\"\n}\n\n// Copy to clipboard\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text)\n    return true\n  } catch (error) {\n    console.error('Failed to copy to clipboard:', error)\n    return false\n  }\n}\n\n// Download file\nexport function downloadFile(data: any, filename: string, type = 'application/json'): void {\n  const blob = new Blob([typeof data === 'string' ? data : JSON.stringify(data, null, 2)], { type })\n  const url = URL.createObjectURL(blob)\n  const link = document.createElement('a')\n  link.href = url\n  link.download = filename\n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n  URL.revokeObjectURL(url)\n}\n\n// Format file size\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// Get file extension\nexport function getFileExtension(filename: string): string {\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)\n}\n\n// Check if mobile device\nexport function isMobile(): boolean {\n  if (typeof window === 'undefined') return false\n  return window.innerWidth < 768\n}\n\n// Scroll to element\nexport function scrollToElement(elementId: string, offset = 0): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const top = element.offsetTop - offset\n    window.scrollTo({ top, behavior: 'smooth' })\n  }\n}\n\n// Local storage helpers\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    if (typeof window === 'undefined') return defaultValue || null\n    \n    try {\n      const item = localStorage.getItem(key)\n      return item ? JSON.parse(item) : defaultValue || null\n    } catch (error) {\n      console.error(`Error getting item from localStorage:`, error)\n      return defaultValue || null\n    }\n  },\n  \n  set: <T>(key: string, value: T): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.setItem(key, JSON.stringify(value))\n    } catch (error) {\n      console.error(`Error setting item in localStorage:`, error)\n    }\n  },\n  \n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.removeItem(key)\n    } catch (error) {\n      console.error(`Error removing item from localStorage:`, error)\n    }\n  },\n  \n  clear: (): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.clear()\n    } catch (error) {\n      console.error(`Error clearing localStorage:`, error)\n    }\n  }\n}\n\n// Format time\nexport function formatTime(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc,EAAE,WAAW,GAAG;IAC3D,OAAO,GAAG,WAAW,OAAO,cAAc,CAAC,SAAS;QAClD,uBAAuB;QACvB,uBAAuB;IACzB,IAAI;AACN;AAGO,SAAS,WAAW,IAAmB,EAAE,SAAS,YAAY;IACnE,MAAM,IAAI,IAAI,KAAK;IAEnB,IAAI,WAAW,cAAc;QAC3B,OAAO,EAAE,kBAAkB,CAAC;IAC9B;IAEA,IAAI,WAAW,YAAY;QACzB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,EAAE,OAAO;QAC1C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE7D,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,SAAS,CAAC;QACnD,IAAI,aAAa,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC;QACrE,IAAI,aAAa,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI,WAAW,CAAC;QACxE,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,KAAK,UAAU,CAAC;IACpD;IAEA,OAAO,EAAE,kBAAkB;AAC7B;AAGO,SAAS,aAAa,IAAY,EAAE,SAAS,EAAE;IACpD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,SAAS,CAAC,GAAG,UAAU;AACrC;AAGO,SAAS,YAAY,SAAkB,EAAE,QAAiB,EAAE,KAAc;IAC/E,IAAI,aAAa,UAAU;QACzB,OAAO,GAAG,SAAS,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW;IACpD;IAEA,IAAI,WAAW;QACb,OAAO,UAAU,SAAS,CAAC,GAAG,GAAG,WAAW;IAC9C;IAEA,IAAI,OAAO;QACT,OAAO,MAAM,SAAS,CAAC,GAAG,GAAG,WAAW;IAC1C;IAEA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,SAAS,WAAW,SAAS,EAAE;IACpC,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,CAAC;IACtC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACvD,OAAO,GAAG,SAAS,YAAY,QAAQ,CAAC,WAAW;AACrD;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAGO,SAAS,YAAY,KAAa;IACvC,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IAErC,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,CAAC,IAAI,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACjE;IAEA,IAAI,QAAQ,MAAM,KAAK,MAAM,QAAQ,UAAU,CAAC,OAAO;QACrD,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACzF;IAEA,OAAO;AACT;AAGO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAGO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,QAAQ;QACR,UAAU;QACV,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;QACR,WAAW;QACX,KAAK;QACL,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;IACR;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAGO,SAAS,aAAa,IAAS,EAAE,QAAgB,EAAE,OAAO,kBAAkB;IACjF,MAAM,OAAO,IAAI,KAAK;QAAC,OAAO,SAAS,WAAW,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM;KAAG,EAAE;QAAE;IAAK;IAChG,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAGO,SAAS;IACd,uCAAmC;;IAAW;IAC9C,OAAO,OAAO,UAAU,GAAG;AAC7B;AAGO,SAAS,gBAAgB,SAAiB,EAAE,SAAS,CAAC;IAC3D,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,MAAM,QAAQ,SAAS,GAAG;QAChC,OAAO,QAAQ,CAAC;YAAE;YAAK,UAAU;QAAS;IAC5C;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,uCAAmC;;QAA0B;QAE7D,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ,gBAAgB;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qCAAqC,CAAC,EAAE;YACvD,OAAO,gBAAgB;QACzB;IACF;IAEA,KAAK,CAAI,KAAa;QACpB,uCAAmC;;QAAK;QAExC,IAAI;YACF,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,CAAC,EAAE;QACvD;IACF;IAEA,QAAQ,CAAC;QACP,uCAAmC;;QAAK;QAExC,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sCAAsC,CAAC,EAAE;QAC1D;IACF;IAEA,OAAO;QACL,uCAAmC;;QAAK;QAExC,IAAI;YACF,aAAa,KAAK;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAChD;IACF;AACF;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"card-base text-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6BACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"btn-primary\",\n        destructive: \"bg-red-500 text-white hover:bg-red-600\",\n        outline: \"btn-outline\",\n        secondary: \"btn-secondary\",\n        ghost: \"hover:bg-primary-100 text-primary\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,mQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"input-field flex h-10 w-full rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,4TAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2RACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,iRAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,iRAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 491, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kRAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kRAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kRAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4TAAC,kRAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,4TAAC,kRAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,4TAAC,2SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,uSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,kRAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,2SAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,kRAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,4TAAC,kRAAA,CAAA,SAAsB;kBACrB,cAAA,4TAAC,kRAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,4TAAC;;;;;8BACD,4TAAC,kRAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,4TAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,kRAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4TAAC,kRAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,4TAAC;gBAAK,WAAU;0BACd,cAAA,4TAAC,kRAAA,CAAA,gBAA6B;8BAC5B,cAAA,4TAAC,2RAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,4TAAC,kRAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kRAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,kRAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/logo.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'full' | 'icon' | 'text'\n  className?: string\n}\n\nconst Logo: React.FC<LogoProps> = ({ \n  size = 'md', \n  variant = 'full', \n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'h-6 w-6',\n    md: 'h-8 w-8', \n    lg: 'h-10 w-10',\n    xl: 'h-12 w-12'\n  }\n\n  const textSizeClasses = {\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-2xl', \n    xl: 'text-3xl'\n  }\n\n  const LogoIcon = () => (\n    <div className={`${sizeClasses[size]} bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center shadow-lg ${className}`}>\n      <div className=\"relative\">\n        {/* S */}\n        <div className=\"absolute -left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5 ml-1\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n        </div>\n        \n        {/* G */}\n        <div className=\"absolute left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-4 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"absolute right-0 top-2 w-1 h-2 bg-white rounded-full\"></div>\n          <div className=\"absolute right-0 top-3 w-1.5 h-1 bg-white rounded-full\"></div>\n        </div>\n        \n        {/* M */}\n        <div className=\"absolute left-4 top-0\">\n          <div className=\"w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-0.5 top-1 w-1 h-1 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1 top-0 w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1.5 top-0 w-1 h-5 bg-white rounded-full\"></div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const LogoText = () => (\n    <span className={`font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent ${textSizeClasses[size]} ${className}`}>\n      SGM\n    </span>\n  )\n\n  if (variant === 'icon') {\n    return <LogoIcon />\n  }\n\n  if (variant === 'text') {\n    return <LogoText />\n  }\n\n  return (\n    <div className={`flex items-center gap-3 ${className}`}>\n      <LogoIcon />\n      <LogoText />\n    </div>\n  )\n}\n\nexport default Logo\n"], "names": [], "mappings": ";;;;;AAQA,MAAM,OAA4B,CAAC,EACjC,OAAO,IAAI,EACX,UAAU,MAAM,EAChB,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,WAAW,kBACf,4TAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,mGAAmG,EAAE,WAAW;sBACnJ,cAAA,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMvB,MAAM,WAAW,kBACf,4TAAC;YAAK,WAAW,CAAC,mFAAmF,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;sBAAE;;;;;;IAK/I,IAAI,YAAY,QAAQ;QACtB,qBAAO,4TAAC;;;;;IACV;IAEA,IAAI,YAAY,QAAQ;QACtB,qBAAO,4TAAC;;;;;IACV;IAEA,qBACE,4TAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;0BACpD,4TAAC;;;;;0BACD,4TAAC;;;;;;;;;;;AAGP;KAvEM;uCAyES", "debugId": null}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAppSelector } from '@/store'\nimport { selectUser } from '@/store/slices/authSlice'\nimport { UserRole } from '@/types'\nimport { cn } from '@/lib/utils'\nimport Logo from '@/components/ui/logo'\nimport {\n  LayoutDashboard,\n  Users,\n  Building,\n  TrendingUp,\n  Target,\n  DollarSign,\n  Headphones,\n  Settings,\n  Plus,\n  UserPlus,\n  UserCheck,\n  BarChart3,\n  Shield,\n  Bell,\n  PieChart,\n  CheckSquare,\n  CalendarDays,\n  UserCog,\n  Award,\n  Minus,\n  Receipt,\n  CreditCard\n} from 'lucide-react'\n\ninterface SidebarProps {\n  collapsed?: boolean\n  onToggle?: () => void\n}\n\nexport default function Sidebar({ collapsed = false, onToggle }: SidebarProps) {\n  const pathname = usePathname()\n  const user = useAppSelector((state) => selectUser(state as any))\n\n  console.log('Sidebar rendering...', { collapsed, pathname, user })\n\n  const menuSections = [\n    {\n      title: \"Dashboard\",\n      items: [\n        {\n          id: \"main-dashboard\",\n          label: \"Overview\",\n          icon: LayoutDashboard,\n          href: \"/dashboard\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES, UserRole.USER]\n        }\n        \n      ]\n    },\n    {\n      title: \"User Management\",\n      items: [\n        {\n          id: \"users-overview\",\n          label: \"All Users\",\n          icon: Users,\n          href: \"/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-management-comprehensive\",\n          label: \"User Management\",\n          icon: Users,\n          href: \"/user-management\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-user\",\n          label: \"Add User\",\n          icon: UserPlus,\n          href: \"/users/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"role-management\",\n          label: \"Role Management\",\n          icon: UserCheck,\n          href: \"/users/roles\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Property Management\",\n      items: [\n        {\n          id: \"properties-overview\",\n          label: \"All Properties\",\n          icon: Building,\n          href: \"/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-property\",\n          label: \"Add Property\",\n          icon: Plus,\n          href: \"/properties/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-owners\",\n          label: \"Property Owners\",\n          icon: UserCheck,\n          href: \"/property-owners\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Stock Investments\",\n      items: [\n        {\n          id: \"stocks-overview\",\n          label: \"All Stocks\",\n          icon: TrendingUp,\n          href: \"/stocks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"create-stock\",\n          label: \"Create Stock\",\n          icon: Plus,\n          href: \"/stocks/create\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Lead Management\",\n      items: [\n        {\n          id: \"leads-overview\",\n          label: \"Lead Management\",\n          icon: Target,\n          href: \"/leads\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES]\n        },\n        {\n          id: \"sales-analytics\",\n          label: \"Sales Analytics\",\n          icon: BarChart3,\n          href: \"/sales-analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Sales Management\",\n      items: [\n        {\n          id: \"sales-team\",\n          label: \"Sales Team\",\n          icon: UserCog,\n          href: \"/sales-team\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-tasks\",\n          label: \"Sales Tasks\",\n          icon: CheckSquare,\n          href: \"/sales-tasks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-calendar\",\n          label: \"Sales Calendar\",\n          icon: CalendarDays,\n          href: \"/sales-calendar\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-targets\",\n          label: \"Sales Targets\",\n          icon: Award,\n          href: \"/sales-targets\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"commissions\",\n          label: \"Commissions\",\n          icon: DollarSign,\n          href: \"/commissions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Financial Management\",\n      items: [\n        {\n          id: \"finance-overview\",\n          label: \"Financial Management\",\n          icon: DollarSign,\n          href: \"/finance\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-funds\",\n          label: \"Add Funds\",\n          icon: Plus,\n          href: \"/add-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"deduct-funds\",\n          label: \"Deduct Funds\",\n          icon: Minus,\n          href: \"/deduct-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"admin-transactions\",\n          label: \"All Transactions\",\n          icon: Receipt,\n          href: \"/admin-transactions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"withdrawal-requests\",\n          label: \"Withdrawal Requests\",\n          icon: CreditCard,\n          href: \"/withdrawal-requests\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Support Management\",\n      items: [\n        {\n          id: \"support-dashboard\",\n          label: \"Support Management\",\n          icon: Headphones,\n          href: \"/support\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"System & Settings\",\n      items: [\n        {\n          id: \"system-settings\",\n          label: \"Settings Management\",\n          icon: Settings,\n          href: \"/settings\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Reports & Analytics\",\n      items: [\n        {\n          id: \"analytics-dashboard\",\n          label: \"Analytics Dashboard\",\n          icon: BarChart3,\n          href: \"/analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-reports\",\n          label: \"User Reports\",\n          icon: Users,\n          href: \"/reports/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-reports\",\n          label: \"Property Reports\",\n          icon: Building,\n          href: \"/reports/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"financial-reports\",\n          label: \"Financial Reports\",\n          icon: DollarSign,\n          href: \"/reports/financial\",\n          roles: [UserRole.ADMIN]\n        },\n       \n      ]\n    }\n  ]\n\n  // Filter menu sections based on user role\n  const userRole = user?.role as UserRole || UserRole.USER\n  const filteredSections = menuSections.map(section => ({\n    ...section,\n    items: section.items.filter(item => item.roles.includes(userRole))\n  })).filter(section => section.items.length > 0)\n\n  // Debug logging\n  console.log('User:', user)\n  console.log('User Role:', user?.role)\n  console.log('UserRole enum:', userRole)\n  console.log('Menu Sections:', menuSections.length)\n  console.log('Filtered Sections:', filteredSections.length)\n\n  // Use filtered sections based on user role\n  const sectionsToShow = filteredSections\n\n  const isActive = (href: string) => {\n    if (!pathname) return false\n    if (href === '/dashboard') {\n      return pathname === '/dashboard'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <div className={cn(\n      \"h-screen bg-white border-r border-sky-200 flex flex-col transition-all duration-300 ease-in-out shadow-lg\",\n      collapsed ? \"w-20\" : \"w-72\"\n    )}>\n      {/* Logo */}\n      <div className=\"flex items-center space-x-3 p-4 border-b border-sky-200 flex-shrink-0 bg-gradient-to-r from-sky-50 to-sky-100\">\n        <Logo size={collapsed ? \"lg\" : \"xl\"} variant={collapsed ? \"icon\" : \"full\"} />\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-sky-300 scrollbar-track-sky-100\">\n        <div className=\"space-y-2\">\n          {sectionsToShow.length === 0 ? (\n            <div className=\"text-center text-red-500 p-4\">\n              <p>No menu items found!</p>\n              <p>User: {user?.firstName || 'Not logged in'}</p>\n              <p>Role: {user?.role || 'No role'}</p>\n            </div>\n          ) : (\n            sectionsToShow.map((section) => (\n              <div key={section.title} className=\"space-y-1 mb-6\">\n                {!collapsed && (\n                  <h3 className=\"px-3 py-2 text-xs font-semibold text-sky-600 uppercase tracking-wider\">\n                    {section.title}\n                  </h3>\n                )}\n\n                {section.items.map((item) => (\n                  <Link\n                    key={item.id}\n                    href={item.href || '#'}\n                    className={cn(\n                      \"flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-200\",\n                      isActive(item.href || '')\n                        ? \"bg-sky-600 text-white shadow-md\"\n                        : \"text-gray-700 hover:text-sky-600 hover:bg-sky-50\"\n                    )}\n                  >\n                    <item.icon className=\"h-5 w-5 flex-shrink-0\" />\n                    {!collapsed && (\n                      <span className=\"flex-1 truncate\">{item.label}</span>\n                    )}\n                  </Link>\n                ))}\n              </div>\n            ))\n          )}\n        </div>\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-sky-200 bg-gradient-to-r from-sky-50 to-sky-100\">\n        {!collapsed && (\n          <div className=\"text-xs text-sky-600 text-center font-medium\">\n            © 2025 SGM. All rights reserved.\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;AAwCe,SAAS,QAAQ,EAAE,YAAY,KAAK,EAAE,QAAQ,EAAgB;;IAC3E,MAAM,WAAW,CAAA,GAAA,oQAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,OAAO,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;wCAAE,CAAC,QAAU,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;;IAElD,QAAQ,GAAG,CAAC,wBAAwB;QAAE;QAAW;QAAU;IAAK;IAEhE,MAAM,eAAe;QACnB;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,mTAAA,CAAA,kBAAe;oBACrB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,IAAI;qBAAC;gBAC3E;aAED;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,qSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,uSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,iSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,yRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,uSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,yRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,6RAAA,CAAA,SAAM;oBACZ,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBAC5D;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,mSAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kTAAA,CAAA,cAAW;oBACjB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,6SAAA,CAAA,eAAY;oBAClB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,yRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,+RAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,qSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,iSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,iSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aAED;QACH;KACD;IAED,0CAA0C;IAC1C,MAAM,WAAW,MAAM,QAAoB,wHAAA,CAAA,WAAQ,CAAC,IAAI;IACxD,MAAM,mBAAmB,aAAa,GAAG,CAAC,CAAA,UAAW,CAAC;YACpD,GAAG,OAAO;YACV,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC;QAC1D,CAAC,GAAG,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,CAAC,MAAM,GAAG;IAE7C,gBAAgB;IAChB,QAAQ,GAAG,CAAC,SAAS;IACrB,QAAQ,GAAG,CAAC,cAAc,MAAM;IAChC,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,QAAQ,GAAG,CAAC,kBAAkB,aAAa,MAAM;IACjD,QAAQ,GAAG,CAAC,sBAAsB,iBAAiB,MAAM;IAEzD,2CAA2C;IAC3C,MAAM,iBAAiB;IAEvB,MAAM,WAAW,CAAC;QAChB,IAAI,CAAC,UAAU,OAAO;QACtB,IAAI,SAAS,cAAc;YACzB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,4TAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,6GACA,YAAY,SAAS;;0BAGrB,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC,mIAAA,CAAA,UAAI;oBAAC,MAAM,YAAY,OAAO;oBAAM,SAAS,YAAY,SAAS;;;;;;;;;;;0BAIrE,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;8BACZ,eAAe,MAAM,KAAK,kBACzB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;0CAAE;;;;;;0CACH,4TAAC;;oCAAE;oCAAO,MAAM,aAAa;;;;;;;0CAC7B,4TAAC;;oCAAE;oCAAO,MAAM,QAAQ;;;;;;;;;;;;+BAG1B,eAAe,GAAG,CAAC,CAAC,wBAClB,4TAAC;4BAAwB,WAAU;;gCAChC,CAAC,2BACA,4TAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;gCAIjB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,4TAAC,8RAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI,IAAI;wCACnB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA,SAAS,KAAK,IAAI,IAAI,MAClB,oCACA;;0DAGN,4TAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,CAAC,2BACA,4TAAC;gDAAK,WAAU;0DAAmB,KAAK,KAAK;;;;;;;uCAX1C,KAAK,EAAE;;;;;;2BATR,QAAQ,KAAK;;;;;;;;;;;;;;;0BA+B/B,4TAAC;gBAAI,WAAU;0BACZ,CAAC,2BACA,4TAAC;oBAAI,WAAU;8BAA+C;;;;;;;;;;;;;;;;;AAOxE;GAvVwB;;QACL,oQAAA,CAAA,cAAW;QACf,wHAAA,CAAA,iBAAc;;;KAFL", "debugId": null}}, {"offset": {"line": 1506, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAppSelector, useAppDispatch } from '@/store'\nimport { selectUser, logoutAsync } from '@/store/slices/authSlice'\nimport { Button } from '@/components/ui/button'\nimport Sidebar from './Sidebar'\nimport { Menu, Bell, Search, LogOut, User, Settings, HelpCircle, ChevronDown } from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)\n  const [userMenuOpen, setUserMenuOpen] = useState(false)\n  const router = useRouter()\n  const dispatch = useAppDispatch()\n  const user = useAppSelector(selectUser)\n\n  const handleLogout = async () => {\n    await dispatch(logoutAsync())\n    router.push('/login')\n  }\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed)\n  }\n\n  return (\n    <div className=\"h-screen bg-white overflow-hidden\">\n      <div className=\"flex h-full\">\n        {/* Sidebar */}\n        <Sidebar collapsed={sidebarCollapsed} onToggle={toggleSidebar} />\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col h-full overflow-hidden\">\n          {/* Header */}\n          <header className=\"bg-gradient-to-r from-sky-500 to-sky-600 shadow-lg border-b border-sky-700 flex-shrink-0 z-40\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              <div className=\"flex justify-between items-center py-4\">\n                {/* Left side */}\n                <div className=\"flex items-center space-x-4\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={toggleSidebar}\n                    className=\"lg:hidden hover:bg-white/20 text-white\"\n                  >\n                    <Menu className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Search Bar */}\n                  <div className=\"hidden md:flex items-center space-x-2 bg-white/20 rounded-lg px-4 py-2.5 min-w-[350px] border border-white/30\">\n                    <Search className=\"h-4 w-4 text-white/70\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search users, properties, transactions...\"\n                      className=\"bg-transparent border-none outline-none flex-1 text-sm placeholder:text-white/70 text-white\"\n                    />\n                    <kbd className=\"hidden lg:inline-flex items-center px-2 py-1 text-xs font-medium text-sky-600 bg-white rounded border border-white/30\">\n                      ⌘K\n                    </kbd>\n                  </div>\n                </div>\n\n                {/* Right side */}\n                <div className=\"flex items-center space-x-3\">\n                  {/* Quick Actions */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"hover:bg-white/20 text-white\">\n                    <HelpCircle className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Notifications */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"relative hover:bg-white/20 text-white\">\n                    <Bell className=\"h-5 w-5\" />\n                    <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-yellow-500 text-black text-xs rounded-full flex items-center justify-center font-semibold\">\n                      5\n                    </span>\n                  </Button>\n\n                  {/* User Menu */}\n                  <div className=\"relative\">\n                    <Button\n                      variant=\"ghost\"\n                      onClick={() => setUserMenuOpen(!userMenuOpen)}\n                      className=\"flex items-center space-x-3 px-3 py-2 hover:bg-white/20 rounded-lg text-white\"\n                    >\n                      <div className=\"hidden sm:block text-right\">\n                        <p className=\"text-sm font-medium text-white\">\n                          {user?.firstName} {user?.lastName}\n                        </p>\n                        <div className=\"flex items-center justify-end space-x-1\">\n                          <div className=\"bg-yellow-500 px-2 py-0.5 rounded-full\">\n                            <span className=\"text-xs font-medium text-black capitalize\">\n                              {user?.role}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"h-10 w-10 bg-white/20 rounded-full flex items-center justify-center shadow-sm border border-white/30\">\n                        <User className=\"h-5 w-5 text-white\" />\n                      </div>\n\n                      <ChevronDown className=\"h-4 w-4 text-white/70\" />\n                    </Button>\n\n                    {/* User Dropdown Menu */}\n                    {userMenuOpen && (\n                      <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-sky-200 py-2 z-50\">\n                        <div className=\"px-4 py-2 border-b border-sky-100\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {user?.firstName} {user?.lastName}\n                          </p>\n                          <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                        </div>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <User className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Profile\n                        </Button>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <Settings className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Settings\n                        </Button>\n\n                        <div className=\"border-t border-sky-100 mt-2 pt-2\">\n                          <Button\n                            variant=\"ghost\"\n                            className=\"w-full justify-start px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                            onClick={handleLogout}\n                          >\n                            <LogOut className=\"h-4 w-4 mr-3\" />\n                            Logout\n                          </Button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </header>\n\n          {/* Main Content Area */}\n          <main className=\"flex-1 overflow-y-auto bg-gray-50\">\n            {children}\n          </main>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAce,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,OAAO,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,sIAAA,CAAA,aAAU;IAEtC,MAAM,eAAe;QACnB,MAAM,SAAS,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB;QACpB,oBAAoB,CAAC;IACvB;IAEA,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC;YAAI,WAAU;;8BAEb,4TAAC,0IAAA,CAAA,UAAO;oBAAC,WAAW;oBAAkB,UAAU;;;;;;8BAGhD,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC;4BAAO,WAAU;sCAChB,cAAA,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;sDAEb,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DAEV,cAAA,4TAAC,yRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAIlB,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,6RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,4TAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;sEAEZ,4TAAC;4DAAI,WAAU;sEAAwH;;;;;;;;;;;;;;;;;;sDAO3I,4TAAC;4CAAI,WAAU;;8DAEb,4TAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;8DAC5C,cAAA,4TAAC,qTAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAIxB,4TAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;;sEAC5C,4TAAC,yRAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,4TAAC;4DAAK,WAAU;sEAAgI;;;;;;;;;;;;8DAMlJ,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS,IAAM,gBAAgB,CAAC;4DAChC,WAAU;;8EAEV,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,4TAAC;4EAAI,WAAU;sFACb,cAAA,4TAAC;gFAAI,WAAU;0FACb,cAAA,4TAAC;oFAAK,WAAU;8FACb,MAAM;;;;;;;;;;;;;;;;;;;;;;8EAMf,4TAAC;oEAAI,WAAU;8EACb,cAAA,4TAAC,yRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAGlB,4TAAC,2SAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;;wDAIxB,8BACC,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,4TAAC;4EAAE,WAAU;sFAAyB,MAAM;;;;;;;;;;;;8EAG9C,4TAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,4TAAC,yRAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIhD,4TAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,4TAAC,iSAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIpD,4TAAC;oEAAI,WAAU;8EACb,cAAA,4TAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,WAAU;wEACV,SAAS;;0FAET,4TAAC,iSAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAarD,4TAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;;;AAMb;GArJwB;;QAGP,oQAAA,CAAA,YAAS;QACP,wHAAA,CAAA,iBAAc;QAClB,wHAAA,CAAA,iBAAc;;;KALL", "debugId": null}}, {"offset": {"line": 1928, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/app/stocks/create/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { toast } from 'sonner'\nimport DashboardLayout from '@/components/layout/DashboardLayout'\nimport { useRouter } from 'next/navigation'\nimport { useGetPropertiesQuery } from '@/store/api/propertiesApi'\nimport { useCreateStockMutation } from '@/store/api/propertyStocksApi'\nimport {\n  TrendingUp,\n  Save,\n  ArrowLeft,\n  DollarSign,\n  Plus,\n  X,\n  AlertCircle,\n  Building,\n  Calculator,\n  Target,\n  Loader2\n} from 'lucide-react'\n\ninterface StockTier {\n  id: number\n  name: string\n  pricePerStock: string\n  stockCount: string\n  minInvestment: string\n  maxInvestment: string\n  description: string\n  benefits: string[]\n}\n\ninterface StockFormData {\n  propertyId: string\n  totalStocks: string\n  basePrice: string\n  expectedROI: string\n  minimumInvestment: string\n  stockTiers: StockTier[]\n  riskLevel: string\n  investmentPeriod: string\n  dividendFrequency: string\n  description: string\n}\n\nexport default function CreateStockPage() {\n  const router = useRouter()\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [errors, setErrors] = useState<Record<string, string>>({})\n\n  // Fetch real properties data\n  const { data: propertiesData, isLoading: isLoadingProperties } = useGetPropertiesQuery({\n    page: 1,\n    limit: 100,\n    status: 'active'\n  })\n\n  // RTK Query mutation\n  const [createStock] = useCreateStockMutation()\n  \n  const [formData, setFormData] = useState<StockFormData>({\n    propertyId: '',\n    totalStocks: '',\n    basePrice: '',\n    expectedROI: '',\n    minimumInvestment: '',\n    stockTiers: [\n      {\n        id: 1,\n        name: 'Basic',\n        pricePerStock: '',\n        stockCount: '',\n        minInvestment: '',\n        maxInvestment: '',\n        description: 'Entry level investment with standard returns',\n        benefits: ['Standard Returns', 'Basic Support']\n      },\n      {\n        id: 2,\n        name: 'Premium',\n        pricePerStock: '',\n        stockCount: '',\n        minInvestment: '',\n        maxInvestment: '',\n        description: 'Higher investment tier with enhanced benefits',\n        benefits: ['Higher Returns', 'Priority Support', 'Exclusive Updates']\n      }\n    ],\n    riskLevel: 'medium',\n    investmentPeriod: '12',\n    dividendFrequency: 'quarterly',\n    description: ''\n  })\n\n  // Use real properties data\n  const realProperties = propertiesData?.data || []\n  const availableProperties = realProperties.map((property: any) => ({\n    id: property._id || property.id,\n    name: property.name,\n    location: `${property.location.city}, ${property.location.state}`,\n    propertyType: property.propertyType,\n    status: property.status\n  }))\n\n  const riskLevels = [\n    { value: 'low', label: 'Low Risk', description: 'Stable returns with minimal risk' },\n    { value: 'medium', label: 'Medium Risk', description: 'Balanced risk-return profile' },\n    { value: 'high', label: 'High Risk', description: 'Higher potential returns with increased risk' }\n  ]\n\n  const dividendFrequencies = [\n    { value: 'monthly', label: 'Monthly' },\n    { value: 'quarterly', label: 'Quarterly' },\n    { value: 'half-yearly', label: 'Half-Yearly' },\n    { value: 'yearly', label: 'Yearly' }\n  ]\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n    \n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }))\n    }\n  }\n\n  const addStockTier = () => {\n    const newTier: StockTier = {\n      id: Date.now(),\n      name: '',\n      pricePerStock: '',\n      stockCount: '',\n      minInvestment: '',\n      maxInvestment: '',\n      description: '',\n      benefits: []\n    }\n    setFormData(prev => ({\n      ...prev,\n      stockTiers: [...prev.stockTiers, newTier]\n    }))\n  }\n\n  const removeStockTier = (id: number) => {\n    setFormData(prev => ({\n      ...prev,\n      stockTiers: prev.stockTiers.filter(tier => tier.id !== id)\n    }))\n  }\n\n  const updateStockTier = (id: number, field: string, value: string | string[]) => {\n    setFormData(prev => ({\n      ...prev,\n      stockTiers: prev.stockTiers.map(tier => \n        tier.id === id ? { ...tier, [field]: value } : tier\n      )\n    }))\n  }\n\n  // Calculate total stocks and value from tiers\n  const totalStocksFromTiers = formData.stockTiers.reduce((total, tier) => \n    total + parseInt(tier.stockCount || '0'), 0\n  )\n  \n  const totalValueFromTiers = formData.stockTiers.reduce((total, tier) => \n    total + (parseInt(tier.stockCount || '0') * parseInt(tier.pricePerStock || '0')), 0\n  )\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {}\n\n    if (!formData.propertyId) newErrors.propertyId = 'Property selection is required'\n    if (!formData.totalStocks) newErrors.totalStocks = 'Total stocks is required'\n    if (!formData.basePrice) newErrors.basePrice = 'Base price is required'\n    if (!formData.expectedROI) newErrors.expectedROI = 'Expected ROI is required'\n    if (!formData.minimumInvestment) newErrors.minimumInvestment = 'Minimum investment is required'\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!validateForm()) return\n\n    setIsSubmitting(true)\n\n    try {\n      // Prepare stock data for API\n      const stockData = {\n        propertyId: formData.propertyId,\n        totalStocks: parseInt(formData.totalStocks),\n        basePrice: parseFloat(formData.basePrice),\n        expectedROI: parseFloat(formData.expectedROI),\n        minimumInvestment: parseFloat(formData.minimumInvestment),\n        stockTiers: formData.stockTiers.map(tier => ({\n          name: tier.name,\n          pricePerStock: parseFloat(tier.pricePerStock),\n          stockCount: parseInt(tier.stockCount),\n          minInvestment: parseFloat(tier.minInvestment),\n          maxInvestment: tier.maxInvestment ? parseFloat(tier.maxInvestment) : undefined,\n          description: tier.description,\n          benefits: tier.benefits\n        })),\n        riskLevel: formData.riskLevel as 'low' | 'medium' | 'high',\n        investmentPeriod: parseInt(formData.investmentPeriod),\n        dividendFrequency: formData.dividendFrequency as 'monthly' | 'quarterly' | 'half-yearly' | 'yearly',\n        description: formData.description\n      }\n\n      // Use RTK Query mutation\n      const result = await createStock(stockData).unwrap()\n\n      if (result.success) {\n        toast.success('Stock created successfully!')\n        router.push('/stocks')\n      } else {\n        toast.error(result.message || 'Failed to create stock')\n      }\n\n    } catch (error: any) {\n      console.error('Error creating stock:', error)\n      toast.error(error?.data?.message || 'Failed to create stock')\n\n      // Handle validation errors\n      if (error?.data?.error && typeof error.data.error === 'string') {\n        const errorMessages = error.data.error.split(', ')\n        const newErrors: Record<string, string> = {}\n        errorMessages.forEach((msg: string) => {\n          // Try to extract field name from error message\n          if (msg.includes('propertyId')) newErrors.propertyId = msg\n          else if (msg.includes('totalStocks')) newErrors.totalStocks = msg\n          else if (msg.includes('basePrice')) newErrors.basePrice = msg\n          else if (msg.includes('expectedROI')) newErrors.expectedROI = msg\n          else if (msg.includes('minimumInvestment')) newErrors.minimumInvestment = msg\n        })\n        setErrors(newErrors)\n      }\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"h-full overflow-auto\">\n        <div className=\"max-w-6xl mx-auto p-6 space-y-6\">\n          {/* Header */}\n          <div className=\"bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white\">\n            <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\n              <div className=\"flex items-center gap-4\">\n                <Button\n                  variant=\"secondary\"\n                  size=\"sm\"\n                  onClick={() => router.back()}\n                  className=\"bg-white/20 hover:bg-white/30 text-white border-white/30\"\n                >\n                  <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                  Back to Stocks\n                </Button>\n                <div>\n                  <h1 className=\"text-3xl font-bold mb-2 flex items-center gap-3\">\n                    <TrendingUp className=\"h-8 w-8\" />\n                    Create Investment Stock\n                  </h1>\n                  <p className=\"text-blue-100\">Set up investment opportunities for property funding</p>\n                </div>\n              </div>\n              <div className=\"flex items-center gap-3\">\n                <Button\n                  onClick={handleSubmit}\n                  disabled={isSubmitting}\n                  className=\"bg-yellow-500 hover:bg-yellow-600 text-black font-semibold\"\n                >\n                  {isSubmitting ? (\n                    <>\n                      <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                      Creating...\n                    </>\n                  ) : (\n                    <>\n                      <Save className=\"h-4 w-4 mr-2\" />\n                      Create Stock\n                    </>\n                  )}\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Basic Stock Information */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <Building className=\"h-5 w-5\" />\n                  Basic Stock Information\n                </CardTitle>\n                <CardDescription>Configure the fundamental stock details</CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <Label className=\"text-sm font-medium text-gray-700\">\n                      Select Property *\n                    </Label>\n                    <Select\n                      value={formData.propertyId}\n                      onValueChange={(value) => handleInputChange('propertyId', value)}\n                    >\n                      <SelectTrigger className=\"mt-1\">\n                        <SelectValue placeholder=\"Choose a property\" />\n                      </SelectTrigger>\n                      <SelectContent>\n                        {isLoadingProperties ? (\n                          <SelectItem value=\"loading\" disabled>\n                            Loading properties...\n                          </SelectItem>\n                        ) : availableProperties.length > 0 ? (\n                          availableProperties.map((property) => (\n                            <SelectItem key={property.id} value={property.id}>\n                              <div>\n                                <div className=\"font-medium\">{property.name}</div>\n                                <div className=\"text-xs text-gray-500\">{property.location}</div>\n                              </div>\n                            </SelectItem>\n                          ))\n                        ) : (\n                          <SelectItem value=\"no-properties\" disabled>\n                            No properties available\n                          </SelectItem>\n                        )}\n                      </SelectContent>\n                    </Select>\n                    {errors.propertyId && (\n                      <p className=\"text-red-500 text-sm mt-1 flex items-center gap-1\">\n                        <AlertCircle className=\"h-4 w-4\" />\n                        {errors.propertyId}\n                      </p>\n                    )}\n                  </div>\n\n                  <div>\n                    <Label className=\"text-sm font-medium text-gray-700\">\n                      Total Stocks *\n                    </Label>\n                    <Input\n                      type=\"number\"\n                      value={formData.totalStocks}\n                      onChange={(e) => handleInputChange('totalStocks', e.target.value)}\n                      className={`mt-1 ${errors.totalStocks ? 'border-red-500' : ''}`}\n                      placeholder=\"e.g., 10000\"\n                    />\n                    {errors.totalStocks && (\n                      <p className=\"text-red-500 text-sm mt-1 flex items-center gap-1\">\n                        <AlertCircle className=\"h-4 w-4\" />\n                        {errors.totalStocks}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Financial Configuration */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <DollarSign className=\"h-5 w-5\" />\n                  Financial Configuration\n                </CardTitle>\n                <CardDescription>Set pricing and investment parameters</CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div>\n                    <Label className=\"text-sm font-medium text-gray-700\">\n                      Base Price per Stock (₹) *\n                    </Label>\n                    <Input\n                      type=\"number\"\n                      value={formData.basePrice}\n                      onChange={(e) => handleInputChange('basePrice', e.target.value)}\n                      className={`mt-1 ${errors.basePrice ? 'border-red-500' : ''}`}\n                      placeholder=\"e.g., 50000\"\n                    />\n                    {errors.basePrice && (\n                      <p className=\"text-red-500 text-sm mt-1 flex items-center gap-1\">\n                        <AlertCircle className=\"h-4 w-4\" />\n                        {errors.basePrice}\n                      </p>\n                    )}\n                  </div>\n\n                  <div>\n                    <Label className=\"text-sm font-medium text-gray-700\">\n                      Expected ROI (%) *\n                    </Label>\n                    <Input\n                      type=\"number\"\n                      step=\"0.1\"\n                      value={formData.expectedROI}\n                      onChange={(e) => handleInputChange('expectedROI', e.target.value)}\n                      className={`mt-1 ${errors.expectedROI ? 'border-red-500' : ''}`}\n                      placeholder=\"e.g., 12.5\"\n                    />\n                    {errors.expectedROI && (\n                      <p className=\"text-red-500 text-sm mt-1 flex items-center gap-1\">\n                        <AlertCircle className=\"h-4 w-4\" />\n                        {errors.expectedROI}\n                      </p>\n                    )}\n                  </div>\n\n                  <div>\n                    <Label className=\"text-sm font-medium text-gray-700\">\n                      Minimum Investment (₹) *\n                    </Label>\n                    <Input\n                      type=\"number\"\n                      value={formData.minimumInvestment}\n                      onChange={(e) => handleInputChange('minimumInvestment', e.target.value)}\n                      className={`mt-1 ${errors.minimumInvestment ? 'border-red-500' : ''}`}\n                      placeholder=\"e.g., 100000\"\n                    />\n                    {errors.minimumInvestment && (\n                      <p className=\"text-red-500 text-sm mt-1 flex items-center gap-1\">\n                        <AlertCircle className=\"h-4 w-4\" />\n                        {errors.minimumInvestment}\n                      </p>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div>\n                    <Label className=\"text-sm font-medium text-gray-700\">\n                      Risk Level\n                    </Label>\n                    <Select\n                      value={formData.riskLevel}\n                      onValueChange={(value) => handleInputChange('riskLevel', value)}\n                    >\n                      <SelectTrigger className=\"mt-1\">\n                        <SelectValue placeholder=\"Select risk level\" />\n                      </SelectTrigger>\n                      <SelectContent>\n                        {riskLevels.map((risk) => (\n                          <SelectItem key={risk.value} value={risk.value}>\n                            <div>\n                              <div className=\"font-medium\">{risk.label}</div>\n                              <div className=\"text-xs text-gray-500\">{risk.description}</div>\n                            </div>\n                          </SelectItem>\n                        ))}\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  <div>\n                    <Label className=\"text-sm font-medium text-gray-700\">\n                      Investment Period (Months)\n                    </Label>\n                    <Input\n                      type=\"number\"\n                      value={formData.investmentPeriod}\n                      onChange={(e) => handleInputChange('investmentPeriod', e.target.value)}\n                      className=\"mt-1\"\n                      placeholder=\"e.g., 24\"\n                    />\n                  </div>\n\n                  <div>\n                    <Label className=\"text-sm font-medium text-gray-700\">\n                      Dividend Frequency\n                    </Label>\n                    <Select\n                      value={formData.dividendFrequency}\n                      onValueChange={(value) => handleInputChange('dividendFrequency', value)}\n                    >\n                      <SelectTrigger className=\"mt-1\">\n                        <SelectValue placeholder=\"Select frequency\" />\n                      </SelectTrigger>\n                      <SelectContent>\n                        {dividendFrequencies.map((freq) => (\n                          <SelectItem key={freq.value} value={freq.value}>\n                            {freq.label}\n                          </SelectItem>\n                        ))}\n                      </SelectContent>\n                    </Select>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Stock Tiers */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <Target className=\"h-5 w-5\" />\n                  Investment Tiers\n                </CardTitle>\n                <CardDescription>Create different investment levels with varying benefits</CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <p className=\"text-sm text-gray-600\">Configure multiple investment tiers to attract different investor segments</p>\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={addStockTier}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <Plus className=\"h-4 w-4\" />\n                    Add Tier\n                  </Button>\n                </div>\n\n                <div className=\"space-y-4\">\n                  {formData.stockTiers.map((tier, index) => (\n                    <div key={tier.id} className=\"p-4 border border-gray-200 rounded-lg bg-gray-50\">\n                      <div className=\"flex items-center justify-between mb-3\">\n                        <h5 className=\"font-medium text-gray-900\">Tier {index + 1}: {tier.name || 'Unnamed'}</h5>\n                        {formData.stockTiers.length > 1 && (\n                          <Button\n                            type=\"button\"\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => removeStockTier(tier.id)}\n                            className=\"text-red-600 hover:text-red-800\"\n                          >\n                            <X className=\"h-4 w-4\" />\n                          </Button>\n                        )}\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                        <div>\n                          <Label className=\"text-sm font-medium text-gray-700\">\n                            Tier Name\n                          </Label>\n                          <Input\n                            value={tier.name}\n                            onChange={(e) => updateStockTier(tier.id, 'name', e.target.value)}\n                            placeholder=\"e.g., Basic, Premium\"\n                            className=\"mt-1\"\n                          />\n                        </div>\n\n                        <div>\n                          <Label className=\"text-sm font-medium text-gray-700\">\n                            Stock Count\n                          </Label>\n                          <Input\n                            type=\"number\"\n                            value={tier.stockCount}\n                            onChange={(e) => updateStockTier(tier.id, 'stockCount', e.target.value)}\n                            placeholder=\"e.g., 1000\"\n                            className=\"mt-1\"\n                          />\n                        </div>\n\n                        <div>\n                          <Label className=\"text-sm font-medium text-gray-700\">\n                            Price per Stock (₹)\n                          </Label>\n                          <Input\n                            type=\"number\"\n                            value={tier.pricePerStock}\n                            onChange={(e) => updateStockTier(tier.id, 'pricePerStock', e.target.value)}\n                            placeholder=\"e.g., 25000\"\n                            className=\"mt-1\"\n                          />\n                        </div>\n\n                        <div>\n                          <Label className=\"text-sm font-medium text-gray-700\">\n                            Min Investment (₹)\n                          </Label>\n                          <Input\n                            type=\"number\"\n                            value={tier.minInvestment}\n                            onChange={(e) => updateStockTier(tier.id, 'minInvestment', e.target.value)}\n                            placeholder=\"e.g., 50000\"\n                            className=\"mt-1\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className=\"mt-3\">\n                        <Label className=\"text-sm font-medium text-gray-700\">\n                          Description\n                        </Label>\n                        <Textarea\n                          value={tier.description}\n                          onChange={(e) => updateStockTier(tier.id, 'description', e.target.value)}\n                          placeholder=\"Describe the benefits and features of this tier\"\n                          className=\"mt-1\"\n                          rows={2}\n                        />\n                      </div>\n\n                      {/* Tier Calculations */}\n                      {tier.stockCount && tier.pricePerStock && (\n                        <div className=\"mt-3 p-3 bg-blue-50 rounded border\">\n                          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                            <div>\n                              <span className=\"text-blue-700\">Tier Value:</span>\n                              <span className=\"font-semibold text-blue-900 ml-2\">\n                                ₹{(parseInt(tier.stockCount) * parseInt(tier.pricePerStock)).toLocaleString()}\n                              </span>\n                            </div>\n                            <div>\n                              <span className=\"text-blue-700\">Stock Range:</span>\n                              <span className=\"font-semibold text-blue-900 ml-2\">\n                                1 - {tier.stockCount} units\n                              </span>\n                            </div>\n                            <div>\n                              <span className=\"text-blue-700\">Investment Range:</span>\n                              <span className=\"font-semibold text-blue-900 ml-2\">\n                                ₹{parseInt(tier.minInvestment || tier.pricePerStock || '0').toLocaleString()} - ₹{(parseInt(tier.stockCount) * parseInt(tier.pricePerStock)).toLocaleString()}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n\n                {/* Total Summary */}\n                {totalStocksFromTiers > 0 && (\n                  <div className=\"p-4 bg-green-50 rounded-lg border border-green-200\">\n                    <h5 className=\"font-medium text-green-900 mb-2\">📊 Investment Summary</h5>\n                    <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-green-700\">Total Stocks (All Tiers):</span>\n                        <span className=\"font-bold text-green-900 ml-2\">\n                          {totalStocksFromTiers.toLocaleString()} units\n                        </span>\n                      </div>\n                      <div>\n                        <span className=\"text-green-700\">Total Investment Value:</span>\n                        <span className=\"font-bold text-green-900 ml-2\">\n                          ₹{totalValueFromTiers.toLocaleString()}\n                        </span>\n                      </div>\n                      <div>\n                        <span className=\"text-green-700\">Value in Crores:</span>\n                        <span className=\"font-bold text-green-900 ml-2\">\n                          ₹{(totalValueFromTiers / 10000000).toFixed(2)} Cr\n                        </span>\n                      </div>\n                      <div>\n                        <span className=\"text-green-700\">Active Tiers:</span>\n                        <span className=\"font-bold text-green-900 ml-2\">\n                          {formData.stockTiers.filter(t => t.stockCount && t.pricePerStock).length} Tiers\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n\n            {/* Description */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Investment Description</CardTitle>\n                <CardDescription>Provide detailed information about this investment opportunity</CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div>\n                  <Label className=\"text-sm font-medium text-gray-700\">\n                    Investment Overview\n                  </Label>\n                  <Textarea\n                    value={formData.description}\n                    onChange={(e) => handleInputChange('description', e.target.value)}\n                    placeholder=\"Describe the investment opportunity, expected returns, timeline, and any other relevant details...\"\n                    className=\"mt-1\"\n                    rows={4}\n                  />\n                </div>\n              </CardContent>\n            </Card>\n          </form>\n        </div>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAdA;;;;;;;;;;;;;;AAoDe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,6BAA6B;IAC7B,MAAM,EAAE,MAAM,cAAc,EAAE,WAAW,mBAAmB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD,EAAE;QACrF,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IAEA,qBAAqB;IACrB,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,2IAAA,CAAA,yBAAsB,AAAD;IAE3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;QACtD,YAAY;QACZ,aAAa;QACb,WAAW;QACX,aAAa;QACb,mBAAmB;QACnB,YAAY;YACV;gBACE,IAAI;gBACJ,MAAM;gBACN,eAAe;gBACf,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,aAAa;gBACb,UAAU;oBAAC;oBAAoB;iBAAgB;YACjD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,eAAe;gBACf,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,aAAa;gBACb,UAAU;oBAAC;oBAAkB;oBAAoB;iBAAoB;YACvE;SACD;QACD,WAAW;QACX,kBAAkB;QAClB,mBAAmB;QACnB,aAAa;IACf;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB,gBAAgB,QAAQ,EAAE;IACjD,MAAM,sBAAsB,eAAe,GAAG,CAAC,CAAC,WAAkB,CAAC;YACjE,IAAI,SAAS,GAAG,IAAI,SAAS,EAAE;YAC/B,MAAM,SAAS,IAAI;YACnB,UAAU,GAAG,SAAS,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,QAAQ,CAAC,KAAK,EAAE;YACjE,cAAc,SAAS,YAAY;YACnC,QAAQ,SAAS,MAAM;QACzB,CAAC;IAED,MAAM,aAAa;QACjB;YAAE,OAAO;YAAO,OAAO;YAAY,aAAa;QAAmC;QACnF;YAAE,OAAO;YAAU,OAAO;YAAe,aAAa;QAA+B;QACrF;YAAE,OAAO;YAAQ,OAAO;YAAa,aAAa;QAA+C;KAClG;IAED,MAAM,sBAAsB;QAC1B;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAa,OAAO;QAAY;QACzC;YAAE,OAAO;YAAe,OAAO;QAAc;QAC7C;YAAE,OAAO;YAAU,OAAO;QAAS;KACpC;IAED,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;QAED,sCAAsC;QACtC,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE;gBACX,CAAC;QACH;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,UAAqB;YACzB,IAAI,KAAK,GAAG;YACZ,MAAM;YACN,eAAe;YACf,YAAY;YACZ,eAAe;YACf,eAAe;YACf,aAAa;YACb,UAAU,EAAE;QACd;QACA,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,YAAY;uBAAI,KAAK,UAAU;oBAAE;iBAAQ;YAC3C,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC;QACvB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,YAAY,KAAK,UAAU,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACzD,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC,IAAY,OAAe;QAClD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,YAAY,KAAK,UAAU,CAAC,GAAG,CAAC,CAAA,OAC9B,KAAK,EAAE,KAAK,KAAK;wBAAE,GAAG,IAAI;wBAAE,CAAC,MAAM,EAAE;oBAAM,IAAI;YAEnD,CAAC;IACH;IAEA,8CAA8C;IAC9C,MAAM,uBAAuB,SAAS,UAAU,CAAC,MAAM,CAAC,CAAC,OAAO,OAC9D,QAAQ,SAAS,KAAK,UAAU,IAAI,MAAM;IAG5C,MAAM,sBAAsB,SAAS,UAAU,CAAC,MAAM,CAAC,CAAC,OAAO,OAC7D,QAAS,SAAS,KAAK,UAAU,IAAI,OAAO,SAAS,KAAK,aAAa,IAAI,MAAO;IAGpF,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,UAAU,EAAE,UAAU,UAAU,GAAG;QACjD,IAAI,CAAC,SAAS,WAAW,EAAE,UAAU,WAAW,GAAG;QACnD,IAAI,CAAC,SAAS,SAAS,EAAE,UAAU,SAAS,GAAG;QAC/C,IAAI,CAAC,SAAS,WAAW,EAAE,UAAU,WAAW,GAAG;QACnD,IAAI,CAAC,SAAS,iBAAiB,EAAE,UAAU,iBAAiB,GAAG;QAE/D,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,gBAAgB;QAEhB,IAAI;YACF,6BAA6B;YAC7B,MAAM,YAAY;gBAChB,YAAY,SAAS,UAAU;gBAC/B,aAAa,SAAS,SAAS,WAAW;gBAC1C,WAAW,WAAW,SAAS,SAAS;gBACxC,aAAa,WAAW,SAAS,WAAW;gBAC5C,mBAAmB,WAAW,SAAS,iBAAiB;gBACxD,YAAY,SAAS,UAAU,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAC3C,MAAM,KAAK,IAAI;wBACf,eAAe,WAAW,KAAK,aAAa;wBAC5C,YAAY,SAAS,KAAK,UAAU;wBACpC,eAAe,WAAW,KAAK,aAAa;wBAC5C,eAAe,KAAK,aAAa,GAAG,WAAW,KAAK,aAAa,IAAI;wBACrE,aAAa,KAAK,WAAW;wBAC7B,UAAU,KAAK,QAAQ;oBACzB,CAAC;gBACD,WAAW,SAAS,SAAS;gBAC7B,kBAAkB,SAAS,SAAS,gBAAgB;gBACpD,mBAAmB,SAAS,iBAAiB;gBAC7C,aAAa,SAAS,WAAW;YACnC;YAEA,yBAAyB;YACzB,MAAM,SAAS,MAAM,YAAY,WAAW,MAAM;YAElD,IAAI,OAAO,OAAO,EAAE;gBAClB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,OAAO,IAAI;YAChC;QAEF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,MAAM,WAAW;YAEpC,2BAA2B;YAC3B,IAAI,OAAO,MAAM,SAAS,OAAO,MAAM,IAAI,CAAC,KAAK,KAAK,UAAU;gBAC9D,MAAM,gBAAgB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;gBAC7C,MAAM,YAAoC,CAAC;gBAC3C,cAAc,OAAO,CAAC,CAAC;oBACrB,+CAA+C;oBAC/C,IAAI,IAAI,QAAQ,CAAC,eAAe,UAAU,UAAU,GAAG;yBAClD,IAAI,IAAI,QAAQ,CAAC,gBAAgB,UAAU,WAAW,GAAG;yBACzD,IAAI,IAAI,QAAQ,CAAC,cAAc,UAAU,SAAS,GAAG;yBACrD,IAAI,IAAI,QAAQ,CAAC,gBAAgB,UAAU,WAAW,GAAG;yBACzD,IAAI,IAAI,QAAQ,CAAC,sBAAsB,UAAU,iBAAiB,GAAG;gBAC5E;gBACA,UAAU;YACZ;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,4TAAC,kJAAA,CAAA,UAAe;kBACd,cAAA,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI;4CAC1B,WAAU;;8DAEV,4TAAC,uSAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGxC,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;;sEACZ,4TAAC,ySAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAGpC,4TAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAGjC,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,6BACC;;8DACE,4TAAC,wSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA8B;;yEAInD;;8DACE,4TAAC,yRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAS7C,4TAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,4TAAC,mIAAA,CAAA,OAAI;;kDACH,4TAAC,mIAAA,CAAA,aAAU;;0DACT,4TAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,4TAAC,iSAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,4TAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,4TAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;;sEACC,4TAAC,oIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAoC;;;;;;sEAGrD,4TAAC,qIAAA,CAAA,SAAM;4DACL,OAAO,SAAS,UAAU;4DAC1B,eAAe,CAAC,QAAU,kBAAkB,cAAc;;8EAE1D,4TAAC,qIAAA,CAAA,gBAAa;oEAAC,WAAU;8EACvB,cAAA,4TAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,4TAAC,qIAAA,CAAA,gBAAa;8EACX,oCACC,4TAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;wEAAU,QAAQ;kFAAC;;;;;+EAGnC,oBAAoB,MAAM,GAAG,IAC/B,oBAAoB,GAAG,CAAC,CAAC,yBACvB,4TAAC,qIAAA,CAAA,aAAU;4EAAmB,OAAO,SAAS,EAAE;sFAC9C,cAAA,4TAAC;;kGACC,4TAAC;wFAAI,WAAU;kGAAe,SAAS,IAAI;;;;;;kGAC3C,4TAAC;wFAAI,WAAU;kGAAyB,SAAS,QAAQ;;;;;;;;;;;;2EAH5C,SAAS,EAAE;;;;kGAQ9B,4TAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;wEAAgB,QAAQ;kFAAC;;;;;;;;;;;;;;;;;wDAMhD,OAAO,UAAU,kBAChB,4TAAC;4DAAE,WAAU;;8EACX,4TAAC,2SAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEACtB,OAAO,UAAU;;;;;;;;;;;;;8DAKxB,4TAAC;;sEACC,4TAAC,oIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAoC;;;;;;sEAGrD,4TAAC,oIAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,OAAO,SAAS,WAAW;4DAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4DAChE,WAAW,CAAC,KAAK,EAAE,OAAO,WAAW,GAAG,mBAAmB,IAAI;4DAC/D,aAAY;;;;;;wDAEb,OAAO,WAAW,kBACjB,4TAAC;4DAAE,WAAU;;8EACX,4TAAC,2SAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEACtB,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS/B,4TAAC,mIAAA,CAAA,OAAI;;kDACH,4TAAC,mIAAA,CAAA,aAAU;;0DACT,4TAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,4TAAC,ySAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGpC,4TAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,4TAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;;0EACC,4TAAC,oIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAoC;;;;;;0EAGrD,4TAAC,oIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO,SAAS,SAAS;gEACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;gEAC9D,WAAW,CAAC,KAAK,EAAE,OAAO,SAAS,GAAG,mBAAmB,IAAI;gEAC7D,aAAY;;;;;;4DAEb,OAAO,SAAS,kBACf,4TAAC;gEAAE,WAAU;;kFACX,4TAAC,2SAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEACtB,OAAO,SAAS;;;;;;;;;;;;;kEAKvB,4TAAC;;0EACC,4TAAC,oIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAoC;;;;;;0EAGrD,4TAAC,oIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,WAAW;gEAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;gEAChE,WAAW,CAAC,KAAK,EAAE,OAAO,WAAW,GAAG,mBAAmB,IAAI;gEAC/D,aAAY;;;;;;4DAEb,OAAO,WAAW,kBACjB,4TAAC;gEAAE,WAAU;;kFACX,4TAAC,2SAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEACtB,OAAO,WAAW;;;;;;;;;;;;;kEAKzB,4TAAC;;0EACC,4TAAC,oIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAoC;;;;;;0EAGrD,4TAAC,oIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO,SAAS,iBAAiB;gEACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;gEACtE,WAAW,CAAC,KAAK,EAAE,OAAO,iBAAiB,GAAG,mBAAmB,IAAI;gEACrE,aAAY;;;;;;4DAEb,OAAO,iBAAiB,kBACvB,4TAAC;gEAAE,WAAU;;kFACX,4TAAC,2SAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEACtB,OAAO,iBAAiB;;;;;;;;;;;;;;;;;;;0DAMjC,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;;0EACC,4TAAC,oIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAoC;;;;;;0EAGrD,4TAAC,qIAAA,CAAA,SAAM;gEACL,OAAO,SAAS,SAAS;gEACzB,eAAe,CAAC,QAAU,kBAAkB,aAAa;;kFAEzD,4TAAC,qIAAA,CAAA,gBAAa;wEAAC,WAAU;kFACvB,cAAA,4TAAC,qIAAA,CAAA,cAAW;4EAAC,aAAY;;;;;;;;;;;kFAE3B,4TAAC,qIAAA,CAAA,gBAAa;kFACX,WAAW,GAAG,CAAC,CAAC,qBACf,4TAAC,qIAAA,CAAA,aAAU;gFAAkB,OAAO,KAAK,KAAK;0FAC5C,cAAA,4TAAC;;sGACC,4TAAC;4FAAI,WAAU;sGAAe,KAAK,KAAK;;;;;;sGACxC,4TAAC;4FAAI,WAAU;sGAAyB,KAAK,WAAW;;;;;;;;;;;;+EAH3C,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;kEAWnC,4TAAC;;0EACC,4TAAC,oIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAoC;;;;;;0EAGrD,4TAAC,oIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO,SAAS,gBAAgB;gEAChC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;gEACrE,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,4TAAC;;0EACC,4TAAC,oIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAoC;;;;;;0EAGrD,4TAAC,qIAAA,CAAA,SAAM;gEACL,OAAO,SAAS,iBAAiB;gEACjC,eAAe,CAAC,QAAU,kBAAkB,qBAAqB;;kFAEjE,4TAAC,qIAAA,CAAA,gBAAa;wEAAC,WAAU;kFACvB,cAAA,4TAAC,qIAAA,CAAA,cAAW;4EAAC,aAAY;;;;;;;;;;;kFAE3B,4TAAC,qIAAA,CAAA,gBAAa;kFACX,oBAAoB,GAAG,CAAC,CAAC,qBACxB,4TAAC,qIAAA,CAAA,aAAU;gFAAkB,OAAO,KAAK,KAAK;0FAC3C,KAAK,KAAK;+EADI,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAYzC,4TAAC,mIAAA,CAAA,OAAI;;kDACH,4TAAC,mIAAA,CAAA,aAAU;;0DACT,4TAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,4TAAC,6RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGhC,4TAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,4TAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,4TAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,4TAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;;0DAKhC,4TAAC;gDAAI,WAAU;0DACZ,SAAS,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC9B,4TAAC;wDAAkB,WAAU;;0EAC3B,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAG,WAAU;;4EAA4B;4EAAM,QAAQ;4EAAE;4EAAG,KAAK,IAAI,IAAI;;;;;;;oEACzE,SAAS,UAAU,CAAC,MAAM,GAAG,mBAC5B,4TAAC,qIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS,IAAM,gBAAgB,KAAK,EAAE;wEACtC,WAAU;kFAEV,cAAA,4TAAC,mRAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;0EAKnB,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;;0FACC,4TAAC,oIAAA,CAAA,QAAK;gFAAC,WAAU;0FAAoC;;;;;;0FAGrD,4TAAC,oIAAA,CAAA,QAAK;gFACJ,OAAO,KAAK,IAAI;gFAChB,UAAU,CAAC,IAAM,gBAAgB,KAAK,EAAE,EAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gFAChE,aAAY;gFACZ,WAAU;;;;;;;;;;;;kFAId,4TAAC;;0FACC,4TAAC,oIAAA,CAAA,QAAK;gFAAC,WAAU;0FAAoC;;;;;;0FAGrD,4TAAC,oIAAA,CAAA,QAAK;gFACJ,MAAK;gFACL,OAAO,KAAK,UAAU;gFACtB,UAAU,CAAC,IAAM,gBAAgB,KAAK,EAAE,EAAE,cAAc,EAAE,MAAM,CAAC,KAAK;gFACtE,aAAY;gFACZ,WAAU;;;;;;;;;;;;kFAId,4TAAC;;0FACC,4TAAC,oIAAA,CAAA,QAAK;gFAAC,WAAU;0FAAoC;;;;;;0FAGrD,4TAAC,oIAAA,CAAA,QAAK;gFACJ,MAAK;gFACL,OAAO,KAAK,aAAa;gFACzB,UAAU,CAAC,IAAM,gBAAgB,KAAK,EAAE,EAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;gFACzE,aAAY;gFACZ,WAAU;;;;;;;;;;;;kFAId,4TAAC;;0FACC,4TAAC,oIAAA,CAAA,QAAK;gFAAC,WAAU;0FAAoC;;;;;;0FAGrD,4TAAC,oIAAA,CAAA,QAAK;gFACJ,MAAK;gFACL,OAAO,KAAK,aAAa;gFACzB,UAAU,CAAC,IAAM,gBAAgB,KAAK,EAAE,EAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;gFACzE,aAAY;gFACZ,WAAU;;;;;;;;;;;;;;;;;;0EAKhB,4TAAC;gEAAI,WAAU;;kFACb,4TAAC,oIAAA,CAAA,QAAK;wEAAC,WAAU;kFAAoC;;;;;;kFAGrD,4TAAC,uIAAA,CAAA,WAAQ;wEACP,OAAO,KAAK,WAAW;wEACvB,UAAU,CAAC,IAAM,gBAAgB,KAAK,EAAE,EAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wEACvE,aAAY;wEACZ,WAAU;wEACV,MAAM;;;;;;;;;;;;4DAKT,KAAK,UAAU,IAAI,KAAK,aAAa,kBACpC,4TAAC;gEAAI,WAAU;0EACb,cAAA,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;;8FACC,4TAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,4TAAC;oFAAK,WAAU;;wFAAmC;wFAC/C,CAAC,SAAS,KAAK,UAAU,IAAI,SAAS,KAAK,aAAa,CAAC,EAAE,cAAc;;;;;;;;;;;;;sFAG/E,4TAAC;;8FACC,4TAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,4TAAC;oFAAK,WAAU;;wFAAmC;wFAC5C,KAAK,UAAU;wFAAC;;;;;;;;;;;;;sFAGzB,4TAAC;;8FACC,4TAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,4TAAC;oFAAK,WAAU;;wFAAmC;wFAC/C,SAAS,KAAK,aAAa,IAAI,KAAK,aAAa,IAAI,KAAK,cAAc;wFAAG;wFAAK,CAAC,SAAS,KAAK,UAAU,IAAI,SAAS,KAAK,aAAa,CAAC,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;uDArG7J,KAAK,EAAE;;;;;;;;;;4CAgHpB,uBAAuB,mBACtB,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAG,WAAU;kEAAkC;;;;;;kEAChD,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;;kFACC,4TAAC;wEAAK,WAAU;kFAAiB;;;;;;kFACjC,4TAAC;wEAAK,WAAU;;4EACb,qBAAqB,cAAc;4EAAG;;;;;;;;;;;;;0EAG3C,4TAAC;;kFACC,4TAAC;wEAAK,WAAU;kFAAiB;;;;;;kFACjC,4TAAC;wEAAK,WAAU;;4EAAgC;4EAC5C,oBAAoB,cAAc;;;;;;;;;;;;;0EAGxC,4TAAC;;kFACC,4TAAC;wEAAK,WAAU;kFAAiB;;;;;;kFACjC,4TAAC;wEAAK,WAAU;;4EAAgC;4EAC5C,CAAC,sBAAsB,QAAQ,EAAE,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAGlD,4TAAC;;kFACC,4TAAC;wEAAK,WAAU;kFAAiB;;;;;;kFACjC,4TAAC;wEAAK,WAAU;;4EACb,SAAS,UAAU,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,IAAI,EAAE,aAAa,EAAE,MAAM;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUvF,4TAAC,mIAAA,CAAA,OAAI;;kDACH,4TAAC,mIAAA,CAAA,aAAU;;0DACT,4TAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,4TAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,4TAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,4TAAC;;8DACC,4TAAC,oIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAoC;;;;;;8DAGrD,4TAAC,uIAAA,CAAA,WAAQ;oDACP,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,aAAY;oDACZ,WAAU;oDACV,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B;GAhpBwB;;QACP,oQAAA,CAAA,YAAS;QAKyC,uIAAA,CAAA,wBAAqB;QAOhE,2IAAA,CAAA,yBAAsB;;;KAbtB", "debugId": null}}]}