{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Format currency\nexport function formatCurrency(amount: number, currency = \"₹\"): string {\n  return `${currency}${amount.toLocaleString('en-IN', { \n    minimumFractionDigits: 2, \n    maximumFractionDigits: 2 \n  })}`\n}\n\n// Format date\nexport function formatDate(date: string | Date, format = \"dd/MM/yyyy\"): string {\n  const d = new Date(date)\n  \n  if (format === \"dd/MM/yyyy\") {\n    return d.toLocaleDateString('en-GB')\n  }\n  \n  if (format === \"relative\") {\n    const now = new Date()\n    const diffInMs = now.getTime() - d.getTime()\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))\n    \n    if (diffInDays === 0) return \"Today\"\n    if (diffInDays === 1) return \"Yesterday\"\n    if (diffInDays < 7) return `${diffInDays} days ago`\n    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`\n    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`\n    return `${Math.floor(diffInDays / 365)} years ago`\n  }\n  \n  return d.toLocaleDateString()\n}\n\n// Truncate text\nexport function truncateText(text: string, length = 50): string {\n  if (text.length <= length) return text\n  return text.substring(0, length) + \"...\"\n}\n\n// Generate initials\nexport function getInitials(firstName?: string, lastName?: string, email?: string): string {\n  if (firstName && lastName) {\n    return `${firstName[0]}${lastName[0]}`.toUpperCase()\n  }\n  \n  if (firstName) {\n    return firstName.substring(0, 2).toUpperCase()\n  }\n  \n  if (email) {\n    return email.substring(0, 2).toUpperCase()\n  }\n  \n  return \"U\"\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n// Sleep function\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\n// Generate random ID\nexport function generateId(prefix = \"\"): string {\n  const timestamp = Date.now().toString(36)\n  const random = Math.random().toString(36).substring(2, 8)\n  return `${prefix}${timestamp}${random}`.toUpperCase()\n}\n\n// Validate email\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// Validate phone\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\n// Format phone number\nexport function formatPhone(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '')\n  \n  if (cleaned.length === 10) {\n    return `+91 ${cleaned.substring(0, 5)} ${cleaned.substring(5)}`\n  }\n  \n  if (cleaned.length === 12 && cleaned.startsWith('91')) {\n    return `+${cleaned.substring(0, 2)} ${cleaned.substring(2, 7)} ${cleaned.substring(7)}`\n  }\n  \n  return phone\n}\n\n// Calculate percentage\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0\n  return Math.round((value / total) * 100)\n}\n\n// Get status color\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    active: \"text-green-600 bg-green-100\",\n    inactive: \"text-gray-600 bg-gray-100\",\n    pending: \"text-yellow-600 bg-yellow-100\",\n    suspended: \"text-red-600 bg-red-100\",\n    completed: \"text-green-600 bg-green-100\",\n    failed: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    new: \"text-blue-600 bg-blue-100\",\n    contacted: \"text-purple-600 bg-purple-100\",\n    qualified: \"text-indigo-600 bg-indigo-100\",\n    converted: \"text-green-600 bg-green-100\",\n    lost: \"text-red-600 bg-red-100\",\n  }\n  \n  return statusColors[status.toLowerCase()] || \"text-gray-600 bg-gray-100\"\n}\n\n// Copy to clipboard\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text)\n    return true\n  } catch (error) {\n    console.error('Failed to copy to clipboard:', error)\n    return false\n  }\n}\n\n// Download file\nexport function downloadFile(data: any, filename: string, type = 'application/json'): void {\n  const blob = new Blob([typeof data === 'string' ? data : JSON.stringify(data, null, 2)], { type })\n  const url = URL.createObjectURL(blob)\n  const link = document.createElement('a')\n  link.href = url\n  link.download = filename\n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n  URL.revokeObjectURL(url)\n}\n\n// Format file size\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// Get file extension\nexport function getFileExtension(filename: string): string {\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)\n}\n\n// Check if mobile device\nexport function isMobile(): boolean {\n  if (typeof window === 'undefined') return false\n  return window.innerWidth < 768\n}\n\n// Scroll to element\nexport function scrollToElement(elementId: string, offset = 0): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const top = element.offsetTop - offset\n    window.scrollTo({ top, behavior: 'smooth' })\n  }\n}\n\n// Local storage helpers\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    if (typeof window === 'undefined') return defaultValue || null\n    \n    try {\n      const item = localStorage.getItem(key)\n      return item ? JSON.parse(item) : defaultValue || null\n    } catch (error) {\n      console.error(`Error getting item from localStorage:`, error)\n      return defaultValue || null\n    }\n  },\n  \n  set: <T>(key: string, value: T): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.setItem(key, JSON.stringify(value))\n    } catch (error) {\n      console.error(`Error setting item in localStorage:`, error)\n    }\n  },\n  \n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.removeItem(key)\n    } catch (error) {\n      console.error(`Error removing item from localStorage:`, error)\n    }\n  },\n  \n  clear: (): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.clear()\n    } catch (error) {\n      console.error(`Error clearing localStorage:`, error)\n    }\n  }\n}\n\n// Format time\nexport function formatTime(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc,EAAE,WAAW,GAAG;IAC3D,OAAO,GAAG,WAAW,OAAO,cAAc,CAAC,SAAS;QAClD,uBAAuB;QACvB,uBAAuB;IACzB,IAAI;AACN;AAGO,SAAS,WAAW,IAAmB,EAAE,SAAS,YAAY;IACnE,MAAM,IAAI,IAAI,KAAK;IAEnB,IAAI,WAAW,cAAc;QAC3B,OAAO,EAAE,kBAAkB,CAAC;IAC9B;IAEA,IAAI,WAAW,YAAY;QACzB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,EAAE,OAAO;QAC1C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE7D,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,SAAS,CAAC;QACnD,IAAI,aAAa,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC;QACrE,IAAI,aAAa,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI,WAAW,CAAC;QACxE,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,KAAK,UAAU,CAAC;IACpD;IAEA,OAAO,EAAE,kBAAkB;AAC7B;AAGO,SAAS,aAAa,IAAY,EAAE,SAAS,EAAE;IACpD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,SAAS,CAAC,GAAG,UAAU;AACrC;AAGO,SAAS,YAAY,SAAkB,EAAE,QAAiB,EAAE,KAAc;IAC/E,IAAI,aAAa,UAAU;QACzB,OAAO,GAAG,SAAS,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW;IACpD;IAEA,IAAI,WAAW;QACb,OAAO,UAAU,SAAS,CAAC,GAAG,GAAG,WAAW;IAC9C;IAEA,IAAI,OAAO;QACT,OAAO,MAAM,SAAS,CAAC,GAAG,GAAG,WAAW;IAC1C;IAEA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,SAAS,WAAW,SAAS,EAAE;IACpC,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,CAAC;IACtC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACvD,OAAO,GAAG,SAAS,YAAY,QAAQ,CAAC,WAAW;AACrD;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAGO,SAAS,YAAY,KAAa;IACvC,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IAErC,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,CAAC,IAAI,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACjE;IAEA,IAAI,QAAQ,MAAM,KAAK,MAAM,QAAQ,UAAU,CAAC,OAAO;QACrD,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACzF;IAEA,OAAO;AACT;AAGO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAGO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,QAAQ;QACR,UAAU;QACV,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;QACR,WAAW;QACX,KAAK;QACL,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;IACR;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAGO,SAAS,aAAa,IAAS,EAAE,QAAgB,EAAE,OAAO,kBAAkB;IACjF,MAAM,OAAO,IAAI,KAAK;QAAC,OAAO,SAAS,WAAW,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM;KAAG,EAAE;QAAE;IAAK;IAChG,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAGO,SAAS;IACd,wCAAmC,OAAO;;AAE5C;AAGO,SAAS,gBAAgB,SAAiB,EAAE,SAAS,CAAC;IAC3D,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,MAAM,QAAQ,SAAS,GAAG;QAChC,OAAO,QAAQ,CAAC;YAAE;YAAK,UAAU;QAAS;IAC5C;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,wCAAmC,OAAO,gBAAgB;;IAS5D;IAEA,KAAK,CAAI,KAAa;QACpB,wCAAmC;;IAOrC;IAEA,QAAQ,CAAC;QACP,wCAAmC;;IAOrC;IAEA,OAAO;QACL,wCAAmC;;IAOrC;AACF;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"card-base text-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6BACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"btn-primary\",\n        destructive: \"bg-red-500 text-white hover:bg-red-600\",\n        outline: \"btn-outline\",\n        secondary: \"btn-secondary\",\n        ghost: \"hover:bg-primary-100 text-primary\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,mQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"input-field flex h-10 w-full rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6WAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2RACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,8QAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,8QAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,+QAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6WAAC,+QAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,oSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,+QAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,wSAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,+QAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6WAAC,+QAAA,CAAA,SAAsB;kBACrB,cAAA,6WAAC,+QAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6WAAC;;;;;8BACD,6WAAC,+QAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6WAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,+QAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6WAAC;gBAAK,WAAU;0BACd,cAAA,6WAAC,+QAAA,CAAA,gBAA6B;8BAC5B,cAAA,6WAAC,wRAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6WAAC,+QAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,+QAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,+QAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6WAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 703, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/logo.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'full' | 'icon' | 'text'\n  className?: string\n}\n\nconst Logo: React.FC<LogoProps> = ({ \n  size = 'md', \n  variant = 'full', \n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'h-6 w-6',\n    md: 'h-8 w-8', \n    lg: 'h-10 w-10',\n    xl: 'h-12 w-12'\n  }\n\n  const textSizeClasses = {\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-2xl', \n    xl: 'text-3xl'\n  }\n\n  const LogoIcon = () => (\n    <div className={`${sizeClasses[size]} bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center shadow-lg ${className}`}>\n      <div className=\"relative\">\n        {/* S */}\n        <div className=\"absolute -left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5 ml-1\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n        </div>\n        \n        {/* G */}\n        <div className=\"absolute left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-4 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"absolute right-0 top-2 w-1 h-2 bg-white rounded-full\"></div>\n          <div className=\"absolute right-0 top-3 w-1.5 h-1 bg-white rounded-full\"></div>\n        </div>\n        \n        {/* M */}\n        <div className=\"absolute left-4 top-0\">\n          <div className=\"w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-0.5 top-1 w-1 h-1 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1 top-0 w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1.5 top-0 w-1 h-5 bg-white rounded-full\"></div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const LogoText = () => (\n    <span className={`font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent ${textSizeClasses[size]} ${className}`}>\n      SGM\n    </span>\n  )\n\n  if (variant === 'icon') {\n    return <LogoIcon />\n  }\n\n  if (variant === 'text') {\n    return <LogoText />\n  }\n\n  return (\n    <div className={`flex items-center gap-3 ${className}`}>\n      <LogoIcon />\n      <LogoText />\n    </div>\n  )\n}\n\nexport default Logo\n"], "names": [], "mappings": ";;;;;AAQA,MAAM,OAA4B,CAAC,EACjC,OAAO,IAAI,EACX,UAAU,MAAM,EAChB,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,WAAW,kBACf,6WAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,mGAAmG,EAAE,WAAW;sBACnJ,cAAA,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMvB,MAAM,WAAW,kBACf,6WAAC;YAAK,WAAW,CAAC,mFAAmF,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;sBAAE;;;;;;IAK/I,IAAI,YAAY,QAAQ;QACtB,qBAAO,6WAAC;;;;;IACV;IAEA,IAAI,YAAY,QAAQ;QACtB,qBAAO,6WAAC;;;;;IACV;IAEA,qBACE,6WAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;0BACpD,6WAAC;;;;;0BACD,6WAAC;;;;;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAppSelector } from '@/store'\nimport { selectUser } from '@/store/slices/authSlice'\nimport { UserRole } from '@/types'\nimport { cn } from '@/lib/utils'\nimport Logo from '@/components/ui/logo'\nimport {\n  LayoutDashboard,\n  Users,\n  Building,\n  TrendingUp,\n  Target,\n  DollarSign,\n  Headphones,\n  Settings,\n  Plus,\n  UserPlus,\n  UserCheck,\n  BarChart3,\n  Shield,\n  Bell,\n  PieChart,\n  CheckSquare,\n  CalendarDays,\n  UserCog,\n  Award,\n  Minus,\n  Receipt,\n  CreditCard,\n  Sparkles\n} from 'lucide-react'\n\ninterface SidebarProps {\n  collapsed?: boolean\n  onToggle?: () => void\n}\n\nexport default function Sidebar({ collapsed = false, onToggle }: SidebarProps) {\n  const pathname = usePathname()\n  const user = useAppSelector((state) => selectUser(state as any))\n\n  console.log('Sidebar rendering...', { collapsed, pathname, user })\n\n  const menuSections = [\n    {\n      title: \"Dashboard\",\n      items: [\n        {\n          id: \"main-dashboard\",\n          label: \"Overview\",\n          icon: LayoutDashboard,\n          href: \"/dashboard\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES, UserRole.USER]\n        }\n        \n      ]\n    },\n    {\n      title: \"User Management\",\n      items: [\n        {\n          id: \"users-overview\",\n          label: \"All Users\",\n          icon: Users,\n          href: \"/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-management-comprehensive\",\n          label: \"User Management\",\n          icon: Users,\n          href: \"/user-management\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-user\",\n          label: \"Add User\",\n          icon: UserPlus,\n          href: \"/users/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"role-management\",\n          label: \"Role Management\",\n          icon: UserCheck,\n          href: \"/users/roles\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Property Management\",\n      items: [\n        {\n          id: \"properties-overview\",\n          label: \"All Properties\",\n          icon: Building,\n          href: \"/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-property\",\n          label: \"Add Property (Basic)\",\n          icon: Plus,\n          href: \"/properties/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-property-enhanced\",\n          label: \"Add Property (Enhanced)\",\n          icon: Sparkles,\n          href: \"/properties/add-enhanced\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-owners\",\n          label: \"Property Owners\",\n          icon: UserCheck,\n          href: \"/property-owners\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Stock Investments\",\n      items: [\n        {\n          id: \"stocks-overview\",\n          label: \"All Stocks\",\n          icon: TrendingUp,\n          href: \"/stocks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"create-stock\",\n          label: \"Create Stock\",\n          icon: Plus,\n          href: \"/stocks/create\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Lead Management\",\n      items: [\n        {\n          id: \"leads-overview\",\n          label: \"Lead Management\",\n          icon: Target,\n          href: \"/leads\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES]\n        },\n        {\n          id: \"sales-analytics\",\n          label: \"Sales Analytics\",\n          icon: BarChart3,\n          href: \"/sales-analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Sales Management\",\n      items: [\n        {\n          id: \"sales-team\",\n          label: \"Sales Team\",\n          icon: UserCog,\n          href: \"/sales-team\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-tasks\",\n          label: \"Sales Tasks\",\n          icon: CheckSquare,\n          href: \"/sales-tasks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-calendar\",\n          label: \"Sales Calendar\",\n          icon: CalendarDays,\n          href: \"/sales-calendar\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-targets\",\n          label: \"Sales Targets\",\n          icon: Award,\n          href: \"/sales-targets\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"commissions\",\n          label: \"Commissions\",\n          icon: DollarSign,\n          href: \"/commissions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Financial Management\",\n      items: [\n        {\n          id: \"finance-overview\",\n          label: \"Financial Management\",\n          icon: DollarSign,\n          href: \"/finance\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-funds\",\n          label: \"Add Funds\",\n          icon: Plus,\n          href: \"/add-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"deduct-funds\",\n          label: \"Deduct Funds\",\n          icon: Minus,\n          href: \"/deduct-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"admin-transactions\",\n          label: \"All Transactions\",\n          icon: Receipt,\n          href: \"/admin-transactions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"withdrawal-requests\",\n          label: \"Withdrawal Requests\",\n          icon: CreditCard,\n          href: \"/withdrawal-requests\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Support Management\",\n      items: [\n        {\n          id: \"support-dashboard\",\n          label: \"Support Management\",\n          icon: Headphones,\n          href: \"/support\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"System & Settings\",\n      items: [\n        {\n          id: \"system-settings\",\n          label: \"Settings Management\",\n          icon: Settings,\n          href: \"/settings\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Reports & Analytics\",\n      items: [\n        {\n          id: \"analytics-dashboard\",\n          label: \"Analytics Dashboard\",\n          icon: BarChart3,\n          href: \"/analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-reports\",\n          label: \"User Reports\",\n          icon: Users,\n          href: \"/reports/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-reports\",\n          label: \"Property Reports\",\n          icon: Building,\n          href: \"/reports/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"financial-reports\",\n          label: \"Financial Reports\",\n          icon: DollarSign,\n          href: \"/reports/financial\",\n          roles: [UserRole.ADMIN]\n        },\n       \n      ]\n    }\n  ]\n\n  // Filter menu sections based on user role\n  const userRole = user?.role as UserRole || UserRole.USER\n  const filteredSections = menuSections.map(section => ({\n    ...section,\n    items: section.items.filter(item => item.roles.includes(userRole))\n  })).filter(section => section.items.length > 0)\n\n  // Debug logging\n  console.log('User:', user)\n  console.log('User Role:', user?.role)\n  console.log('UserRole enum:', userRole)\n  console.log('Menu Sections:', menuSections.length)\n  console.log('Filtered Sections:', filteredSections.length)\n\n  // Use filtered sections based on user role\n  const sectionsToShow = filteredSections\n\n  const isActive = (href: string) => {\n    if (!pathname) return false\n    if (href === '/dashboard') {\n      return pathname === '/dashboard'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <div className={cn(\n      \"h-screen bg-white border-r border-sky-200 flex flex-col transition-all duration-300 ease-in-out shadow-lg\",\n      collapsed ? \"w-20\" : \"w-72\"\n    )}>\n      {/* Logo */}\n      <div className=\"flex items-center space-x-3 p-4 border-b border-sky-200 flex-shrink-0 bg-gradient-to-r from-sky-50 to-sky-100\">\n        <Logo size={collapsed ? \"lg\" : \"xl\"} variant={collapsed ? \"icon\" : \"full\"} />\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-sky-300 scrollbar-track-sky-100\">\n        <div className=\"space-y-2\">\n          {sectionsToShow.length === 0 ? (\n            <div className=\"text-center text-red-500 p-4\">\n              <p>No menu items found!</p>\n              <p>User: {user?.firstName || 'Not logged in'}</p>\n              <p>Role: {user?.role || 'No role'}</p>\n            </div>\n          ) : (\n            sectionsToShow.map((section) => (\n              <div key={section.title} className=\"space-y-1 mb-6\">\n                {!collapsed && (\n                  <h3 className=\"px-3 py-2 text-xs font-semibold text-sky-600 uppercase tracking-wider\">\n                    {section.title}\n                  </h3>\n                )}\n\n                {section.items.map((item) => (\n                  <Link\n                    key={item.id}\n                    href={item.href || '#'}\n                    className={cn(\n                      \"flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-200\",\n                      isActive(item.href || '')\n                        ? \"bg-sky-600 text-white shadow-md\"\n                        : \"text-gray-700 hover:text-sky-600 hover:bg-sky-50\"\n                    )}\n                  >\n                    <item.icon className=\"h-5 w-5 flex-shrink-0\" />\n                    {!collapsed && (\n                      <span className=\"flex-1 truncate\">{item.label}</span>\n                    )}\n                  </Link>\n                ))}\n              </div>\n            ))\n          )}\n        </div>\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-sky-200 bg-gradient-to-r from-sky-50 to-sky-100\">\n        {!collapsed && (\n          <div className=\"text-xs text-sky-600 text-center font-medium\">\n            © 2025 SGM. All rights reserved.\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;AAyCe,SAAS,QAAQ,EAAE,YAAY,KAAK,EAAE,QAAQ,EAAgB;IAC3E,MAAM,WAAW,CAAA,GAAA,iQAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,OAAO,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,QAAU,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;IAElD,QAAQ,GAAG,CAAC,wBAAwB;QAAE;QAAW;QAAU;IAAK;IAEhE,MAAM,eAAe;QACnB;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,gTAAA,CAAA,kBAAe;oBACrB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,IAAI;qBAAC;gBAC3E;aAED;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,oSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,oSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,0RAAA,CAAA,SAAM;oBACZ,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBAC5D;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,gSAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,+SAAA,CAAA,cAAW;oBACjB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,0SAAA,CAAA,eAAY;oBAClB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,4RAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aAED;QACH;KACD;IAED,0CAA0C;IAC1C,MAAM,WAAW,MAAM,QAAoB,qHAAA,CAAA,WAAQ,CAAC,IAAI;IACxD,MAAM,mBAAmB,aAAa,GAAG,CAAC,CAAA,UAAW,CAAC;YACpD,GAAG,OAAO;YACV,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC;QAC1D,CAAC,GAAG,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,CAAC,MAAM,GAAG;IAE7C,gBAAgB;IAChB,QAAQ,GAAG,CAAC,SAAS;IACrB,QAAQ,GAAG,CAAC,cAAc,MAAM;IAChC,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,QAAQ,GAAG,CAAC,kBAAkB,aAAa,MAAM;IACjD,QAAQ,GAAG,CAAC,sBAAsB,iBAAiB,MAAM;IAEzD,2CAA2C;IAC3C,MAAM,iBAAiB;IAEvB,MAAM,WAAW,CAAC;QAChB,IAAI,CAAC,UAAU,OAAO;QACtB,IAAI,SAAS,cAAc;YACzB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6WAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,6GACA,YAAY,SAAS;;0BAGrB,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC,gIAAA,CAAA,UAAI;oBAAC,MAAM,YAAY,OAAO;oBAAM,SAAS,YAAY,SAAS;;;;;;;;;;;0BAIrE,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;8BACZ,eAAe,MAAM,KAAK,kBACzB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;0CAAE;;;;;;0CACH,6WAAC;;oCAAE;oCAAO,MAAM,aAAa;;;;;;;0CAC7B,6WAAC;;oCAAE;oCAAO,MAAM,QAAQ;;;;;;;;;;;;+BAG1B,eAAe,GAAG,CAAC,CAAC,wBAClB,6WAAC;4BAAwB,WAAU;;gCAChC,CAAC,2BACA,6WAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;gCAIjB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6WAAC,2RAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI,IAAI;wCACnB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA,SAAS,KAAK,IAAI,IAAI,MAClB,oCACA;;0DAGN,6WAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,CAAC,2BACA,6WAAC;gDAAK,WAAU;0DAAmB,KAAK,KAAK;;;;;;;uCAX1C,KAAK,EAAE;;;;;;2BATR,QAAQ,KAAK;;;;;;;;;;;;;;;0BA+B/B,6WAAC;gBAAI,WAAU;0BACZ,CAAC,2BACA,6WAAC;oBAAI,WAAU;8BAA+C;;;;;;;;;;;;;;;;;AAOxE", "debugId": null}}, {"offset": {"line": 1457, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAppSelector, useAppDispatch } from '@/store'\nimport { selectUser, logoutAsync } from '@/store/slices/authSlice'\nimport { Button } from '@/components/ui/button'\nimport Sidebar from './Sidebar'\nimport { Menu, Bell, Search, LogOut, User, Settings, HelpCircle, ChevronDown } from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)\n  const [userMenuOpen, setUserMenuOpen] = useState(false)\n  const router = useRouter()\n  const dispatch = useAppDispatch()\n  const user = useAppSelector(selectUser)\n\n  const handleLogout = async () => {\n    await dispatch(logoutAsync())\n    router.push('/login')\n  }\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed)\n  }\n\n  return (\n    <div className=\"h-screen bg-white overflow-hidden\">\n      <div className=\"flex h-full\">\n        {/* Sidebar */}\n        <Sidebar collapsed={sidebarCollapsed} onToggle={toggleSidebar} />\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col h-full overflow-hidden\">\n          {/* Header */}\n          <header className=\"bg-gradient-to-r from-sky-500 to-sky-600 shadow-lg border-b border-sky-700 flex-shrink-0 z-40\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              <div className=\"flex justify-between items-center py-4\">\n                {/* Left side */}\n                <div className=\"flex items-center space-x-4\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={toggleSidebar}\n                    className=\"lg:hidden hover:bg-white/20 text-white\"\n                  >\n                    <Menu className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Search Bar */}\n                  <div className=\"hidden md:flex items-center space-x-2 bg-white/20 rounded-lg px-4 py-2.5 min-w-[350px] border border-white/30\">\n                    <Search className=\"h-4 w-4 text-white/70\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search users, properties, transactions...\"\n                      className=\"bg-transparent border-none outline-none flex-1 text-sm placeholder:text-white/70 text-white\"\n                    />\n                    <kbd className=\"hidden lg:inline-flex items-center px-2 py-1 text-xs font-medium text-sky-600 bg-white rounded border border-white/30\">\n                      ⌘K\n                    </kbd>\n                  </div>\n                </div>\n\n                {/* Right side */}\n                <div className=\"flex items-center space-x-3\">\n                  {/* Quick Actions */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"hover:bg-white/20 text-white\">\n                    <HelpCircle className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Notifications */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"relative hover:bg-white/20 text-white\">\n                    <Bell className=\"h-5 w-5\" />\n                    <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-yellow-500 text-black text-xs rounded-full flex items-center justify-center font-semibold\">\n                      5\n                    </span>\n                  </Button>\n\n                  {/* User Menu */}\n                  <div className=\"relative\">\n                    <Button\n                      variant=\"ghost\"\n                      onClick={() => setUserMenuOpen(!userMenuOpen)}\n                      className=\"flex items-center space-x-3 px-3 py-2 hover:bg-white/20 rounded-lg text-white\"\n                    >\n                      <div className=\"hidden sm:block text-right\">\n                        <p className=\"text-sm font-medium text-white\">\n                          {user?.firstName} {user?.lastName}\n                        </p>\n                        <div className=\"flex items-center justify-end space-x-1\">\n                          <div className=\"bg-yellow-500 px-2 py-0.5 rounded-full\">\n                            <span className=\"text-xs font-medium text-black capitalize\">\n                              {user?.role}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"h-10 w-10 bg-white/20 rounded-full flex items-center justify-center shadow-sm border border-white/30\">\n                        <User className=\"h-5 w-5 text-white\" />\n                      </div>\n\n                      <ChevronDown className=\"h-4 w-4 text-white/70\" />\n                    </Button>\n\n                    {/* User Dropdown Menu */}\n                    {userMenuOpen && (\n                      <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-sky-200 py-2 z-50\">\n                        <div className=\"px-4 py-2 border-b border-sky-100\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {user?.firstName} {user?.lastName}\n                          </p>\n                          <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                        </div>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <User className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Profile\n                        </Button>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <Settings className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Settings\n                        </Button>\n\n                        <div className=\"border-t border-sky-100 mt-2 pt-2\">\n                          <Button\n                            variant=\"ghost\"\n                            className=\"w-full justify-start px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                            onClick={handleLogout}\n                          >\n                            <LogOut className=\"h-4 w-4 mr-3\" />\n                            Logout\n                          </Button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </header>\n\n          {/* Main Content Area */}\n          <main className=\"flex-1 overflow-y-auto bg-gray-50\">\n            {children}\n          </main>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAce,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,OAAO,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,mIAAA,CAAA,aAAU;IAEtC,MAAM,eAAe;QACnB,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB;QACpB,oBAAoB,CAAC;IACvB;IAEA,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BAEb,6WAAC,uIAAA,CAAA,UAAO;oBAAC,WAAW;oBAAkB,UAAU;;;;;;8BAGhD,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC;4BAAO,WAAU;sCAChB,cAAA,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDAEb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DAEV,cAAA,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAIlB,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,0RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6WAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;sEAEZ,6WAAC;4DAAI,WAAU;sEAAwH;;;;;;;;;;;;;;;;;;sDAO3I,6WAAC;4CAAI,WAAU;;8DAEb,6WAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;8DAC5C,cAAA,6WAAC,kTAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAIxB,6WAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;;sEAC5C,6WAAC,sRAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6WAAC;4DAAK,WAAU;sEAAgI;;;;;;;;;;;;8DAMlJ,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS,IAAM,gBAAgB,CAAC;4DAChC,WAAU;;8EAEV,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,6WAAC;4EAAI,WAAU;sFACb,cAAA,6WAAC;gFAAI,WAAU;0FACb,cAAA,6WAAC;oFAAK,WAAU;8FACb,MAAM;;;;;;;;;;;;;;;;;;;;;;8EAMf,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAGlB,6WAAC,wSAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;;wDAIxB,8BACC,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,6WAAC;4EAAE,WAAU;sFAAyB,MAAM;;;;;;;;;;;;8EAG9C,6WAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,6WAAC,sRAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIhD,6WAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,6WAAC,8RAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIpD,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,WAAU;wEACV,SAAS;;0FAET,6WAAC,8RAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAarD,6WAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1864, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/forms/property/LocationPicker.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport { MapPin, Navigation, Loader2, CheckCircle, AlertCircle } from 'lucide-react'\nimport { toast } from 'sonner'\n\ninterface LocationData {\n  address: string\n  city: string\n  state: string\n  pincode: string\n  coordinates?: {\n    latitude: number\n    longitude: number\n  }\n}\n\ninterface LocationPickerProps {\n  value: LocationData\n  onChange: (location: LocationData) => void\n  errors?: {\n    address?: string[]\n    city?: string[]\n    state?: string[]\n    pincode?: string[]\n    coordinates?: string[]\n  }\n}\n\nexport default function LocationPicker({ value, onChange, errors }: LocationPickerProps) {\n  const [isDetectingLocation, setIsDetectingLocation] = useState(false)\n  const [locationError, setLocationError] = useState<string | null>(null)\n\n  const handleInputChange = (field: keyof LocationData, inputValue: string) => {\n    onChange({\n      ...value,\n      [field]: inputValue\n    })\n  }\n\n  const detectCurrentLocation = async () => {\n    if (!navigator.geolocation) {\n      setLocationError('Geolocation is not supported by this browser')\n      toast.error('Geolocation not supported')\n      return\n    }\n\n    setIsDetectingLocation(true)\n    setLocationError(null)\n\n    navigator.geolocation.getCurrentPosition(\n      async (position) => {\n        try {\n          const { latitude, longitude } = position.coords\n          \n          // Update coordinates\n          onChange({\n            ...value,\n            coordinates: { latitude, longitude }\n          })\n\n          // Try to get address from coordinates using reverse geocoding\n          // This would typically use a geocoding service like Google Maps API\n          toast.success('Location detected successfully')\n          \n        } catch (error) {\n          console.error('Error getting address:', error)\n          toast.error('Could not get address from location')\n        } finally {\n          setIsDetectingLocation(false)\n        }\n      },\n      (error) => {\n        setIsDetectingLocation(false)\n        let errorMessage = 'Failed to get location'\n        \n        switch (error.code) {\n          case error.PERMISSION_DENIED:\n            errorMessage = 'Location access denied by user'\n            break\n          case error.POSITION_UNAVAILABLE:\n            errorMessage = 'Location information unavailable'\n            break\n          case error.TIMEOUT:\n            errorMessage = 'Location request timed out'\n            break\n        }\n        \n        setLocationError(errorMessage)\n        toast.error(errorMessage)\n      },\n      {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 60000\n      }\n    )\n  }\n\n  const validatePincode = async (pincode: string) => {\n    if (pincode.length === 6) {\n      // Here you could integrate with a pincode API to get city/state\n      // For now, we'll just validate the format\n      const isValid = /^[0-9]{6}$/.test(pincode)\n      if (!isValid) {\n        toast.error('Invalid pincode format')\n      }\n    }\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <MapPin className=\"h-5 w-5\" />\n          Property Location\n        </CardTitle>\n        <CardDescription>\n          Enter the complete address details for the property\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Address Field */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"address\">Complete Address *</Label>\n          <Input\n            id=\"address\"\n            placeholder=\"Enter complete address with landmarks\"\n            value={value.address}\n            onChange={(e) => handleInputChange('address', e.target.value)}\n            className={errors?.address ? 'border-red-500' : ''}\n          />\n          {errors?.address && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {errors.address[0]}\n            </p>\n          )}\n        </div>\n\n        {/* City and State Row */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"city\">City *</Label>\n            <Input\n              id=\"city\"\n              placeholder=\"Enter city\"\n              value={value.city}\n              onChange={(e) => handleInputChange('city', e.target.value)}\n              className={errors?.city ? 'border-red-500' : ''}\n            />\n            {errors?.city && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.city[0]}\n              </p>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"state\">State *</Label>\n            <Input\n              id=\"state\"\n              placeholder=\"Enter state\"\n              value={value.state}\n              onChange={(e) => handleInputChange('state', e.target.value)}\n              className={errors?.state ? 'border-red-500' : ''}\n            />\n            {errors?.state && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.state[0]}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Pincode Field */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"pincode\">Pincode *</Label>\n          <Input\n            id=\"pincode\"\n            placeholder=\"Enter 6-digit pincode\"\n            value={value.pincode}\n            onChange={(e) => {\n              handleInputChange('pincode', e.target.value)\n              if (e.target.value.length === 6) {\n                validatePincode(e.target.value)\n              }\n            }}\n            maxLength={6}\n            className={errors?.pincode ? 'border-red-500' : ''}\n          />\n          {errors?.pincode && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {errors.pincode[0]}\n            </p>\n          )}\n        </div>\n\n        {/* Location Detection */}\n        <div className=\"space-y-3\">\n          <div className=\"flex items-center justify-between\">\n            <Label>GPS Coordinates</Label>\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={detectCurrentLocation}\n              disabled={isDetectingLocation}\n              className=\"flex items-center gap-2\"\n            >\n              {isDetectingLocation ? (\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\n              ) : (\n                <Navigation className=\"h-4 w-4\" />\n              )}\n              {isDetectingLocation ? 'Detecting...' : 'Detect Location'}\n            </Button>\n          </div>\n\n          {value.coordinates && (\n            <div className=\"flex items-center gap-2\">\n              <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n                <CheckCircle className=\"h-3 w-3\" />\n                Lat: {value.coordinates.latitude.toFixed(6)}\n              </Badge>\n              <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n                <CheckCircle className=\"h-3 w-3\" />\n                Lng: {value.coordinates.longitude.toFixed(6)}\n              </Badge>\n            </div>\n          )}\n\n          {locationError && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {locationError}\n            </p>\n          )}\n        </div>\n\n        {/* Manual Coordinates Input */}\n        {!value.coordinates && (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"latitude\">Latitude (Optional)</Label>\n              <Input\n                id=\"latitude\"\n                type=\"number\"\n                step=\"any\"\n                placeholder=\"e.g., 28.6139\"\n                onChange={(e) => {\n                  const lat = parseFloat(e.target.value)\n                  if (!isNaN(lat)) {\n                    onChange({\n                      ...value,\n                      coordinates: {\n                        latitude: lat,\n                        longitude: value.coordinates?.longitude || 0\n                      }\n                    })\n                  }\n                }}\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"longitude\">Longitude (Optional)</Label>\n              <Input\n                id=\"longitude\"\n                type=\"number\"\n                step=\"any\"\n                placeholder=\"e.g., 77.2090\"\n                onChange={(e) => {\n                  const lng = parseFloat(e.target.value)\n                  if (!isNaN(lng)) {\n                    onChange({\n                      ...value,\n                      coordinates: {\n                        latitude: value.coordinates?.latitude || 0,\n                        longitude: lng\n                      }\n                    })\n                  }\n                }}\n              />\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AATA;;;;;;;;;;AAkCe,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAuB;IACrF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,MAAM,oBAAoB,CAAC,OAA2B;QACpD,SAAS;YACP,GAAG,KAAK;YACR,CAAC,MAAM,EAAE;QACX;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,UAAU,WAAW,EAAE;YAC1B,iBAAiB;YACjB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,uBAAuB;QACvB,iBAAiB;QAEjB,UAAU,WAAW,CAAC,kBAAkB,CACtC,OAAO;YACL,IAAI;gBACF,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,SAAS,MAAM;gBAE/C,qBAAqB;gBACrB,SAAS;oBACP,GAAG,KAAK;oBACR,aAAa;wBAAE;wBAAU;oBAAU;gBACrC;gBAEA,8DAA8D;gBAC9D,oEAAoE;gBACpE,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEhB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,uBAAuB;YACzB;QACF,GACA,CAAC;YACC,uBAAuB;YACvB,IAAI,eAAe;YAEnB,OAAQ,MAAM,IAAI;gBAChB,KAAK,MAAM,iBAAiB;oBAC1B,eAAe;oBACf;gBACF,KAAK,MAAM,oBAAoB;oBAC7B,eAAe;oBACf;gBACF,KAAK,MAAM,OAAO;oBAChB,eAAe;oBACf;YACJ;YAEA,iBAAiB;YACjB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,GACA;YACE,oBAAoB;YACpB,SAAS;YACT,YAAY;QACd;IAEJ;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI,QAAQ,MAAM,KAAK,GAAG;YACxB,gEAAgE;YAChE,0CAA0C;YAC1C,MAAM,UAAU,aAAa,IAAI,CAAC;YAClC,IAAI,CAAC,SAAS;gBACZ,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;IACF;IAEA,qBACE,6WAAC,gIAAA,CAAA,OAAI;;0BACH,6WAAC,gIAAA,CAAA,aAAU;;kCACT,6WAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6WAAC,8RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGhC,6WAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6WAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAU;;;;;;0CACzB,6WAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACZ,OAAO,MAAM,OAAO;gCACpB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC5D,WAAW,QAAQ,UAAU,mBAAmB;;;;;;4BAEjD,QAAQ,yBACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,OAAO,CAAC,EAAE;;;;;;;;;;;;;kCAMxB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAO;;;;;;kDACtB,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,MAAM,IAAI;wCACjB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACzD,WAAW,QAAQ,OAAO,mBAAmB;;;;;;oCAE9C,QAAQ,sBACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,IAAI,CAAC,EAAE;;;;;;;;;;;;;0CAKrB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;kDACvB,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,MAAM,KAAK;wCAClB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAW,QAAQ,QAAQ,mBAAmB;;;;;;oCAE/C,QAAQ,uBACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,KAAK,CAAC,EAAE;;;;;;;;;;;;;;;;;;;kCAOxB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAU;;;;;;0CACzB,6WAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACZ,OAAO,MAAM,OAAO;gCACpB,UAAU,CAAC;oCACT,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC3C,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;wCAC/B,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAChC;gCACF;gCACA,WAAW;gCACX,WAAW,QAAQ,UAAU,mBAAmB;;;;;;4BAEjD,QAAQ,yBACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,OAAO,CAAC,EAAE;;;;;;;;;;;;;kCAMxB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6WAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;;4CAET,oCACC,6WAAC,qSAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,6WAAC,kSAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAEvB,sBAAsB,iBAAiB;;;;;;;;;;;;;4BAI3C,MAAM,WAAW,kBAChB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,6WAAC,+SAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAY;4CAC7B,MAAM,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC;;;;;;;kDAE3C,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,6WAAC,+SAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAY;4CAC7B,MAAM,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC;;;;;;;;;;;;;4BAK/C,+BACC,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB;;;;;;;;;;;;;oBAMN,CAAC,MAAM,WAAW,kBACjB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAW;;;;;;kDAC1B,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,aAAY;wCACZ,UAAU,CAAC;4CACT,MAAM,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CACrC,IAAI,CAAC,MAAM,MAAM;gDACf,SAAS;oDACP,GAAG,KAAK;oDACR,aAAa;wDACX,UAAU;wDACV,WAAW,MAAM,WAAW,EAAE,aAAa;oDAC7C;gDACF;4CACF;wCACF;;;;;;;;;;;;0CAIJ,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAY;;;;;;kDAC3B,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,aAAY;wCACZ,UAAU,CAAC;4CACT,MAAM,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CACrC,IAAI,CAAC,MAAM,MAAM;gDACf,SAAS;oDACP,GAAG,KAAK;oDACR,aAAa;wDACX,UAAU,MAAM,WAAW,EAAE,YAAY;wDACzC,WAAW;oDACb;gDACF;4CACF;wCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 2414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,6QAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,6QAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,6QAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6WAAC,6QAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,6QAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2450, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/forms/property/FinancialDetails.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { DollarSign, TrendingUp, Calculator, AlertCircle, Info, Users, Percent } from 'lucide-react'\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'\n\ninterface FinancialData {\n  expectedReturns: number\n  maturityPeriodMonths: number\n  totalStocks: number\n  pricePerStock: number\n  availableStocks: number\n  minimumInvestment?: number\n  maximumInvestment?: number\n  stockPrefix: string\n  stockStartNumber: number\n  referralCommissionRate: number\n  salesCommissionRate: number\n  referralCommissionPerStock: number\n  salesCommissionPerStock: number\n  commissionType: 'percentage' | 'fixed'\n}\n\ninterface FinancialDetailsProps {\n  value: FinancialData\n  onChange: (financial: FinancialData) => void\n  errors?: {\n    expectedReturns?: string[]\n    maturityPeriodMonths?: string[]\n    totalStocks?: string[]\n    pricePerStock?: string[]\n    availableStocks?: string[]\n    minimumInvestment?: string[]\n    maximumInvestment?: string[]\n    stockPrefix?: string[]\n    stockStartNumber?: string[]\n    referralCommissionRate?: string[]\n    salesCommissionRate?: string[]\n    referralCommissionPerStock?: string[]\n    salesCommissionPerStock?: string[]\n    commissionType?: string[]\n  }\n}\n\nexport default function FinancialDetails({ value, onChange, errors }: FinancialDetailsProps) {\n  const handleInputChange = (field: keyof FinancialData, inputValue: string | number) => {\n    if (field === 'stockPrefix' || field === 'commissionType') {\n      onChange({\n        ...value,\n        [field]: inputValue\n      })\n    } else {\n      const numericValue = typeof inputValue === 'string' ? parseFloat(inputValue) || 0 : inputValue\n      onChange({\n        ...value,\n        [field]: numericValue\n      })\n    }\n  }\n\n  // Calculate derived values\n  const totalPropertyValue = value.totalStocks * value.pricePerStock\n  const minimumInvestmentAmount = value.minimumInvestment || value.pricePerStock\n  const maximumInvestmentAmount = value.maximumInvestment || totalPropertyValue\n  const maturityYears = value.maturityPeriodMonths / 12\n\n  // Calculate commission amounts\n  const totalReferralCommission = value.commissionType === 'percentage'\n    ? (value.referralCommissionRate / 100) * totalPropertyValue\n    : value.referralCommissionPerStock * value.totalStocks\n\n  const totalSalesCommission = value.commissionType === 'percentage'\n    ? (value.salesCommissionRate / 100) * totalPropertyValue\n    : value.salesCommissionPerStock * value.totalStocks\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <DollarSign className=\"h-5 w-5\" />\n          Financial Details\n        </CardTitle>\n        <CardDescription>\n          Configure investment parameters and expected returns\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        {/* Returns and Maturity */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <Label htmlFor=\"expectedReturns\">Expected Returns (% p.a.) *</Label>\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger>\n                    <Info className=\"h-4 w-4 text-muted-foreground\" />\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Annual percentage return expected from this investment</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n            <div className=\"relative\">\n              <Input\n                id=\"expectedReturns\"\n                type=\"number\"\n                step=\"0.1\"\n                min=\"0\"\n                max=\"100\"\n                placeholder=\"e.g., 12.5\"\n                value={value.expectedReturns || ''}\n                onChange={(e) => handleInputChange('expectedReturns', e.target.value)}\n                className={errors?.expectedReturns ? 'border-red-500' : ''}\n              />\n              <TrendingUp className=\"absolute right-3 top-3 h-4 w-4 text-muted-foreground\" />\n            </div>\n            {errors?.expectedReturns && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.expectedReturns[0]}\n              </p>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <Label htmlFor=\"maturityPeriodMonths\">Maturity Period (Months) *</Label>\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger>\n                    <Info className=\"h-4 w-4 text-muted-foreground\" />\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Duration of the investment in months</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n            <Input\n              id=\"maturityPeriodMonths\"\n              type=\"number\"\n              min=\"1\"\n              max=\"600\"\n              placeholder=\"e.g., 36\"\n              value={value.maturityPeriodMonths || ''}\n              onChange={(e) => handleInputChange('maturityPeriodMonths', e.target.value)}\n              className={errors?.maturityPeriodMonths ? 'border-red-500' : ''}\n            />\n            {errors?.maturityPeriodMonths && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.maturityPeriodMonths[0]}\n              </p>\n            )}\n            {value.maturityPeriodMonths > 0 && (\n              <p className=\"text-sm text-muted-foreground\">\n                ≈ {maturityYears.toFixed(1)} years\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Stock Configuration */}\n        <div className=\"space-y-4\">\n          <h4 className=\"font-medium flex items-center gap-2\">\n            <Calculator className=\"h-4 w-4\" />\n            Stock Configuration\n          </h4>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"totalStocks\">Total Stocks *</Label>\n              <Input\n                id=\"totalStocks\"\n                type=\"number\"\n                min=\"1\"\n                placeholder=\"e.g., 1000\"\n                value={value.totalStocks || ''}\n                onChange={(e) => handleInputChange('totalStocks', e.target.value)}\n                className={errors?.totalStocks ? 'border-red-500' : ''}\n              />\n              {errors?.totalStocks && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.totalStocks[0]}\n                </p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"pricePerStock\">Price per Stock (₹) *</Label>\n              <Input\n                id=\"pricePerStock\"\n                type=\"number\"\n                step=\"0.01\"\n                min=\"0.01\"\n                placeholder=\"e.g., 1000\"\n                value={value.pricePerStock || ''}\n                onChange={(e) => handleInputChange('pricePerStock', e.target.value)}\n                className={errors?.pricePerStock ? 'border-red-500' : ''}\n              />\n              {errors?.pricePerStock && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.pricePerStock[0]}\n                </p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"availableStocks\">Available Stocks *</Label>\n              <Input\n                id=\"availableStocks\"\n                type=\"number\"\n                min=\"0\"\n                max={value.totalStocks}\n                placeholder=\"e.g., 800\"\n                value={value.availableStocks || ''}\n                onChange={(e) => handleInputChange('availableStocks', e.target.value)}\n                className={errors?.availableStocks ? 'border-red-500' : ''}\n              />\n              {errors?.availableStocks && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.availableStocks[0]}\n                </p>\n              )}\n              {value.availableStocks > value.totalStocks && (\n                <p className=\"text-sm text-amber-600\">\n                  Available stocks cannot exceed total stocks\n                </p>\n              )}\n            </div>\n          </div>\n\n          {/* Stock Numbering */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"stockPrefix\">Stock Prefix *</Label>\n              <Input\n                id=\"stockPrefix\"\n                placeholder=\"e.g., PROP, STK, INV\"\n                value={value.stockPrefix || ''}\n                onChange={(e) => handleInputChange('stockPrefix', e.target.value.toUpperCase())}\n                className={errors?.stockPrefix ? 'border-red-500' : ''}\n                maxLength={10}\n              />\n              {errors?.stockPrefix && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.stockPrefix[0]}\n                </p>\n              )}\n              <p className=\"text-xs text-muted-foreground\">\n                Stock numbers will be: {value.stockPrefix || 'PREFIX'}-{String(value.stockStartNumber || 1).padStart(4, '0')} to {value.stockPrefix || 'PREFIX'}-{String((value.stockStartNumber || 1) + (value.totalStocks || 1) - 1).padStart(4, '0')}\n              </p>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"stockStartNumber\">Starting Stock Number *</Label>\n              <Input\n                id=\"stockStartNumber\"\n                type=\"number\"\n                min=\"1\"\n                placeholder=\"e.g., 1\"\n                value={value.stockStartNumber || ''}\n                onChange={(e) => handleInputChange('stockStartNumber', e.target.value)}\n                className={errors?.stockStartNumber ? 'border-red-500' : ''}\n              />\n              {errors?.stockStartNumber && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.stockStartNumber[0]}\n                </p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Investment Limits */}\n        <div className=\"space-y-4\">\n          <h4 className=\"font-medium flex items-center gap-2\">\n            <DollarSign className=\"h-4 w-4\" />\n            Investment Limits\n          </h4>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center gap-2\">\n                <Label htmlFor=\"minimumInvestment\">Minimum Investment (₹)</Label>\n                <TooltipProvider>\n                  <Tooltip>\n                    <TooltipTrigger>\n                      <Info className=\"h-4 w-4 text-muted-foreground\" />\n                    </TooltipTrigger>\n                    <TooltipContent>\n                      <p>Minimum amount required to invest. Defaults to price per stock if not specified.</p>\n                    </TooltipContent>\n                  </Tooltip>\n                </TooltipProvider>\n              </div>\n              <Input\n                id=\"minimumInvestment\"\n                type=\"number\"\n                step=\"0.01\"\n                min={value.pricePerStock}\n                placeholder={`Default: ₹${value.pricePerStock || 0}`}\n                value={value.minimumInvestment || ''}\n                onChange={(e) => handleInputChange('minimumInvestment', e.target.value)}\n                className={errors?.minimumInvestment ? 'border-red-500' : ''}\n              />\n              {errors?.minimumInvestment && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.minimumInvestment[0]}\n                </p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center gap-2\">\n                <Label htmlFor=\"maximumInvestment\">Maximum Investment (₹)</Label>\n                <TooltipProvider>\n                  <Tooltip>\n                    <TooltipTrigger>\n                      <Info className=\"h-4 w-4 text-muted-foreground\" />\n                    </TooltipTrigger>\n                    <TooltipContent>\n                      <p>Maximum amount a single investor can invest. Defaults to total property value.</p>\n                    </TooltipContent>\n                  </Tooltip>\n                </TooltipProvider>\n              </div>\n              <Input\n                id=\"maximumInvestment\"\n                type=\"number\"\n                step=\"0.01\"\n                min={value.minimumInvestment || value.pricePerStock}\n                max={totalPropertyValue}\n                placeholder={`Default: ₹${totalPropertyValue.toLocaleString('en-IN')}`}\n                value={value.maximumInvestment || ''}\n                onChange={(e) => handleInputChange('maximumInvestment', e.target.value)}\n                className={errors?.maximumInvestment ? 'border-red-500' : ''}\n              />\n              {errors?.maximumInvestment && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.maximumInvestment[0]}\n                </p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Commission Configuration */}\n        <div className=\"space-y-4\">\n          <h4 className=\"font-medium flex items-center gap-2\">\n            <Users className=\"h-4 w-4\" />\n            Commission Configuration\n          </h4>\n\n          <div className=\"space-y-4\">\n            {/* Commission Type */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"commissionType\">Commission Type *</Label>\n              <Select\n                value={value.commissionType || 'percentage'}\n                onValueChange={(val) => handleInputChange('commissionType', val)}\n              >\n                <SelectTrigger className={errors?.commissionType ? 'border-red-500' : ''}>\n                  <SelectValue placeholder=\"Select commission type\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"percentage\">Percentage Based</SelectItem>\n                  <SelectItem value=\"fixed\">Fixed Amount per Stock</SelectItem>\n                </SelectContent>\n              </Select>\n              {errors?.commissionType && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.commissionType[0]}\n                </p>\n              )}\n            </div>\n\n            {/* Commission Rates */}\n            {value.commissionType === 'percentage' ? (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center gap-2\">\n                    <Label htmlFor=\"referralCommissionRate\">Referral Commission (%)</Label>\n                    <TooltipProvider>\n                      <Tooltip>\n                        <TooltipTrigger>\n                          <Info className=\"h-4 w-4 text-muted-foreground\" />\n                        </TooltipTrigger>\n                        <TooltipContent>\n                          <p>Percentage commission for referral partners</p>\n                        </TooltipContent>\n                      </Tooltip>\n                    </TooltipProvider>\n                  </div>\n                  <div className=\"relative\">\n                    <Input\n                      id=\"referralCommissionRate\"\n                      type=\"number\"\n                      step=\"0.1\"\n                      min=\"0\"\n                      max=\"100\"\n                      placeholder=\"e.g., 2.5\"\n                      value={value.referralCommissionRate || ''}\n                      onChange={(e) => handleInputChange('referralCommissionRate', e.target.value)}\n                      className={errors?.referralCommissionRate ? 'border-red-500' : ''}\n                    />\n                    <Percent className=\"absolute right-3 top-3 h-4 w-4 text-muted-foreground\" />\n                  </div>\n                  {errors?.referralCommissionRate && (\n                    <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                      <AlertCircle className=\"h-4 w-4\" />\n                      {errors.referralCommissionRate[0]}\n                    </p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center gap-2\">\n                    <Label htmlFor=\"salesCommissionRate\">Sales Commission (%)</Label>\n                    <TooltipProvider>\n                      <Tooltip>\n                        <TooltipTrigger>\n                          <Info className=\"h-4 w-4 text-muted-foreground\" />\n                        </TooltipTrigger>\n                        <TooltipContent>\n                          <p>Percentage commission for sales team</p>\n                        </TooltipContent>\n                      </Tooltip>\n                    </TooltipProvider>\n                  </div>\n                  <div className=\"relative\">\n                    <Input\n                      id=\"salesCommissionRate\"\n                      type=\"number\"\n                      step=\"0.1\"\n                      min=\"0\"\n                      max=\"100\"\n                      placeholder=\"e.g., 1.5\"\n                      value={value.salesCommissionRate || ''}\n                      onChange={(e) => handleInputChange('salesCommissionRate', e.target.value)}\n                      className={errors?.salesCommissionRate ? 'border-red-500' : ''}\n                    />\n                    <Percent className=\"absolute right-3 top-3 h-4 w-4 text-muted-foreground\" />\n                  </div>\n                  {errors?.salesCommissionRate && (\n                    <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                      <AlertCircle className=\"h-4 w-4\" />\n                      {errors.salesCommissionRate[0]}\n                    </p>\n                  )}\n                </div>\n              </div>\n            ) : (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center gap-2\">\n                    <Label htmlFor=\"referralCommissionPerStock\">Referral Commission per Stock (₹)</Label>\n                    <TooltipProvider>\n                      <Tooltip>\n                        <TooltipTrigger>\n                          <Info className=\"h-4 w-4 text-muted-foreground\" />\n                        </TooltipTrigger>\n                        <TooltipContent>\n                          <p>Fixed amount commission per stock for referral partners</p>\n                        </TooltipContent>\n                      </Tooltip>\n                    </TooltipProvider>\n                  </div>\n                  <Input\n                    id=\"referralCommissionPerStock\"\n                    type=\"number\"\n                    step=\"0.01\"\n                    min=\"0\"\n                    placeholder=\"e.g., 25\"\n                    value={value.referralCommissionPerStock || ''}\n                    onChange={(e) => handleInputChange('referralCommissionPerStock', e.target.value)}\n                    className={errors?.referralCommissionPerStock ? 'border-red-500' : ''}\n                  />\n                  {errors?.referralCommissionPerStock && (\n                    <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                      <AlertCircle className=\"h-4 w-4\" />\n                      {errors.referralCommissionPerStock[0]}\n                    </p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center gap-2\">\n                    <Label htmlFor=\"salesCommissionPerStock\">Sales Commission per Stock (₹)</Label>\n                    <TooltipProvider>\n                      <Tooltip>\n                        <TooltipTrigger>\n                          <Info className=\"h-4 w-4 text-muted-foreground\" />\n                        </TooltipTrigger>\n                        <TooltipContent>\n                          <p>Fixed amount commission per stock for sales team</p>\n                        </TooltipContent>\n                      </Tooltip>\n                    </TooltipProvider>\n                  </div>\n                  <Input\n                    id=\"salesCommissionPerStock\"\n                    type=\"number\"\n                    step=\"0.01\"\n                    min=\"0\"\n                    placeholder=\"e.g., 15\"\n                    value={value.salesCommissionPerStock || ''}\n                    onChange={(e) => handleInputChange('salesCommissionPerStock', e.target.value)}\n                    className={errors?.salesCommissionPerStock ? 'border-red-500' : ''}\n                  />\n                  {errors?.salesCommissionPerStock && (\n                    <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                      <AlertCircle className=\"h-4 w-4\" />\n                      {errors.salesCommissionPerStock[0]}\n                    </p>\n                  )}\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Calculated Values */}\n        {(value.totalStocks > 0 && value.pricePerStock > 0) && (\n          <div className=\"space-y-4 p-4 bg-muted/50 rounded-lg\">\n            <h4 className=\"font-medium text-sm\">Calculated Values</h4>\n\n            {/* Property Values */}\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n              <div className=\"text-center\">\n                <Badge variant=\"secondary\" className=\"w-full justify-center\">\n                  Total Value\n                </Badge>\n                <p className=\"text-sm font-medium mt-1\">\n                  ₹{totalPropertyValue.toLocaleString('en-IN')}\n                </p>\n              </div>\n\n              <div className=\"text-center\">\n                <Badge variant=\"secondary\" className=\"w-full justify-center\">\n                  Min Investment\n                </Badge>\n                <p className=\"text-sm font-medium mt-1\">\n                  ₹{minimumInvestmentAmount.toLocaleString('en-IN')}\n                </p>\n              </div>\n\n              <div className=\"text-center\">\n                <Badge variant=\"secondary\" className=\"w-full justify-center\">\n                  Max Investment\n                </Badge>\n                <p className=\"text-sm font-medium mt-1\">\n                  ₹{maximumInvestmentAmount.toLocaleString('en-IN')}\n                </p>\n              </div>\n\n              <div className=\"text-center\">\n                <Badge variant=\"secondary\" className=\"w-full justify-center\">\n                  Sold Percentage\n                </Badge>\n                <p className=\"text-sm font-medium mt-1\">\n                  {value.totalStocks > 0 ? (((value.totalStocks - value.availableStocks) / value.totalStocks) * 100).toFixed(1) : 0}%\n                </p>\n              </div>\n            </div>\n\n            {/* Commission Values */}\n            {(value.commissionType && (value.referralCommissionRate > 0 || value.salesCommissionRate > 0 || value.referralCommissionPerStock > 0 || value.salesCommissionPerStock > 0)) && (\n              <div>\n                <h5 className=\"font-medium text-sm mb-2\">Commission Breakdown</h5>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n                  <div className=\"text-center\">\n                    <Badge variant=\"outline\" className=\"w-full justify-center\">\n                      Referral Commission\n                    </Badge>\n                    <p className=\"text-sm font-medium mt-1\">\n                      ₹{totalReferralCommission.toLocaleString('en-IN')}\n                    </p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      {value.commissionType === 'percentage'\n                        ? `${value.referralCommissionRate}% of total`\n                        : `₹${value.referralCommissionPerStock} × ${value.totalStocks} stocks`\n                      }\n                    </p>\n                  </div>\n\n                  <div className=\"text-center\">\n                    <Badge variant=\"outline\" className=\"w-full justify-center\">\n                      Sales Commission\n                    </Badge>\n                    <p className=\"text-sm font-medium mt-1\">\n                      ₹{totalSalesCommission.toLocaleString('en-IN')}\n                    </p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      {value.commissionType === 'percentage'\n                        ? `${value.salesCommissionRate}% of total`\n                        : `₹${value.salesCommissionPerStock} × ${value.totalStocks} stocks`\n                      }\n                    </p>\n                  </div>\n\n                  <div className=\"text-center\">\n                    <Badge variant=\"outline\" className=\"w-full justify-center\">\n                      Total Commission\n                    </Badge>\n                    <p className=\"text-sm font-medium mt-1\">\n                      ₹{(totalReferralCommission + totalSalesCommission).toLocaleString('en-IN')}\n                    </p>\n                  </div>\n\n                  <div className=\"text-center\">\n                    <Badge variant=\"outline\" className=\"w-full justify-center\">\n                      Net Property Value\n                    </Badge>\n                    <p className=\"text-sm font-medium mt-1\">\n                      ₹{(totalPropertyValue - totalReferralCommission - totalSalesCommission).toLocaleString('en-IN')}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Stock Range Preview */}\n            {value.stockPrefix && value.stockStartNumber && value.totalStocks && (\n              <div>\n                <h5 className=\"font-medium text-sm mb-2\">Stock Number Range</h5>\n                <div className=\"flex items-center justify-center gap-2\">\n                  <Badge variant=\"outline\">\n                    {value.stockPrefix}-{String(value.stockStartNumber).padStart(4, '0')}\n                  </Badge>\n                  <span className=\"text-muted-foreground\">to</span>\n                  <Badge variant=\"outline\">\n                    {value.stockPrefix}-{String(value.stockStartNumber + value.totalStocks - 1).padStart(4, '0')}\n                  </Badge>\n                  <span className=\"text-muted-foreground\">({value.totalStocks} stocks)</span>\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AATA;;;;;;;;;AAiDe,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAyB;IACzF,MAAM,oBAAoB,CAAC,OAA4B;QACrD,IAAI,UAAU,iBAAiB,UAAU,kBAAkB;YACzD,SAAS;gBACP,GAAG,KAAK;gBACR,CAAC,MAAM,EAAE;YACX;QACF,OAAO;YACL,MAAM,eAAe,OAAO,eAAe,WAAW,WAAW,eAAe,IAAI;YACpF,SAAS;gBACP,GAAG,KAAK;gBACR,CAAC,MAAM,EAAE;YACX;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,qBAAqB,MAAM,WAAW,GAAG,MAAM,aAAa;IAClE,MAAM,0BAA0B,MAAM,iBAAiB,IAAI,MAAM,aAAa;IAC9E,MAAM,0BAA0B,MAAM,iBAAiB,IAAI;IAC3D,MAAM,gBAAgB,MAAM,oBAAoB,GAAG;IAEnD,+BAA+B;IAC/B,MAAM,0BAA0B,MAAM,cAAc,KAAK,eACrD,AAAC,MAAM,sBAAsB,GAAG,MAAO,qBACvC,MAAM,0BAA0B,GAAG,MAAM,WAAW;IAExD,MAAM,uBAAuB,MAAM,cAAc,KAAK,eAClD,AAAC,MAAM,mBAAmB,GAAG,MAAO,qBACpC,MAAM,uBAAuB,GAAG,MAAM,WAAW;IAErD,qBACE,6WAAC,gIAAA,CAAA,OAAI;;0BACH,6WAAC,gIAAA,CAAA,aAAU;;kCACT,6WAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6WAAC,sSAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGpC,6WAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6WAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAkB;;;;;;0DACjC,6WAAC,mIAAA,CAAA,kBAAe;0DACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;sEACN,6WAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6WAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,6WAAC;0EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKX,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,aAAY;gDACZ,OAAO,MAAM,eAAe,IAAI;gDAChC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDACpE,WAAW,QAAQ,kBAAkB,mBAAmB;;;;;;0DAE1D,6WAAC,sSAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;oCAEvB,QAAQ,iCACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,eAAe,CAAC,EAAE;;;;;;;;;;;;;0CAKhC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAuB;;;;;;0DACtC,6WAAC,mIAAA,CAAA,kBAAe;0DACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;sEACN,6WAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6WAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,6WAAC;0EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKX,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,aAAY;wCACZ,OAAO,MAAM,oBAAoB,IAAI;wCACrC,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,KAAK;wCACzE,WAAW,QAAQ,uBAAuB,mBAAmB;;;;;;oCAE9D,QAAQ,sCACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,oBAAoB,CAAC,EAAE;;;;;;;oCAGlC,MAAM,oBAAoB,GAAG,mBAC5B,6WAAC;wCAAE,WAAU;;4CAAgC;4CACxC,cAAc,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;;kCAOpC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;;kDACZ,6WAAC,kSAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAIpC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,aAAY;gDACZ,OAAO,MAAM,WAAW,IAAI;gDAC5B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;gDAChE,WAAW,QAAQ,cAAc,mBAAmB;;;;;;4CAErD,QAAQ,6BACP,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,WAAW,CAAC,EAAE;;;;;;;;;;;;;kDAK5B,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;0DAC/B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,KAAI;gDACJ,aAAY;gDACZ,OAAO,MAAM,aAAa,IAAI;gDAC9B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAClE,WAAW,QAAQ,gBAAgB,mBAAmB;;;;;;4CAEvD,QAAQ,+BACP,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,aAAa,CAAC,EAAE;;;;;;;;;;;;;kDAK9B,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAkB;;;;;;0DACjC,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,KAAK,MAAM,WAAW;gDACtB,aAAY;gDACZ,OAAO,MAAM,eAAe,IAAI;gDAChC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDACpE,WAAW,QAAQ,kBAAkB,mBAAmB;;;;;;4CAEzD,QAAQ,iCACP,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,eAAe,CAAC,EAAE;;;;;;;4CAG7B,MAAM,eAAe,GAAG,MAAM,WAAW,kBACxC,6WAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;0CAQ5C,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,MAAM,WAAW,IAAI;gDAC5B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;gDAC5E,WAAW,QAAQ,cAAc,mBAAmB;gDACpD,WAAW;;;;;;4CAEZ,QAAQ,6BACP,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,WAAW,CAAC,EAAE;;;;;;;0DAG1B,6WAAC;gDAAE,WAAU;;oDAAgC;oDACnB,MAAM,WAAW,IAAI;oDAAS;oDAAE,OAAO,MAAM,gBAAgB,IAAI,GAAG,QAAQ,CAAC,GAAG;oDAAK;oDAAK,MAAM,WAAW,IAAI;oDAAS;oDAAE,OAAO,CAAC,MAAM,gBAAgB,IAAI,CAAC,IAAI,CAAC,MAAM,WAAW,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,GAAG;;;;;;;;;;;;;kDAIvO,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAmB;;;;;;0DAClC,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,aAAY;gDACZ,OAAO,MAAM,gBAAgB,IAAI;gDACjC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACrE,WAAW,QAAQ,mBAAmB,mBAAmB;;;;;;4CAE1D,QAAQ,kCACP,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,gBAAgB,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;kCAQrC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;;kDACZ,6WAAC,sSAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAIpC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAoB;;;;;;kEACnC,6WAAC,mIAAA,CAAA,kBAAe;kEACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;8EACN,6WAAC,mIAAA,CAAA,iBAAc;8EACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,6WAAC,mIAAA,CAAA,iBAAc;8EACb,cAAA,6WAAC;kFAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAKX,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,KAAK,MAAM,aAAa;gDACxB,aAAa,CAAC,UAAU,EAAE,MAAM,aAAa,IAAI,GAAG;gDACpD,OAAO,MAAM,iBAAiB,IAAI;gDAClC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;gDACtE,WAAW,QAAQ,oBAAoB,mBAAmB;;;;;;4CAE3D,QAAQ,mCACP,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,iBAAiB,CAAC,EAAE;;;;;;;;;;;;;kDAKlC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAoB;;;;;;kEACnC,6WAAC,mIAAA,CAAA,kBAAe;kEACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;8EACN,6WAAC,mIAAA,CAAA,iBAAc;8EACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,6WAAC,mIAAA,CAAA,iBAAc;8EACb,cAAA,6WAAC;kFAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAKX,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,KAAK,MAAM,iBAAiB,IAAI,MAAM,aAAa;gDACnD,KAAK;gDACL,aAAa,CAAC,UAAU,EAAE,mBAAmB,cAAc,CAAC,UAAU;gDACtE,OAAO,MAAM,iBAAiB,IAAI;gDAClC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;gDACtE,WAAW,QAAQ,oBAAoB,mBAAmB;;;;;;4CAE3D,QAAQ,mCACP,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,iBAAiB,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;kCAQtC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;;kDACZ,6WAAC,wRAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAI/B,6WAAC;gCAAI,WAAU;;kDAEb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,6WAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,MAAM,cAAc,IAAI;gDAC/B,eAAe,CAAC,MAAQ,kBAAkB,kBAAkB;;kEAE5D,6WAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAW,QAAQ,iBAAiB,mBAAmB;kEACpE,cAAA,6WAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6WAAC,kIAAA,CAAA,gBAAa;;0EACZ,6WAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAa;;;;;;0EAC/B,6WAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;;;;;;;;;;;;;4CAG7B,QAAQ,gCACP,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,cAAc,CAAC,EAAE;;;;;;;;;;;;;oCAM9B,MAAM,cAAc,KAAK,6BACxB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAyB;;;;;;0EACxC,6WAAC,mIAAA,CAAA,kBAAe;0EACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;sFACN,6WAAC,mIAAA,CAAA,iBAAc;sFACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;;;;;;sFAElB,6WAAC,mIAAA,CAAA,iBAAc;sFACb,cAAA,6WAAC;0FAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAKX,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,aAAY;gEACZ,OAAO,MAAM,sBAAsB,IAAI;gEACvC,UAAU,CAAC,IAAM,kBAAkB,0BAA0B,EAAE,MAAM,CAAC,KAAK;gEAC3E,WAAW,QAAQ,yBAAyB,mBAAmB;;;;;;0EAEjE,6WAAC,4RAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;oDAEpB,QAAQ,wCACP,6WAAC;wDAAE,WAAU;;0EACX,6WAAC,wSAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DACtB,OAAO,sBAAsB,CAAC,EAAE;;;;;;;;;;;;;0DAKvC,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAsB;;;;;;0EACrC,6WAAC,mIAAA,CAAA,kBAAe;0EACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;sFACN,6WAAC,mIAAA,CAAA,iBAAc;sFACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;;;;;;sFAElB,6WAAC,mIAAA,CAAA,iBAAc;sFACb,cAAA,6WAAC;0FAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAKX,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,aAAY;gEACZ,OAAO,MAAM,mBAAmB,IAAI;gEACpC,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,EAAE,MAAM,CAAC,KAAK;gEACxE,WAAW,QAAQ,sBAAsB,mBAAmB;;;;;;0EAE9D,6WAAC,4RAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;oDAEpB,QAAQ,qCACP,6WAAC;wDAAE,WAAU;;0EACX,6WAAC,wSAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DACtB,OAAO,mBAAmB,CAAC,EAAE;;;;;;;;;;;;;;;;;;6DAMtC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAA6B;;;;;;0EAC5C,6WAAC,mIAAA,CAAA,kBAAe;0EACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;sFACN,6WAAC,mIAAA,CAAA,iBAAc;sFACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;;;;;;sFAElB,6WAAC,mIAAA,CAAA,iBAAc;sFACb,cAAA,6WAAC;0FAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAKX,6WAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,KAAI;wDACJ,aAAY;wDACZ,OAAO,MAAM,0BAA0B,IAAI;wDAC3C,UAAU,CAAC,IAAM,kBAAkB,8BAA8B,EAAE,MAAM,CAAC,KAAK;wDAC/E,WAAW,QAAQ,6BAA6B,mBAAmB;;;;;;oDAEpE,QAAQ,4CACP,6WAAC;wDAAE,WAAU;;0EACX,6WAAC,wSAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DACtB,OAAO,0BAA0B,CAAC,EAAE;;;;;;;;;;;;;0DAK3C,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAA0B;;;;;;0EACzC,6WAAC,mIAAA,CAAA,kBAAe;0EACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;sFACN,6WAAC,mIAAA,CAAA,iBAAc;sFACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;;;;;;sFAElB,6WAAC,mIAAA,CAAA,iBAAc;sFACb,cAAA,6WAAC;0FAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAKX,6WAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,KAAI;wDACJ,aAAY;wDACZ,OAAO,MAAM,uBAAuB,IAAI;wDACxC,UAAU,CAAC,IAAM,kBAAkB,2BAA2B,EAAE,MAAM,CAAC,KAAK;wDAC5E,WAAW,QAAQ,0BAA0B,mBAAmB;;;;;;oDAEjE,QAAQ,yCACP,6WAAC;wDAAE,WAAU;;0EACX,6WAAC,wSAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DACtB,OAAO,uBAAuB,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU9C,MAAM,WAAW,GAAG,KAAK,MAAM,aAAa,GAAG,mBAC/C,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;0CAAsB;;;;;;0CAGpC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAwB;;;;;;0DAG7D,6WAAC;gDAAE,WAAU;;oDAA2B;oDACpC,mBAAmB,cAAc,CAAC;;;;;;;;;;;;;kDAIxC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAwB;;;;;;0DAG7D,6WAAC;gDAAE,WAAU;;oDAA2B;oDACpC,wBAAwB,cAAc,CAAC;;;;;;;;;;;;;kDAI7C,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAwB;;;;;;0DAG7D,6WAAC;gDAAE,WAAU;;oDAA2B;oDACpC,wBAAwB,cAAc,CAAC;;;;;;;;;;;;;kDAI7C,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAwB;;;;;;0DAG7D,6WAAC;gDAAE,WAAU;;oDACV,MAAM,WAAW,GAAG,IAAI,CAAC,AAAC,CAAC,MAAM,WAAW,GAAG,MAAM,eAAe,IAAI,MAAM,WAAW,GAAI,GAAG,EAAE,OAAO,CAAC,KAAK;oDAAE;;;;;;;;;;;;;;;;;;;4BAMtH,MAAM,cAAc,IAAI,CAAC,MAAM,sBAAsB,GAAG,KAAK,MAAM,mBAAmB,GAAG,KAAK,MAAM,0BAA0B,GAAG,KAAK,MAAM,uBAAuB,GAAG,CAAC,mBACvK,6WAAC;;kDACC,6WAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAAwB;;;;;;kEAG3D,6WAAC;wDAAE,WAAU;;4DAA2B;4DACpC,wBAAwB,cAAc,CAAC;;;;;;;kEAE3C,6WAAC;wDAAE,WAAU;kEACV,MAAM,cAAc,KAAK,eACtB,GAAG,MAAM,sBAAsB,CAAC,UAAU,CAAC,GAC3C,CAAC,CAAC,EAAE,MAAM,0BAA0B,CAAC,GAAG,EAAE,MAAM,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAK5E,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAAwB;;;;;;kEAG3D,6WAAC;wDAAE,WAAU;;4DAA2B;4DACpC,qBAAqB,cAAc,CAAC;;;;;;;kEAExC,6WAAC;wDAAE,WAAU;kEACV,MAAM,cAAc,KAAK,eACtB,GAAG,MAAM,mBAAmB,CAAC,UAAU,CAAC,GACxC,CAAC,CAAC,EAAE,MAAM,uBAAuB,CAAC,GAAG,EAAE,MAAM,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAKzE,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAAwB;;;;;;kEAG3D,6WAAC;wDAAE,WAAU;;4DAA2B;4DACpC,CAAC,0BAA0B,oBAAoB,EAAE,cAAc,CAAC;;;;;;;;;;;;;0DAItE,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAAwB;;;;;;kEAG3D,6WAAC;wDAAE,WAAU;;4DAA2B;4DACpC,CAAC,qBAAqB,0BAA0B,oBAAoB,EAAE,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;4BAQhG,MAAM,WAAW,IAAI,MAAM,gBAAgB,IAAI,MAAM,WAAW,kBAC/D,6WAAC;;kDACC,6WAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;;oDACZ,MAAM,WAAW;oDAAC;oDAAE,OAAO,MAAM,gBAAgB,EAAE,QAAQ,CAAC,GAAG;;;;;;;0DAElE,6WAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;;oDACZ,MAAM,WAAW;oDAAC;oDAAE,OAAO,MAAM,gBAAgB,GAAG,MAAM,WAAW,GAAG,GAAG,QAAQ,CAAC,GAAG;;;;;;;0DAE1F,6WAAC;gDAAK,WAAU;;oDAAwB;oDAAE,MAAM,WAAW;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9E", "debugId": null}}, {"offset": {"line": 4246, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/forms/property/ConstructionTimeline.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Badge } from '@/components/ui/badge'\nimport { Calendar, Building, Clock, AlertCircle, CheckCircle, Info } from 'lucide-react'\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'\n\ninterface ConstructionData {\n  constructionStatus: string\n  launchDate: string\n  expectedCompletion: string\n  actualCompletion?: string\n  constructionTimeline?: string\n}\n\ninterface ConstructionTimelineProps {\n  value: ConstructionData\n  onChange: (construction: ConstructionData) => void\n  errors?: {\n    constructionStatus?: string[]\n    launchDate?: string[]\n    expectedCompletion?: string[]\n    actualCompletion?: string[]\n    constructionTimeline?: string[]\n  }\n}\n\nconst constructionStatuses = [\n  { value: 'planning', label: 'Planning', color: 'bg-blue-500' },\n  { value: 'foundation', label: 'Foundation', color: 'bg-orange-500' },\n  { value: 'structure', label: 'Structure', color: 'bg-yellow-500' },\n  { value: 'finishing', label: 'Finishing', color: 'bg-purple-500' },\n  { value: 'completed', label: 'Completed', color: 'bg-green-500' }\n]\n\nexport default function ConstructionTimeline({ value, onChange, errors }: ConstructionTimelineProps) {\n  const handleInputChange = (field: keyof ConstructionData, inputValue: string) => {\n    onChange({\n      ...value,\n      [field]: inputValue\n    })\n  }\n\n  // Calculate project duration\n  const calculateDuration = () => {\n    if (value.launchDate && value.expectedCompletion) {\n      const launch = new Date(value.launchDate)\n      const completion = new Date(value.expectedCompletion)\n      const diffTime = completion.getTime() - launch.getTime()\n      const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30))\n      return diffMonths > 0 ? diffMonths : 0\n    }\n    return 0\n  }\n\n  // Check if dates are valid\n  const isValidDateRange = () => {\n    if (value.launchDate && value.expectedCompletion) {\n      return new Date(value.launchDate) < new Date(value.expectedCompletion)\n    }\n    return true\n  }\n\n  const currentStatus = constructionStatuses.find(status => status.value === value.constructionStatus)\n  const projectDuration = calculateDuration()\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Building className=\"h-5 w-5\" />\n          Construction Timeline\n        </CardTitle>\n        <CardDescription>\n          Configure construction phases and project timeline\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        {/* Construction Status */}\n        <div className=\"space-y-2\">\n          <div className=\"flex items-center gap-2\">\n            <Label htmlFor=\"constructionStatus\">Construction Status *</Label>\n            <TooltipProvider>\n              <Tooltip>\n                <TooltipTrigger>\n                  <Info className=\"h-4 w-4 text-muted-foreground\" />\n                </TooltipTrigger>\n                <TooltipContent>\n                  <p>Current phase of construction</p>\n                </TooltipContent>\n              </Tooltip>\n            </TooltipProvider>\n          </div>\n          <Select\n            value={value.constructionStatus}\n            onValueChange={(val) => handleInputChange('constructionStatus', val)}\n          >\n            <SelectTrigger className={errors?.constructionStatus ? 'border-red-500' : ''}>\n              <SelectValue placeholder=\"Select construction status\" />\n            </SelectTrigger>\n            <SelectContent>\n              {constructionStatuses.map((status) => (\n                <SelectItem key={status.value} value={status.value}>\n                  <div className=\"flex items-center gap-2\">\n                    <div className={`w-3 h-3 rounded-full ${status.color}`} />\n                    {status.label}\n                  </div>\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n          {errors?.constructionStatus && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {errors.constructionStatus[0]}\n            </p>\n          )}\n          {currentStatus && (\n            <div className=\"flex items-center gap-2\">\n              <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n                <div className={`w-2 h-2 rounded-full ${currentStatus.color}`} />\n                Current: {currentStatus.label}\n              </Badge>\n            </div>\n          )}\n        </div>\n\n        {/* Project Dates */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <Label htmlFor=\"launchDate\">Launch Date *</Label>\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger>\n                    <Info className=\"h-4 w-4 text-muted-foreground\" />\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Project launch or start date</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n            <div className=\"relative\">\n              <Input\n                id=\"launchDate\"\n                type=\"date\"\n                value={value.launchDate}\n                onChange={(e) => handleInputChange('launchDate', e.target.value)}\n                className={errors?.launchDate ? 'border-red-500' : ''}\n              />\n              <Calendar className=\"absolute right-3 top-3 h-4 w-4 text-muted-foreground pointer-events-none\" />\n            </div>\n            {errors?.launchDate && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.launchDate[0]}\n              </p>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <Label htmlFor=\"expectedCompletion\">Expected Completion *</Label>\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger>\n                    <Info className=\"h-4 w-4 text-muted-foreground\" />\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Expected project completion date</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n            <div className=\"relative\">\n              <Input\n                id=\"expectedCompletion\"\n                type=\"date\"\n                value={value.expectedCompletion}\n                onChange={(e) => handleInputChange('expectedCompletion', e.target.value)}\n                className={errors?.expectedCompletion ? 'border-red-500' : ''}\n                min={value.launchDate}\n              />\n              <Calendar className=\"absolute right-3 top-3 h-4 w-4 text-muted-foreground pointer-events-none\" />\n            </div>\n            {errors?.expectedCompletion && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.expectedCompletion[0]}\n              </p>\n            )}\n            {!isValidDateRange() && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                Completion date must be after launch date\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Actual Completion (if completed) */}\n        {value.constructionStatus === 'completed' && (\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <Label htmlFor=\"actualCompletion\">Actual Completion Date</Label>\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger>\n                    <Info className=\"h-4 w-4 text-muted-foreground\" />\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Actual date when construction was completed</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n            <div className=\"relative\">\n              <Input\n                id=\"actualCompletion\"\n                type=\"date\"\n                value={value.actualCompletion || ''}\n                onChange={(e) => handleInputChange('actualCompletion', e.target.value)}\n                className={errors?.actualCompletion ? 'border-red-500' : ''}\n                max={new Date().toISOString().split('T')[0]}\n              />\n              <Calendar className=\"absolute right-3 top-3 h-4 w-4 text-muted-foreground pointer-events-none\" />\n            </div>\n            {errors?.actualCompletion && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.actualCompletion[0]}\n              </p>\n            )}\n          </div>\n        )}\n\n        {/* Construction Timeline Description */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"constructionTimeline\">Construction Timeline Description</Label>\n          <Input\n            id=\"constructionTimeline\"\n            placeholder=\"Brief description of construction phases and milestones\"\n            value={value.constructionTimeline || ''}\n            onChange={(e) => handleInputChange('constructionTimeline', e.target.value)}\n            className={errors?.constructionTimeline ? 'border-red-500' : ''}\n          />\n          {errors?.constructionTimeline && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {errors.constructionTimeline[0]}\n            </p>\n          )}\n        </div>\n\n        {/* Project Summary */}\n        {(value.launchDate && value.expectedCompletion && isValidDateRange()) && (\n          <div className=\"p-4 bg-muted/50 rounded-lg space-y-3\">\n            <h4 className=\"font-medium text-sm flex items-center gap-2\">\n              <Clock className=\"h-4 w-4\" />\n              Project Summary\n            </h4>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n              <div className=\"text-center\">\n                <Badge variant=\"secondary\" className=\"w-full justify-center\">\n                  Duration\n                </Badge>\n                <p className=\"text-sm font-medium mt-1\">\n                  {projectDuration} months\n                </p>\n              </div>\n              \n              <div className=\"text-center\">\n                <Badge variant=\"secondary\" className=\"w-full justify-center\">\n                  Launch Date\n                </Badge>\n                <p className=\"text-sm font-medium mt-1\">\n                  {new Date(value.launchDate).toLocaleDateString('en-IN')}\n                </p>\n              </div>\n              \n              <div className=\"text-center\">\n                <Badge variant=\"secondary\" className=\"w-full justify-center\">\n                  Expected Completion\n                </Badge>\n                <p className=\"text-sm font-medium mt-1\">\n                  {new Date(value.expectedCompletion).toLocaleDateString('en-IN')}\n                </p>\n              </div>\n            </div>\n            \n            {value.actualCompletion && (\n              <div className=\"text-center\">\n                <Badge variant=\"outline\" className=\"flex items-center gap-1 justify-center\">\n                  <CheckCircle className=\"h-3 w-3\" />\n                  Completed on {new Date(value.actualCompletion).toLocaleDateString('en-IN')}\n                </Badge>\n              </div>\n            )}\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AATA;;;;;;;;;AA+BA,MAAM,uBAAuB;IAC3B;QAAE,OAAO;QAAY,OAAO;QAAY,OAAO;IAAc;IAC7D;QAAE,OAAO;QAAc,OAAO;QAAc,OAAO;IAAgB;IACnE;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;IAAgB;IACjE;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;IAAgB;IACjE;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;IAAe;CACjE;AAEc,SAAS,qBAAqB,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAA6B;IACjG,MAAM,oBAAoB,CAAC,OAA+B;QACxD,SAAS;YACP,GAAG,KAAK;YACR,CAAC,MAAM,EAAE;QACX;IACF;IAEA,6BAA6B;IAC7B,MAAM,oBAAoB;QACxB,IAAI,MAAM,UAAU,IAAI,MAAM,kBAAkB,EAAE;YAChD,MAAM,SAAS,IAAI,KAAK,MAAM,UAAU;YACxC,MAAM,aAAa,IAAI,KAAK,MAAM,kBAAkB;YACpD,MAAM,WAAW,WAAW,OAAO,KAAK,OAAO,OAAO;YACtD,MAAM,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,KAAK,EAAE;YACjE,OAAO,aAAa,IAAI,aAAa;QACvC;QACA,OAAO;IACT;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB;QACvB,IAAI,MAAM,UAAU,IAAI,MAAM,kBAAkB,EAAE;YAChD,OAAO,IAAI,KAAK,MAAM,UAAU,IAAI,IAAI,KAAK,MAAM,kBAAkB;QACvE;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB,qBAAqB,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK,MAAM,kBAAkB;IACnG,MAAM,kBAAkB;IAExB,qBACE,6WAAC,gIAAA,CAAA,OAAI;;0BACH,6WAAC,gIAAA,CAAA,aAAU;;kCACT,6WAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6WAAC,8RAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGlC,6WAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6WAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAqB;;;;;;kDACpC,6WAAC,mIAAA,CAAA,kBAAe;kDACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;8DACN,6WAAC,mIAAA,CAAA,iBAAc;8DACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6WAAC,mIAAA,CAAA,iBAAc;8DACb,cAAA,6WAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKX,6WAAC,kIAAA,CAAA,SAAM;gCACL,OAAO,MAAM,kBAAkB;gCAC/B,eAAe,CAAC,MAAQ,kBAAkB,sBAAsB;;kDAEhE,6WAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAW,QAAQ,qBAAqB,mBAAmB;kDACxE,cAAA,6WAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6WAAC,kIAAA,CAAA,gBAAa;kDACX,qBAAqB,GAAG,CAAC,CAAC,uBACzB,6WAAC,kIAAA,CAAA,aAAU;gDAAoB,OAAO,OAAO,KAAK;0DAChD,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAI,WAAW,CAAC,qBAAqB,EAAE,OAAO,KAAK,EAAE;;;;;;wDACrD,OAAO,KAAK;;;;;;;+CAHA,OAAO,KAAK;;;;;;;;;;;;;;;;4BASlC,QAAQ,oCACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,kBAAkB,CAAC,EAAE;;;;;;;4BAGhC,+BACC,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;sDACnC,6WAAC;4CAAI,WAAW,CAAC,qBAAqB,EAAE,cAAc,KAAK,EAAE;;;;;;wCAAI;wCACvD,cAAc,KAAK;;;;;;;;;;;;;;;;;;kCAOrC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa;;;;;;0DAC5B,6WAAC,mIAAA,CAAA,kBAAe;0DACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;sEACN,6WAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6WAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,6WAAC;0EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKX,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,MAAM,UAAU;gDACvB,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC/D,WAAW,QAAQ,aAAa,mBAAmB;;;;;;0DAErD,6WAAC,8RAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;oCAErB,QAAQ,4BACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,UAAU,CAAC,EAAE;;;;;;;;;;;;;0CAK3B,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAqB;;;;;;0DACpC,6WAAC,mIAAA,CAAA,kBAAe;0DACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;sEACN,6WAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6WAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,6WAAC;0EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKX,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,MAAM,kBAAkB;gDAC/B,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;gDACvE,WAAW,QAAQ,qBAAqB,mBAAmB;gDAC3D,KAAK,MAAM,UAAU;;;;;;0DAEvB,6WAAC,8RAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;oCAErB,QAAQ,oCACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,kBAAkB,CAAC,EAAE;;;;;;;oCAGhC,CAAC,oCACA,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;oBAQ1C,MAAM,kBAAkB,KAAK,6BAC5B,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAmB;;;;;;kDAClC,6WAAC,mIAAA,CAAA,kBAAe;kDACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;8DACN,6WAAC,mIAAA,CAAA,iBAAc;8DACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6WAAC,mIAAA,CAAA,iBAAc;8DACb,cAAA,6WAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKX,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,OAAO,MAAM,gBAAgB,IAAI;wCACjC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACrE,WAAW,QAAQ,mBAAmB,mBAAmB;wCACzD,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;kDAE7C,6WAAC,8RAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;4BAErB,QAAQ,kCACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,gBAAgB,CAAC,EAAE;;;;;;;;;;;;;kCAOnC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAuB;;;;;;0CACtC,6WAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACZ,OAAO,MAAM,oBAAoB,IAAI;gCACrC,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,KAAK;gCACzE,WAAW,QAAQ,uBAAuB,mBAAmB;;;;;;4BAE9D,QAAQ,sCACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,oBAAoB,CAAC,EAAE;;;;;;;;;;;;;oBAMnC,MAAM,UAAU,IAAI,MAAM,kBAAkB,IAAI,oCAChD,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;;kDACZ,6WAAC,wRAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG/B,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAwB;;;;;;0DAG7D,6WAAC;gDAAE,WAAU;;oDACV;oDAAgB;;;;;;;;;;;;;kDAIrB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAwB;;;;;;0DAG7D,6WAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,MAAM,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;kDAInD,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAwB;;;;;;0DAG7D,6WAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,MAAM,kBAAkB,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;4BAK5D,MAAM,gBAAgB,kBACrB,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;sDACjC,6WAAC,+SAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAY;wCACrB,IAAI,KAAK,MAAM,gBAAgB,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpF", "debugId": null}}, {"offset": {"line": 5089, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = \"Avatar\"\n\nconst AvatarImage = React.forwardRef<\n  HTMLImageElement,\n  React.ImgHTMLAttributes<HTMLImageElement>\n>(({ className, ...props }, ref) => (\n  <img\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = \"AvatarImage\"\n\nconst AvatarFallback = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = \"AvatarFallback\"\n\nconst AvatarInitials = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & { name: string }\n>(({ className, name, ...props }, ref) => {\n  const initials = name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        \"flex h-full w-full items-center justify-center rounded-full bg-sky-500 text-white text-sm font-medium\",\n        className\n      )}\n      {...props}\n    >\n      {initials}\n    </div>\n  )\n})\nAvatarInitials.displayName = \"AvatarInitials\"\n\nexport { Avatar, AvatarImage, AvatarFallback, AvatarInitials }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAEA,MAAM,uBAAS,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG;AAErB,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,+BAAiB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAChC,MAAM,WAAW,KACd,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IAEZ,qBACE,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yGACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/forms/property/OwnerDeveloperSelector.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\nimport { \n  User, \n  Building, \n  Search, \n  Filter, \n  AlertCircle, \n  Phone, \n  Mail, \n  MapPin,\n  Briefcase,\n  Plus,\n  ExternalLink\n} from 'lucide-react'\nimport { useGetPropertyOwnersQuery } from '@/store/api/propertyOwnersApi'\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'\n\ninterface OwnerDeveloperData {\n  ownerId?: string\n  developerId?: string\n}\n\ninterface OwnerDeveloperSelectorProps {\n  value: OwnerDeveloperData\n  onChange: (data: OwnerDeveloperData) => void\n  errors?: {\n    ownerId?: string[]\n    developerId?: string[]\n  }\n}\n\ninterface PropertyOwner {\n  _id: string\n  firstName: string\n  lastName: string\n  email: string\n  phone: string\n  company?: string\n  isOwner: boolean\n  isDeveloper: boolean\n  isInvestor: boolean\n  address: {\n    city: string\n    state: string\n  }\n  developerDetails?: {\n    experience: number\n    completedProjects: number\n    specialization: string[]\n  }\n}\n\nexport default function OwnerDeveloperSelector({ value, onChange, errors }: OwnerDeveloperSelectorProps) {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [roleFilter, setRoleFilter] = useState<'all' | 'owner' | 'developer'>('all')\n  const [selectedOwner, setSelectedOwner] = useState<PropertyOwner | null>(null)\n  const [selectedDeveloper, setSelectedDeveloper] = useState<PropertyOwner | null>(null)\n\n  // Fetch property owners/developers\n  const { data: ownersData, isLoading, error } = useGetPropertyOwnersQuery({\n    page: 1,\n    limit: 100,\n    search: searchTerm,\n    includeProperties: false\n  })\n\n  const owners = ownersData?.data?.data || []\n\n  // Filter owners based on role and search\n  const filteredOwners = owners.filter((owner: PropertyOwner) => {\n    const matchesSearch = searchTerm === '' || \n      `${owner.firstName} ${owner.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      owner.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      owner.company?.toLowerCase().includes(searchTerm.toLowerCase())\n\n    const matchesRole = roleFilter === 'all' || \n      (roleFilter === 'owner' && owner.isOwner) ||\n      (roleFilter === 'developer' && owner.isDeveloper)\n\n    return matchesSearch && matchesRole\n  })\n\n  // Get owners only\n  const propertyOwners = filteredOwners.filter((owner: PropertyOwner) => owner.isOwner)\n  \n  // Get developers only\n  const developers = filteredOwners.filter((owner: PropertyOwner) => owner.isDeveloper)\n\n  // Update selected owner/developer when IDs change\n  useEffect(() => {\n    if (value.ownerId) {\n      const owner = owners.find((o: PropertyOwner) => o._id === value.ownerId)\n      setSelectedOwner(owner || null)\n    }\n    if (value.developerId) {\n      const developer = owners.find((o: PropertyOwner) => o._id === value.developerId)\n      setSelectedDeveloper(developer || null)\n    }\n  }, [value.ownerId, value.developerId, owners])\n\n  const handleOwnerSelect = (ownerId: string) => {\n    onChange({\n      ...value,\n      ownerId: ownerId\n    })\n  }\n\n  const handleDeveloperSelect = (developerId: string) => {\n    onChange({\n      ...value,\n      developerId: developerId\n    })\n  }\n\n  const getInitials = (firstName: string, lastName: string) => {\n    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()\n  }\n\n  const getRoleBadges = (owner: PropertyOwner) => {\n    const roles = []\n    if (owner.isOwner) roles.push({ label: 'Owner', icon: Building, color: 'bg-blue-500' })\n    if (owner.isDeveloper) roles.push({ label: 'Developer', icon: Briefcase, color: 'bg-green-500' })\n    if (owner.isInvestor) roles.push({ label: 'Investor', icon: User, color: 'bg-purple-500' })\n    \n    return roles.map((role, index) => (\n      <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n        <role.icon className=\"h-3 w-3 mr-1\" />\n        {role.label}\n      </Badge>\n    ))\n  }\n\n  if (isLoading) {\n    return (\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-center justify-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n            <span className=\"ml-2\">Loading owners and developers...</span>\n          </div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  if (error) {\n    return (\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"text-center text-red-500\">\n            <AlertCircle className=\"h-8 w-8 mx-auto mb-2\" />\n            <p>Failed to load owners and developers</p>\n          </div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <User className=\"h-5 w-5\" />\n          Property Owner & Developer\n        </CardTitle>\n        <CardDescription>\n          Select existing property owner and developer for this property\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        {/* Search and Filter */}\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          <div className=\"flex-1\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-3 h-4 w-4 text-muted-foreground\" />\n              <Input\n                placeholder=\"Search by name, email, or company...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n          </div>\n          <div className=\"flex items-center gap-2\">\n            <Filter className=\"h-4 w-4 text-muted-foreground\" />\n            <Select value={roleFilter} onValueChange={(value: any) => setRoleFilter(value)}>\n              <SelectTrigger className=\"w-40\">\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">All Roles</SelectItem>\n                <SelectItem value=\"owner\">Owners Only</SelectItem>\n                <SelectItem value=\"developer\">Developers Only</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n        </div>\n\n        {/* Property Owner Selection */}\n        <div className=\"space-y-3\">\n          <div className=\"flex items-center justify-between\">\n            <Label className=\"text-base font-medium\">Property Owner (Optional)</Label>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => window.open('/properties/owners/add', '_blank')}\n              className=\"text-xs\"\n            >\n              <Plus className=\"h-3 w-3 mr-1\" />\n              Add New Owner\n              <ExternalLink className=\"h-3 w-3 ml-1\" />\n            </Button>\n          </div>\n          \n          {selectedOwner ? (\n            <div className=\"p-4 border rounded-lg bg-blue-50 border-blue-200\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <Avatar>\n                    <AvatarFallback className=\"bg-blue-500 text-white\">\n                      {getInitials(selectedOwner.firstName, selectedOwner.lastName)}\n                    </AvatarFallback>\n                  </Avatar>\n                  <div>\n                    <h4 className=\"font-medium\">{selectedOwner.firstName} {selectedOwner.lastName}</h4>\n                    <p className=\"text-sm text-muted-foreground\">{selectedOwner.email}</p>\n                    <div className=\"flex items-center gap-2 mt-1\">\n                      {getRoleBadges(selectedOwner)}\n                    </div>\n                  </div>\n                </div>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => handleOwnerSelect('')}\n                >\n                  Change\n                </Button>\n              </div>\n            </div>\n          ) : (\n            <Select value={value.ownerId || ''} onValueChange={handleOwnerSelect}>\n              <SelectTrigger className={errors?.ownerId ? 'border-red-500' : ''}>\n                <SelectValue placeholder=\"Select property owner\" />\n              </SelectTrigger>\n              <SelectContent>\n                {propertyOwners.map((owner: PropertyOwner) => (\n                  <SelectItem key={owner._id} value={owner._id}>\n                    <div className=\"flex items-center gap-2\">\n                      <Avatar className=\"h-6 w-6\">\n                        <AvatarFallback className=\"text-xs\">\n                          {getInitials(owner.firstName, owner.lastName)}\n                        </AvatarFallback>\n                      </Avatar>\n                      <div>\n                        <div className=\"font-medium\">{owner.firstName} {owner.lastName}</div>\n                        <div className=\"text-xs text-muted-foreground\">{owner.company || owner.email}</div>\n                      </div>\n                    </div>\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          )}\n          \n          {errors?.ownerId && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {errors.ownerId[0]}\n            </p>\n          )}\n        </div>\n\n        {/* Developer Selection */}\n        <div className=\"space-y-3\">\n          <div className=\"flex items-center justify-between\">\n            <Label className=\"text-base font-medium\">Developer Information *</Label>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => window.open('/properties/owners/add', '_blank')}\n              className=\"text-xs\"\n            >\n              <Plus className=\"h-3 w-3 mr-1\" />\n              Add New Developer\n              <ExternalLink className=\"h-3 w-3 ml-1\" />\n            </Button>\n          </div>\n          \n          {selectedDeveloper ? (\n            <div className=\"p-4 border rounded-lg bg-green-50 border-green-200\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <Avatar>\n                    <AvatarFallback className=\"bg-green-500 text-white\">\n                      {getInitials(selectedDeveloper.firstName, selectedDeveloper.lastName)}\n                    </AvatarFallback>\n                  </Avatar>\n                  <div>\n                    <h4 className=\"font-medium\">{selectedDeveloper.firstName} {selectedDeveloper.lastName}</h4>\n                    <p className=\"text-sm text-muted-foreground\">{selectedDeveloper.company || selectedDeveloper.email}</p>\n                    <div className=\"flex items-center gap-2 mt-1\">\n                      {getRoleBadges(selectedDeveloper)}\n                      {selectedDeveloper.developerDetails && (\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          {selectedDeveloper.developerDetails.experience}+ years exp\n                        </Badge>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => handleDeveloperSelect('')}\n                >\n                  Change\n                </Button>\n              </div>\n            </div>\n          ) : (\n            <Select value={value.developerId || ''} onValueChange={handleDeveloperSelect}>\n              <SelectTrigger className={errors?.developerId ? 'border-red-500' : ''}>\n                <SelectValue placeholder=\"Select developer\" />\n              </SelectTrigger>\n              <SelectContent>\n                {developers.map((developer: PropertyOwner) => (\n                  <SelectItem key={developer._id} value={developer._id}>\n                    <div className=\"flex items-center gap-2\">\n                      <Avatar className=\"h-6 w-6\">\n                        <AvatarFallback className=\"text-xs\">\n                          {getInitials(developer.firstName, developer.lastName)}\n                        </AvatarFallback>\n                      </Avatar>\n                      <div>\n                        <div className=\"font-medium\">{developer.firstName} {developer.lastName}</div>\n                        <div className=\"text-xs text-muted-foreground\">\n                          {developer.company || developer.email}\n                          {developer.developerDetails && (\n                            <span className=\"ml-2\">• {developer.developerDetails.experience}+ years</span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          )}\n          \n          {errors?.developerId && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {errors.developerId[0]}\n            </p>\n          )}\n        </div>\n\n        {/* Summary */}\n        {(selectedOwner || selectedDeveloper) && (\n          <div className=\"p-4 bg-muted/50 rounded-lg\">\n            <h4 className=\"font-medium text-sm mb-2\">Selection Summary</h4>\n            <div className=\"space-y-2 text-sm\">\n              {selectedOwner && (\n                <div className=\"flex items-center gap-2\">\n                  <Building className=\"h-4 w-4 text-blue-500\" />\n                  <span>Owner: {selectedOwner.firstName} {selectedOwner.lastName}</span>\n                </div>\n              )}\n              {selectedDeveloper && (\n                <div className=\"flex items-center gap-2\">\n                  <Briefcase className=\"h-4 w-4 text-green-500\" />\n                  <span>Developer: {selectedDeveloper.firstName} {selectedDeveloper.lastName}</span>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAxBA;;;;;;;;;;;;AA8De,SAAS,uBAAuB,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAA+B;IACrG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiC;IAC5E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAwB;IAEjF,mCAAmC;IACnC,MAAM,EAAE,MAAM,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,4BAAyB,AAAD,EAAE;QACvE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,mBAAmB;IACrB;IAEA,MAAM,SAAS,YAAY,MAAM,QAAQ,EAAE;IAE3C,yCAAyC;IACzC,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAC;QACpC,MAAM,gBAAgB,eAAe,MACnC,GAAG,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACpF,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,MAAM,OAAO,EAAE,cAAc,SAAS,WAAW,WAAW;QAE9D,MAAM,cAAc,eAAe,SAChC,eAAe,WAAW,MAAM,OAAO,IACvC,eAAe,eAAe,MAAM,WAAW;QAElD,OAAO,iBAAiB;IAC1B;IAEA,kBAAkB;IAClB,MAAM,iBAAiB,eAAe,MAAM,CAAC,CAAC,QAAyB,MAAM,OAAO;IAEpF,sBAAsB;IACtB,MAAM,aAAa,eAAe,MAAM,CAAC,CAAC,QAAyB,MAAM,WAAW;IAEpF,kDAAkD;IAClD,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAC,IAAqB,EAAE,GAAG,KAAK,MAAM,OAAO;YACvE,iBAAiB,SAAS;QAC5B;QACA,IAAI,MAAM,WAAW,EAAE;YACrB,MAAM,YAAY,OAAO,IAAI,CAAC,CAAC,IAAqB,EAAE,GAAG,KAAK,MAAM,WAAW;YAC/E,qBAAqB,aAAa;QACpC;IACF,GAAG;QAAC,MAAM,OAAO;QAAE,MAAM,WAAW;QAAE;KAAO;IAE7C,MAAM,oBAAoB,CAAC;QACzB,SAAS;YACP,GAAG,KAAK;YACR,SAAS;QACX;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,SAAS;YACP,GAAG,KAAK;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc,CAAC,WAAmB;QACtC,OAAO,GAAG,UAAU,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,IAAI,CAAC,WAAW;IAClE;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,QAAQ,EAAE;QAChB,IAAI,MAAM,OAAO,EAAE,MAAM,IAAI,CAAC;YAAE,OAAO;YAAS,MAAM,8RAAA,CAAA,WAAQ;YAAE,OAAO;QAAc;QACrF,IAAI,MAAM,WAAW,EAAE,MAAM,IAAI,CAAC;YAAE,OAAO;YAAa,MAAM,gSAAA,CAAA,YAAS;YAAE,OAAO;QAAe;QAC/F,IAAI,MAAM,UAAU,EAAE,MAAM,IAAI,CAAC;YAAE,OAAO;YAAY,MAAM,sRAAA,CAAA,OAAI;YAAE,OAAO;QAAgB;QAEzF,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM,sBACtB,6WAAC,iIAAA,CAAA,QAAK;gBAAa,SAAQ;gBAAY,WAAU;;kCAC/C,6WAAC,KAAK,IAAI;wBAAC,WAAU;;;;;;oBACpB,KAAK,KAAK;;eAFD;;;;;IAKhB;IAEA,IAAI,WAAW;QACb,qBACE,6WAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,6WAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;;;;;;sCACf,6WAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;IAKjC;IAEA,IAAI,OAAO;QACT,qBACE,6WAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,6WAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC,wSAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6WAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,6WAAC,gIAAA,CAAA,OAAI;;0BACH,6WAAC,gIAAA,CAAA,aAAU;;kCACT,6WAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6WAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAG9B,6WAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6WAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6WAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;;;;;;0CAIhB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6WAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAY,eAAe,CAAC,QAAe,cAAc;;0DACtE,6WAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,kIAAA,CAAA,gBAAa;;kEACZ,6WAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAQ;;;;;;kEAC1B,6WAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOtC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAwB;;;;;;kDACzC,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,0BAA0B;wCACrD,WAAU;;0DAEV,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;0DAEjC,6WAAC,0SAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;4BAI3B,8BACC,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,kIAAA,CAAA,SAAM;8DACL,cAAA,6WAAC,kIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,YAAY,cAAc,SAAS,EAAE,cAAc,QAAQ;;;;;;;;;;;8DAGhE,6WAAC;;sEACC,6WAAC;4DAAG,WAAU;;gEAAe,cAAc,SAAS;gEAAC;gEAAE,cAAc,QAAQ;;;;;;;sEAC7E,6WAAC;4DAAE,WAAU;sEAAiC,cAAc,KAAK;;;;;;sEACjE,6WAAC;4DAAI,WAAU;sEACZ,cAAc;;;;;;;;;;;;;;;;;;sDAIrB,6WAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,kBAAkB;sDAClC;;;;;;;;;;;;;;;;qDAML,6WAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO,MAAM,OAAO,IAAI;gCAAI,eAAe;;kDACjD,6WAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAW,QAAQ,UAAU,mBAAmB;kDAC7D,cAAA,6WAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6WAAC,kIAAA,CAAA,gBAAa;kDACX,eAAe,GAAG,CAAC,CAAC,sBACnB,6WAAC,kIAAA,CAAA,aAAU;gDAAiB,OAAO,MAAM,GAAG;0DAC1C,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;sEAChB,cAAA,6WAAC,kIAAA,CAAA,iBAAc;gEAAC,WAAU;0EACvB,YAAY,MAAM,SAAS,EAAE,MAAM,QAAQ;;;;;;;;;;;sEAGhD,6WAAC;;8EACC,6WAAC;oEAAI,WAAU;;wEAAe,MAAM,SAAS;wEAAC;wEAAE,MAAM,QAAQ;;;;;;;8EAC9D,6WAAC;oEAAI,WAAU;8EAAiC,MAAM,OAAO,IAAI,MAAM,KAAK;;;;;;;;;;;;;;;;;;+CATjE,MAAM,GAAG;;;;;;;;;;;;;;;;4BAkBjC,QAAQ,yBACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,OAAO,CAAC,EAAE;;;;;;;;;;;;;kCAMxB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAwB;;;;;;kDACzC,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,0BAA0B;wCACrD,WAAU;;0DAEV,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;0DAEjC,6WAAC,0SAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;4BAI3B,kCACC,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,kIAAA,CAAA,SAAM;8DACL,cAAA,6WAAC,kIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,YAAY,kBAAkB,SAAS,EAAE,kBAAkB,QAAQ;;;;;;;;;;;8DAGxE,6WAAC;;sEACC,6WAAC;4DAAG,WAAU;;gEAAe,kBAAkB,SAAS;gEAAC;gEAAE,kBAAkB,QAAQ;;;;;;;sEACrF,6WAAC;4DAAE,WAAU;sEAAiC,kBAAkB,OAAO,IAAI,kBAAkB,KAAK;;;;;;sEAClG,6WAAC;4DAAI,WAAU;;gEACZ,cAAc;gEACd,kBAAkB,gBAAgB,kBACjC,6WAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;;wEAChC,kBAAkB,gBAAgB,CAAC,UAAU;wEAAC;;;;;;;;;;;;;;;;;;;;;;;;;sDAMzD,6WAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,sBAAsB;sDACtC;;;;;;;;;;;;;;;;qDAML,6WAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO,MAAM,WAAW,IAAI;gCAAI,eAAe;;kDACrD,6WAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAW,QAAQ,cAAc,mBAAmB;kDACjE,cAAA,6WAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6WAAC,kIAAA,CAAA,gBAAa;kDACX,WAAW,GAAG,CAAC,CAAC,0BACf,6WAAC,kIAAA,CAAA,aAAU;gDAAqB,OAAO,UAAU,GAAG;0DAClD,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;sEAChB,cAAA,6WAAC,kIAAA,CAAA,iBAAc;gEAAC,WAAU;0EACvB,YAAY,UAAU,SAAS,EAAE,UAAU,QAAQ;;;;;;;;;;;sEAGxD,6WAAC;;8EACC,6WAAC;oEAAI,WAAU;;wEAAe,UAAU,SAAS;wEAAC;wEAAE,UAAU,QAAQ;;;;;;;8EACtE,6WAAC;oEAAI,WAAU;;wEACZ,UAAU,OAAO,IAAI,UAAU,KAAK;wEACpC,UAAU,gBAAgB,kBACzB,6WAAC;4EAAK,WAAU;;gFAAO;gFAAG,UAAU,gBAAgB,CAAC,UAAU;gFAAC;;;;;;;;;;;;;;;;;;;;;;;;;+CAZzD,UAAU,GAAG;;;;;;;;;;;;;;;;4BAuBrC,QAAQ,6BACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,WAAW,CAAC,EAAE;;;;;;;;;;;;;oBAM3B,CAAC,iBAAiB,iBAAiB,mBAClC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,6WAAC;gCAAI,WAAU;;oCACZ,+BACC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,8RAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6WAAC;;oDAAK;oDAAQ,cAAc,SAAS;oDAAC;oDAAE,cAAc,QAAQ;;;;;;;;;;;;;oCAGjE,mCACC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,gSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6WAAC;;oDAAK;oDAAY,kBAAkB,SAAS;oDAAC;oDAAE,kBAAkB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5F", "debugId": null}}, {"offset": {"line": 6115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/lib/validations/property.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// Property Type Enum\nexport const PropertyTypeEnum = z.enum([\n  'residential',\n  'commercial', \n  'industrial',\n  'land',\n  'mixed',\n  'luxury',\n  'eco_friendly'\n])\n\n// Construction Status Enum\nexport const ConstructionStatusEnum = z.enum([\n  'planning',\n  'foundation', \n  'structure',\n  'finishing',\n  'completed'\n])\n\n// Property Status Enum\nexport const PropertyStatusEnum = z.enum([\n  'active',\n  'inactive',\n  'sold_out',\n  'coming_soon'\n])\n\n// Location Schema\nexport const LocationSchema = z.object({\n  address: z.string()\n    .min(5, 'Address must be at least 5 characters')\n    .max(200, 'Address cannot exceed 200 characters'),\n  city: z.string()\n    .min(2, 'City must be at least 2 characters')\n    .max(100, 'City cannot exceed 100 characters'),\n  state: z.string()\n    .min(2, 'State must be at least 2 characters')\n    .max(100, 'State cannot exceed 100 characters'),\n  pincode: z.string()\n    .regex(/^[0-9]{6}$/, 'Pincode must be 6 digits'),\n  coordinates: z.object({\n    latitude: z.number()\n      .min(-90, 'Latitude must be between -90 and 90')\n      .max(90, 'Latitude must be between -90 and 90'),\n    longitude: z.number()\n      .min(-180, 'Longitude must be between -180 and 180')\n      .max(180, 'Longitude must be between -180 and 180')\n  }).optional()\n})\n\n// Developer Schema\nexport const DeveloperSchema = z.object({\n  name: z.string()\n    .min(2, 'Developer name must be at least 2 characters')\n    .max(100, 'Developer name cannot exceed 100 characters'),\n  contact: z.string()\n    .regex(/^[0-9]{10,15}$/, 'Contact must be 10-15 digits'),\n  email: z.string()\n    .email('Invalid email format'),\n  experience: z.number()\n    .min(0, 'Experience cannot be negative')\n    .max(100, 'Experience cannot exceed 100 years')\n    .optional()\n})\n\n// File Upload Schema\nexport const FileUploadSchema = z.object({\n  key: z.string().min(1, 'File key is required'),\n  name: z.string().min(1, 'File name is required'),\n  type: z.string().min(1, 'File type is required'),\n  url: z.string().url('Invalid file URL'),\n  uploadedAt: z.date().optional()\n})\n\n// Property Form Schema\nexport const PropertyFormSchema = z.object({\n  // Basic Information\n  name: z.string()\n    .min(3, 'Property name must be at least 3 characters')\n    .max(255, 'Property name cannot exceed 255 characters'),\n  description: z.string()\n    .min(10, 'Description must be at least 10 characters')\n    .max(2000, 'Description cannot exceed 2000 characters'),\n  propertyType: PropertyTypeEnum,\n  \n  // Location\n  location: LocationSchema,\n  \n  // Financial Details\n  expectedReturns: z.number()\n    .min(0, 'Expected returns cannot be negative')\n    .max(100, 'Expected returns cannot exceed 100%'),\n  maturityPeriodMonths: z.number()\n    .min(1, 'Maturity period must be at least 1 month')\n    .max(600, 'Maturity period cannot exceed 600 months'),\n  \n  // Stock Information\n  totalStocks: z.number()\n    .min(1, 'Total stocks must be at least 1'),\n  pricePerStock: z.number()\n    .min(0.01, 'Price per stock must be greater than 0'),\n  availableStocks: z.number()\n    .min(0, 'Available stocks cannot be negative'),\n  minimumInvestment: z.number()\n    .min(1, 'Minimum investment must be at least 1')\n    .optional(),\n  maximumInvestment: z.number()\n    .min(1, 'Maximum investment must be at least 1')\n    .optional(),\n\n  // Stock Configuration\n  stockPrefix: z.string()\n    .min(1, 'Stock prefix is required')\n    .max(10, 'Stock prefix cannot exceed 10 characters'),\n  stockStartNumber: z.number()\n    .min(1, 'Stock start number must be at least 1'),\n\n  // Commission Configuration\n  referralCommissionRate: z.number()\n    .min(0, 'Referral commission rate cannot be negative')\n    .max(100, 'Referral commission rate cannot exceed 100%'),\n  salesCommissionRate: z.number()\n    .min(0, 'Sales commission rate cannot be negative')\n    .max(100, 'Sales commission rate cannot exceed 100%'),\n  referralCommissionPerStock: z.number()\n    .min(0, 'Referral commission per stock cannot be negative'),\n  salesCommissionPerStock: z.number()\n    .min(0, 'Sales commission per stock cannot be negative'),\n  commissionType: z.enum(['percentage', 'fixed']),\n  \n  // Construction Details\n  constructionStatus: ConstructionStatusEnum,\n  launchDate: z.string()\n    .min(1, 'Launch date is required'),\n  expectedCompletion: z.string()\n    .min(1, 'Expected completion date is required'),\n  actualCompletion: z.string().optional(),\n  constructionTimeline: z.string().optional(),\n  \n  // Owner and Developer Information\n  ownerId: z.string().optional(),\n  developerId: z.string()\n    .min(1, 'Developer is required'),\n\n  // Property Features\n  amenities: z.array(z.string()).default([]),\n  features: z.array(z.string()).default([]),\n  specifications: z.record(z.any()).optional(),\n\n  // Media Files\n  images: z.array(FileUploadSchema).default([]),\n  documents: z.array(FileUploadSchema).default([]),\n  videos: z.array(FileUploadSchema).default([]),\n  legalDocuments: z.array(z.string()).default([]),\n\n  // Administrative\n  status: PropertyStatusEnum.default('active'),\n  featured: z.boolean().default(false),\n  priorityOrder: z.number().default(0)\n}).refine((data) => {\n  // Validate that available stocks don't exceed total stocks\n  return data.availableStocks <= data.totalStocks\n}, {\n  message: 'Available stocks cannot exceed total stocks',\n  path: ['availableStocks']\n}).refine((data) => {\n  // Validate minimum investment is not greater than maximum investment\n  if (data.minimumInvestment && data.maximumInvestment) {\n    return data.minimumInvestment <= data.maximumInvestment\n  }\n  return true\n}, {\n  message: 'Minimum investment cannot be greater than maximum investment',\n  path: ['minimumInvestment']\n})\n\nexport type PropertyFormData = z.infer<typeof PropertyFormSchema>\n\n// Validation helper functions\nexport const validatePropertyForm = (data: unknown) => {\n  return PropertyFormSchema.safeParse(data)\n}\n\nexport const getPropertyFormErrors = (data: unknown) => {\n  const result = PropertyFormSchema.safeParse(data)\n  if (!result.success) {\n    return result.error.flatten()\n  }\n  return null\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAGO,MAAM,mBAAmB,6NAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,yBAAyB,6NAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAC3C;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,qBAAqB,6NAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IACvC;IACA;IACA;IACA;CACD;AAGM,MAAM,iBAAiB,6NAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,SAAS,6NAAA,CAAA,IAAC,CAAC,MAAM,GACd,GAAG,CAAC,GAAG,yCACP,GAAG,CAAC,KAAK;IACZ,MAAM,6NAAA,CAAA,IAAC,CAAC,MAAM,GACX,GAAG,CAAC,GAAG,sCACP,GAAG,CAAC,KAAK;IACZ,OAAO,6NAAA,CAAA,IAAC,CAAC,MAAM,GACZ,GAAG,CAAC,GAAG,uCACP,GAAG,CAAC,KAAK;IACZ,SAAS,6NAAA,CAAA,IAAC,CAAC,MAAM,GACd,KAAK,CAAC,cAAc;IACvB,aAAa,6NAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,UAAU,6NAAA,CAAA,IAAC,CAAC,MAAM,GACf,GAAG,CAAC,CAAC,IAAI,uCACT,GAAG,CAAC,IAAI;QACX,WAAW,6NAAA,CAAA,IAAC,CAAC,MAAM,GAChB,GAAG,CAAC,CAAC,KAAK,0CACV,GAAG,CAAC,KAAK;IACd,GAAG,QAAQ;AACb;AAGO,MAAM,kBAAkB,6NAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,MAAM,6NAAA,CAAA,IAAC,CAAC,MAAM,GACX,GAAG,CAAC,GAAG,gDACP,GAAG,CAAC,KAAK;IACZ,SAAS,6NAAA,CAAA,IAAC,CAAC,MAAM,GACd,KAAK,CAAC,kBAAkB;IAC3B,OAAO,6NAAA,CAAA,IAAC,CAAC,MAAM,GACZ,KAAK,CAAC;IACT,YAAY,6NAAA,CAAA,IAAC,CAAC,MAAM,GACjB,GAAG,CAAC,GAAG,iCACP,GAAG,CAAC,KAAK,sCACT,QAAQ;AACb;AAGO,MAAM,mBAAmB,6NAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,KAAK,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACvB,MAAM,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,MAAM,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,KAAK,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACpB,YAAY,6NAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;AAC/B;AAGO,MAAM,qBAAqB,6NAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,oBAAoB;IACpB,MAAM,6NAAA,CAAA,IAAC,CAAC,MAAM,GACX,GAAG,CAAC,GAAG,+CACP,GAAG,CAAC,KAAK;IACZ,aAAa,6NAAA,CAAA,IAAC,CAAC,MAAM,GAClB,GAAG,CAAC,IAAI,8CACR,GAAG,CAAC,MAAM;IACb,cAAc;IAEd,WAAW;IACX,UAAU;IAEV,oBAAoB;IACpB,iBAAiB,6NAAA,CAAA,IAAC,CAAC,MAAM,GACtB,GAAG,CAAC,GAAG,uCACP,GAAG,CAAC,KAAK;IACZ,sBAAsB,6NAAA,CAAA,IAAC,CAAC,MAAM,GAC3B,GAAG,CAAC,GAAG,4CACP,GAAG,CAAC,KAAK;IAEZ,oBAAoB;IACpB,aAAa,6NAAA,CAAA,IAAC,CAAC,MAAM,GAClB,GAAG,CAAC,GAAG;IACV,eAAe,6NAAA,CAAA,IAAC,CAAC,MAAM,GACpB,GAAG,CAAC,MAAM;IACb,iBAAiB,6NAAA,CAAA,IAAC,CAAC,MAAM,GACtB,GAAG,CAAC,GAAG;IACV,mBAAmB,6NAAA,CAAA,IAAC,CAAC,MAAM,GACxB,GAAG,CAAC,GAAG,yCACP,QAAQ;IACX,mBAAmB,6NAAA,CAAA,IAAC,CAAC,MAAM,GACxB,GAAG,CAAC,GAAG,yCACP,QAAQ;IAEX,sBAAsB;IACtB,aAAa,6NAAA,CAAA,IAAC,CAAC,MAAM,GAClB,GAAG,CAAC,GAAG,4BACP,GAAG,CAAC,IAAI;IACX,kBAAkB,6NAAA,CAAA,IAAC,CAAC,MAAM,GACvB,GAAG,CAAC,GAAG;IAEV,2BAA2B;IAC3B,wBAAwB,6NAAA,CAAA,IAAC,CAAC,MAAM,GAC7B,GAAG,CAAC,GAAG,+CACP,GAAG,CAAC,KAAK;IACZ,qBAAqB,6NAAA,CAAA,IAAC,CAAC,MAAM,GAC1B,GAAG,CAAC,GAAG,4CACP,GAAG,CAAC,KAAK;IACZ,4BAA4B,6NAAA,CAAA,IAAC,CAAC,MAAM,GACjC,GAAG,CAAC,GAAG;IACV,yBAAyB,6NAAA,CAAA,IAAC,CAAC,MAAM,GAC9B,GAAG,CAAC,GAAG;IACV,gBAAgB,6NAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAc;KAAQ;IAE9C,uBAAuB;IACvB,oBAAoB;IACpB,YAAY,6NAAA,CAAA,IAAC,CAAC,MAAM,GACjB,GAAG,CAAC,GAAG;IACV,oBAAoB,6NAAA,CAAA,IAAC,CAAC,MAAM,GACzB,GAAG,CAAC,GAAG;IACV,kBAAkB,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACrC,sBAAsB,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAEzC,kCAAkC;IAClC,SAAS,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,aAAa,6NAAA,CAAA,IAAC,CAAC,MAAM,GAClB,GAAG,CAAC,GAAG;IAEV,oBAAoB;IACpB,WAAW,6NAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6NAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IACzC,UAAU,6NAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6NAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IACxC,gBAAgB,6NAAA,CAAA,IAAC,CAAC,MAAM,CAAC,6NAAA,CAAA,IAAC,CAAC,GAAG,IAAI,QAAQ;IAE1C,cAAc;IACd,QAAQ,6NAAA,CAAA,IAAC,CAAC,KAAK,CAAC,kBAAkB,OAAO,CAAC,EAAE;IAC5C,WAAW,6NAAA,CAAA,IAAC,CAAC,KAAK,CAAC,kBAAkB,OAAO,CAAC,EAAE;IAC/C,QAAQ,6NAAA,CAAA,IAAC,CAAC,KAAK,CAAC,kBAAkB,OAAO,CAAC,EAAE;IAC5C,gBAAgB,6NAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6NAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IAE9C,iBAAiB;IACjB,QAAQ,mBAAmB,OAAO,CAAC;IACnC,UAAU,6NAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,eAAe,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;AACpC,GAAG,MAAM,CAAC,CAAC;IACT,2DAA2D;IAC3D,OAAO,KAAK,eAAe,IAAI,KAAK,WAAW;AACjD,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B,GAAG,MAAM,CAAC,CAAC;IACT,qEAAqE;IACrE,IAAI,KAAK,iBAAiB,IAAI,KAAK,iBAAiB,EAAE;QACpD,OAAO,KAAK,iBAAiB,IAAI,KAAK,iBAAiB;IACzD;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAoB;AAC7B;AAKO,MAAM,uBAAuB,CAAC;IACnC,OAAO,mBAAmB,SAAS,CAAC;AACtC;AAEO,MAAM,wBAAwB,CAAC;IACpC,MAAM,SAAS,mBAAmB,SAAS,CAAC;IAC5C,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,OAAO,OAAO,KAAK,CAAC,OAAO;IAC7B;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 6259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/app/properties/add-enhanced/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Badge } from '@/components/ui/badge'\nimport { toast } from 'sonner'\nimport DashboardLayout from '@/components/layout/DashboardLayout'\nimport { useCreatePropertyMutation } from '@/store/api/propertiesApi'\nimport LocationPicker from '@/components/forms/property/LocationPicker'\nimport FinancialDetails from '@/components/forms/property/FinancialDetails'\nimport ConstructionTimeline from '@/components/forms/property/ConstructionTimeline'\nimport OwnerDeveloperSelector from '@/components/forms/property/OwnerDeveloperSelector'\nimport { PropertyFormSchema, type PropertyFormData } from '@/lib/validations/property'\nimport {\n  ArrowLeft,\n  Save,\n  Building,\n  MapPin,\n  DollarSign,\n  Calendar,\n  User,\n  Upload,\n  Eye,\n  Plus,\n  Minus,\n  AlertCircle,\n  CheckCircle,\n  Info,\n  X\n} from 'lucide-react'\n\nconst propertyTypes = [\n  { value: 'residential', label: 'Residential', icon: '🏠' },\n  { value: 'commercial', label: 'Commercial', icon: '🏢' },\n  { value: 'industrial', label: 'Industrial', icon: '🏭' },\n  { value: 'land', label: 'Land', icon: '🌍' },\n  { value: 'mixed', label: 'Mixed Use', icon: '🏘️' },\n  { value: 'luxury', label: 'Luxury', icon: '✨' },\n  { value: 'eco_friendly', label: 'Eco-Friendly', icon: '🌱' }\n]\n\nconst amenitiesList = [\n  'Swimming Pool', 'Gym', 'Parking', 'Security', 'Garden', 'Playground',\n  'Club House', 'Power Backup', 'Elevator', 'CCTV', 'Intercom', 'Water Supply'\n]\n\nexport default function AddEnhancedPropertyPage() {\n  const router = useRouter()\n  const [createProperty, { isLoading: isCreating }] = useCreatePropertyMutation()\n  const [currentStep, setCurrentStep] = useState(1)\n  const [errors, setErrors] = useState<any>({})\n\n  const [formData, setFormData] = useState<PropertyFormData>({\n    // Basic Information\n    name: '',\n    description: '',\n    propertyType: 'residential',\n    \n    // Location\n    location: {\n      address: '',\n      city: '',\n      state: '',\n      pincode: '',\n      coordinates: {\n        latitude: 0,\n        longitude: 0\n      }\n    },\n    \n    // Financial Details\n    expectedReturns: 0,\n    maturityPeriodMonths: 12,\n    totalStocks: 100,\n    pricePerStock: 1000,\n    availableStocks: 100,\n    minimumInvestment: 1000,\n    maximumInvestment: 100000,\n    stockPrefix: 'PROP',\n    stockStartNumber: 1,\n    referralCommissionRate: 2,\n    salesCommissionRate: 1,\n    referralCommissionPerStock: 20,\n    salesCommissionPerStock: 10,\n    commissionType: 'percentage',\n    \n    // Construction Details\n    constructionStatus: 'planning',\n    launchDate: '',\n    expectedCompletion: '',\n    actualCompletion: '',\n    constructionTimeline: '',\n    \n    // Owner and Developer\n    ownerId: '',\n    developerId: '',\n    \n    // Property Features\n    amenities: [],\n    features: [],\n    specifications: {},\n    \n    // Media Files\n    images: [],\n    documents: [],\n    videos: [],\n    legalDocuments: [],\n    \n    // Administrative\n    status: 'active',\n    featured: false,\n    priorityOrder: 0\n  })\n\n  const steps = [\n    { id: 1, title: 'Basic Information', icon: Building },\n    { id: 2, title: 'Location Details', icon: MapPin },\n    { id: 3, title: 'Owner & Developer', icon: User },\n    { id: 4, title: 'Financial Details', icon: DollarSign },\n    { id: 5, title: 'Construction Timeline', icon: Calendar },\n    { id: 6, title: 'Features & Media', icon: Upload }\n  ]\n\n  const handleInputChange = (field: string, value: any) => {\n    if (field.includes('.')) {\n      const keys = field.split('.')\n      setFormData(prev => {\n        const updated = { ...prev }\n        let current = updated as any\n        for (let i = 0; i < keys.length - 1; i++) {\n          current = current[keys[i]]\n        }\n        current[keys[keys.length - 1]] = value\n        return updated\n      })\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [field]: value\n      }))\n    }\n    \n    // Clear error for this field\n    if (errors[field]) {\n      setErrors((prev: any) => ({\n        ...prev,\n        [field]: undefined\n      }))\n    }\n  }\n\n  const handleLocationChange = (location: any) => {\n    setFormData(prev => ({\n      ...prev,\n      location\n    }))\n  }\n\n  const handleFinancialChange = (financial: any) => {\n    setFormData(prev => ({\n      ...prev,\n      ...financial\n    }))\n  }\n\n  const handleConstructionChange = (construction: any) => {\n    setFormData(prev => ({\n      ...prev,\n      ...construction\n    }))\n  }\n\n  const handleOwnerDeveloperChange = (ownerDeveloper: any) => {\n    setFormData(prev => ({\n      ...prev,\n      ...ownerDeveloper\n    }))\n  }\n\n  const handleAmenityToggle = (amenity: string) => {\n    setFormData(prev => ({\n      ...prev,\n      amenities: prev.amenities.includes(amenity)\n        ? prev.amenities.filter(a => a !== amenity)\n        : [...prev.amenities, amenity]\n    }))\n  }\n\n  const validateCurrentStep = () => {\n    const result = PropertyFormSchema.safeParse(formData)\n    if (!result.success) {\n      const fieldErrors = result.error.flatten().fieldErrors\n      setErrors(fieldErrors)\n      return false\n    }\n    setErrors({})\n    return true\n  }\n\n  const handleNext = () => {\n    if (currentStep < steps.length) {\n      setCurrentStep(currentStep + 1)\n    }\n  }\n\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1)\n    }\n  }\n\n  const handleSubmit = async () => {\n    if (!validateCurrentStep()) {\n      toast.error('Please fix the validation errors before submitting')\n      return\n    }\n\n    try {\n      const result = await createProperty(formData).unwrap()\n      toast.success('Property created successfully!')\n      router.push('/properties')\n    } catch (error: any) {\n      console.error('Error creating property:', error)\n      toast.error(error?.data?.message || 'Failed to create property')\n    }\n  }\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Building className=\"h-5 w-5\" />\n                Basic Information\n              </CardTitle>\n              <CardDescription>\n                Enter the property's basic details and description\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"name\">Property Name *</Label>\n                  <Input\n                    id=\"name\"\n                    placeholder=\"Enter property name\"\n                    value={formData.name}\n                    onChange={(e) => handleInputChange('name', e.target.value)}\n                    className={errors.name ? 'border-red-500' : ''}\n                  />\n                  {errors.name && (\n                    <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                      <AlertCircle className=\"h-4 w-4\" />\n                      {errors.name[0]}\n                    </p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"propertyType\">Property Type *</Label>\n                  <Select\n                    value={formData.propertyType}\n                    onValueChange={(value) => handleInputChange('propertyType', value)}\n                  >\n                    <SelectTrigger className={errors.propertyType ? 'border-red-500' : ''}>\n                      <SelectValue placeholder=\"Select property type\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {propertyTypes.map((type) => (\n                        <SelectItem key={type.value} value={type.value}>\n                          <div className=\"flex items-center gap-2\">\n                            <span>{type.icon}</span>\n                            {type.label}\n                          </div>\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  {errors.propertyType && (\n                    <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                      <AlertCircle className=\"h-4 w-4\" />\n                      {errors.propertyType[0]}\n                    </p>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"description\">Description *</Label>\n                <Textarea\n                  id=\"description\"\n                  placeholder=\"Enter detailed property description\"\n                  value={formData.description}\n                  onChange={(e) => handleInputChange('description', e.target.value)}\n                  className={errors.description ? 'border-red-500' : ''}\n                  rows={4}\n                />\n                {errors.description && (\n                  <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                    <AlertCircle className=\"h-4 w-4\" />\n                    {errors.description[0]}\n                  </p>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n        )\n\n      case 2:\n        return (\n          <LocationPicker\n            value={formData.location}\n            onChange={handleLocationChange}\n            errors={errors.location}\n          />\n        )\n\n      case 3:\n        return (\n          <OwnerDeveloperSelector\n            value={{\n              ownerId: formData.ownerId,\n              developerId: formData.developerId\n            }}\n            onChange={handleOwnerDeveloperChange}\n            errors={{\n              ownerId: errors.ownerId,\n              developerId: errors.developerId\n            }}\n          />\n        )\n\n      case 4:\n        return (\n          <FinancialDetails\n            value={{\n              expectedReturns: formData.expectedReturns,\n              maturityPeriodMonths: formData.maturityPeriodMonths,\n              totalStocks: formData.totalStocks,\n              pricePerStock: formData.pricePerStock,\n              availableStocks: formData.availableStocks,\n              minimumInvestment: formData.minimumInvestment,\n              maximumInvestment: formData.maximumInvestment,\n              stockPrefix: formData.stockPrefix,\n              stockStartNumber: formData.stockStartNumber,\n              referralCommissionRate: formData.referralCommissionRate,\n              salesCommissionRate: formData.salesCommissionRate,\n              referralCommissionPerStock: formData.referralCommissionPerStock,\n              salesCommissionPerStock: formData.salesCommissionPerStock,\n              commissionType: formData.commissionType\n            }}\n            onChange={handleFinancialChange}\n            errors={errors}\n          />\n        )\n\n      case 5:\n        return (\n          <ConstructionTimeline\n            value={{\n              constructionStatus: formData.constructionStatus,\n              launchDate: formData.launchDate,\n              expectedCompletion: formData.expectedCompletion,\n              actualCompletion: formData.actualCompletion,\n              constructionTimeline: formData.constructionTimeline\n            }}\n            onChange={handleConstructionChange}\n            errors={{\n              constructionStatus: errors.constructionStatus,\n              launchDate: errors.launchDate,\n              expectedCompletion: errors.expectedCompletion,\n              actualCompletion: errors.actualCompletion,\n              constructionTimeline: errors.constructionTimeline\n            }}\n          />\n        )\n\n      case 6:\n        return (\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Upload className=\"h-5 w-5\" />\n                Features & Amenities\n              </CardTitle>\n              <CardDescription>\n                Select property amenities and features\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"space-y-3\">\n                <Label>Amenities</Label>\n                <div className=\"grid grid-cols-2 md:grid-cols-3 gap-3\">\n                  {amenitiesList.map((amenity) => (\n                    <div\n                      key={amenity}\n                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${\n                        formData.amenities.includes(amenity)\n                          ? 'bg-blue-50 border-blue-300'\n                          : 'hover:bg-gray-50'\n                      }`}\n                      onClick={() => handleAmenityToggle(amenity)}\n                    >\n                      <div className=\"flex items-center gap-2\">\n                        <div className={`w-4 h-4 rounded border-2 ${\n                          formData.amenities.includes(amenity)\n                            ? 'bg-blue-500 border-blue-500'\n                            : 'border-gray-300'\n                        }`}>\n                          {formData.amenities.includes(amenity) && (\n                            <CheckCircle className=\"w-4 h-4 text-white\" />\n                          )}\n                        </div>\n                        <span className=\"text-sm\">{amenity}</span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {formData.amenities.length > 0 && (\n                <div className=\"p-3 bg-muted/50 rounded-lg\">\n                  <h4 className=\"font-medium text-sm mb-2\">Selected Amenities</h4>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {formData.amenities.map((amenity) => (\n                      <Badge key={amenity} variant=\"secondary\">\n                        {amenity}\n                      </Badge>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        )\n\n      default:\n        return null\n    }\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-emerald-50\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-emerald-600 to-blue-600 text-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-4\">\n                <Button\n                  variant=\"ghost\"\n                  onClick={() => router.back()}\n                  className=\"text-white hover:bg-white/20\"\n                >\n                  <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                  Back\n                </Button>\n                <div>\n                  <h1 className=\"text-3xl font-bold\">Add New Property</h1>\n                  <p className=\"text-emerald-100\">Create a new property listing with complete details</p>\n                </div>\n              </div>\n              <div className=\"flex items-center gap-3\">\n                <Button\n                  variant=\"secondary\"\n                  className=\"bg-white/20 hover:bg-white/30 text-white border-white/30\"\n                >\n                  <Eye className=\"h-4 w-4 mr-2\" />\n                  Preview\n                </Button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          {/* Progress Steps */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center justify-between\">\n              {steps.map((step, index) => (\n                <div key={step.id} className=\"flex items-center\">\n                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${\n                    currentStep >= step.id\n                      ? 'bg-blue-500 border-blue-500 text-white'\n                      : 'border-gray-300 text-gray-500'\n                  }`}>\n                    {React.createElement(step.icon, { className: \"h-5 w-5\" })}\n                  </div>\n                  <div className=\"ml-3\">\n                    <p className={`text-sm font-medium ${\n                      currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'\n                    }`}>\n                      {step.title}\n                    </p>\n                  </div>\n                  {index < steps.length - 1 && (\n                    <div className={`w-16 h-0.5 mx-4 ${\n                      currentStep > step.id ? 'bg-blue-500' : 'bg-gray-300'\n                    }`} />\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Step Content */}\n          <div className=\"mb-8\">\n            {renderStepContent()}\n          </div>\n\n          {/* Navigation */}\n          <div className=\"flex items-center justify-between\">\n            <Button\n              variant=\"outline\"\n              onClick={handlePrevious}\n              disabled={currentStep === 1}\n            >\n              Previous\n            </Button>\n            \n            <div className=\"flex items-center gap-3\">\n              {currentStep < steps.length ? (\n                <Button onClick={handleNext}>\n                  Next\n                </Button>\n              ) : (\n                <Button\n                  onClick={handleSubmit}\n                  disabled={isCreating}\n                  className=\"bg-emerald-600 hover:bg-emerald-700\"\n                >\n                  {isCreating ? (\n                    <>\n                      <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                      Creating...\n                    </>\n                  ) : (\n                    <>\n                      <Save className=\"h-4 w-4 mr-2\" />\n                      Create Property\n                    </>\n                  )}\n                </Button>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAnBA;;;;;;;;;;;;;;;;;;;;AAqCA,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAe,OAAO;QAAe,MAAM;IAAK;IACzD;QAAE,OAAO;QAAc,OAAO;QAAc,MAAM;IAAK;IACvD;QAAE,OAAO;QAAc,OAAO;QAAc,MAAM;IAAK;IACvD;QAAE,OAAO;QAAQ,OAAO;QAAQ,MAAM;IAAK;IAC3C;QAAE,OAAO;QAAS,OAAO;QAAa,MAAM;IAAM;IAClD;QAAE,OAAO;QAAU,OAAO;QAAU,MAAM;IAAI;IAC9C;QAAE,OAAO;QAAgB,OAAO;QAAgB,MAAM;IAAK;CAC5D;AAED,MAAM,gBAAgB;IACpB;IAAiB;IAAO;IAAW;IAAY;IAAU;IACzD;IAAc;IAAgB;IAAY;IAAQ;IAAY;CAC/D;AAEc,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,gBAAgB,EAAE,WAAW,UAAU,EAAE,CAAC,GAAG,CAAA,GAAA,oIAAA,CAAA,4BAAyB,AAAD;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAE3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,oBAAoB;QACpB,MAAM;QACN,aAAa;QACb,cAAc;QAEd,WAAW;QACX,UAAU;YACR,SAAS;YACT,MAAM;YACN,OAAO;YACP,SAAS;YACT,aAAa;gBACX,UAAU;gBACV,WAAW;YACb;QACF;QAEA,oBAAoB;QACpB,iBAAiB;QACjB,sBAAsB;QACtB,aAAa;QACb,eAAe;QACf,iBAAiB;QACjB,mBAAmB;QACnB,mBAAmB;QACnB,aAAa;QACb,kBAAkB;QAClB,wBAAwB;QACxB,qBAAqB;QACrB,4BAA4B;QAC5B,yBAAyB;QACzB,gBAAgB;QAEhB,uBAAuB;QACvB,oBAAoB;QACpB,YAAY;QACZ,oBAAoB;QACpB,kBAAkB;QAClB,sBAAsB;QAEtB,sBAAsB;QACtB,SAAS;QACT,aAAa;QAEb,oBAAoB;QACpB,WAAW,EAAE;QACb,UAAU,EAAE;QACZ,gBAAgB,CAAC;QAEjB,cAAc;QACd,QAAQ,EAAE;QACV,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB,EAAE;QAElB,iBAAiB;QACjB,QAAQ;QACR,UAAU;QACV,eAAe;IACjB;IAEA,MAAM,QAAQ;QACZ;YAAE,IAAI;YAAG,OAAO;YAAqB,MAAM,8RAAA,CAAA,WAAQ;QAAC;QACpD;YAAE,IAAI;YAAG,OAAO;YAAoB,MAAM,8RAAA,CAAA,SAAM;QAAC;QACjD;YAAE,IAAI;YAAG,OAAO;YAAqB,MAAM,sRAAA,CAAA,OAAI;QAAC;QAChD;YAAE,IAAI;YAAG,OAAO;YAAqB,MAAM,sSAAA,CAAA,aAAU;QAAC;QACtD;YAAE,IAAI;YAAG,OAAO;YAAyB,MAAM,8RAAA,CAAA,WAAQ;QAAC;QACxD;YAAE,IAAI;YAAG,OAAO;YAAoB,MAAM,0RAAA,CAAA,SAAM;QAAC;KAClD;IAED,MAAM,oBAAoB,CAAC,OAAe;QACxC,IAAI,MAAM,QAAQ,CAAC,MAAM;YACvB,MAAM,OAAO,MAAM,KAAK,CAAC;YACzB,YAAY,CAAA;gBACV,MAAM,UAAU;oBAAE,GAAG,IAAI;gBAAC;gBAC1B,IAAI,UAAU;gBACd,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,IAAK;oBACxC,UAAU,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC5B;gBACA,OAAO,CAAC,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG;gBACjC,OAAO;YACT;QACF,OAAO;YACL,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE;gBACX,CAAC;QACH;QAEA,6BAA6B;QAC7B,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAC,OAAc,CAAC;oBACxB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE;gBACX,CAAC;QACH;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP;YACF,CAAC;IACH;IAEA,MAAM,wBAAwB,CAAC;QAC7B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,GAAG,SAAS;YACd,CAAC;IACH;IAEA,MAAM,2BAA2B,CAAC;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,GAAG,YAAY;YACjB,CAAC;IACH;IAEA,MAAM,6BAA6B,CAAC;QAClC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,GAAG,cAAc;YACnB,CAAC;IACH;IAEA,MAAM,sBAAsB,CAAC;QAC3B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,WAAW,KAAK,SAAS,CAAC,QAAQ,CAAC,WAC/B,KAAK,SAAS,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,WACjC;uBAAI,KAAK,SAAS;oBAAE;iBAAQ;YAClC,CAAC;IACH;IAEA,MAAM,sBAAsB;QAC1B,MAAM,SAAS,qIAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC;QAC5C,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,cAAc,OAAO,KAAK,CAAC,OAAO,GAAG,WAAW;YACtD,UAAU;YACV,OAAO;QACT;QACA,UAAU,CAAC;QACX,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,MAAM,MAAM,EAAE;YAC9B,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,uBAAuB;YAC1B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,eAAe,UAAU,MAAM;YACpD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,MAAM,WAAW;QACtC;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,6WAAC,gIAAA,CAAA,OAAI;;sCACH,6WAAC,gIAAA,CAAA,aAAU;;8CACT,6WAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6WAAC,8RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGlC,6WAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,6WAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAO;;;;;;8DACtB,6WAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,aAAY;oDACZ,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACzD,WAAW,OAAO,IAAI,GAAG,mBAAmB;;;;;;gDAE7C,OAAO,IAAI,kBACV,6WAAC;oDAAE,WAAU;;sEACX,6WAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDACtB,OAAO,IAAI,CAAC,EAAE;;;;;;;;;;;;;sDAKrB,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,6WAAC,kIAAA,CAAA,SAAM;oDACL,OAAO,SAAS,YAAY;oDAC5B,eAAe,CAAC,QAAU,kBAAkB,gBAAgB;;sEAE5D,6WAAC,kIAAA,CAAA,gBAAa;4DAAC,WAAW,OAAO,YAAY,GAAG,mBAAmB;sEACjE,cAAA,6WAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6WAAC,kIAAA,CAAA,gBAAa;sEACX,cAAc,GAAG,CAAC,CAAC,qBAClB,6WAAC,kIAAA,CAAA,aAAU;oEAAkB,OAAO,KAAK,KAAK;8EAC5C,cAAA,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;0FAAM,KAAK,IAAI;;;;;;4EACf,KAAK,KAAK;;;;;;;mEAHE,KAAK,KAAK;;;;;;;;;;;;;;;;gDAShC,OAAO,YAAY,kBAClB,6WAAC;oDAAE,WAAU;;sEACX,6WAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDACtB,OAAO,YAAY,CAAC,EAAE;;;;;;;;;;;;;;;;;;;8CAM/B,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAc;;;;;;sDAC7B,6WAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,aAAY;4CACZ,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4CAChE,WAAW,OAAO,WAAW,GAAG,mBAAmB;4CACnD,MAAM;;;;;;wCAEP,OAAO,WAAW,kBACjB,6WAAC;4CAAE,WAAU;;8DACX,6WAAC,wSAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDACtB,OAAO,WAAW,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;YAQpC,KAAK;gBACH,qBACE,6WAAC,yJAAA,CAAA,UAAc;oBACb,OAAO,SAAS,QAAQ;oBACxB,UAAU;oBACV,QAAQ,OAAO,QAAQ;;;;;;YAI7B,KAAK;gBACH,qBACE,6WAAC,iKAAA,CAAA,UAAsB;oBACrB,OAAO;wBACL,SAAS,SAAS,OAAO;wBACzB,aAAa,SAAS,WAAW;oBACnC;oBACA,UAAU;oBACV,QAAQ;wBACN,SAAS,OAAO,OAAO;wBACvB,aAAa,OAAO,WAAW;oBACjC;;;;;;YAIN,KAAK;gBACH,qBACE,6WAAC,2JAAA,CAAA,UAAgB;oBACf,OAAO;wBACL,iBAAiB,SAAS,eAAe;wBACzC,sBAAsB,SAAS,oBAAoB;wBACnD,aAAa,SAAS,WAAW;wBACjC,eAAe,SAAS,aAAa;wBACrC,iBAAiB,SAAS,eAAe;wBACzC,mBAAmB,SAAS,iBAAiB;wBAC7C,mBAAmB,SAAS,iBAAiB;wBAC7C,aAAa,SAAS,WAAW;wBACjC,kBAAkB,SAAS,gBAAgB;wBAC3C,wBAAwB,SAAS,sBAAsB;wBACvD,qBAAqB,SAAS,mBAAmB;wBACjD,4BAA4B,SAAS,0BAA0B;wBAC/D,yBAAyB,SAAS,uBAAuB;wBACzD,gBAAgB,SAAS,cAAc;oBACzC;oBACA,UAAU;oBACV,QAAQ;;;;;;YAId,KAAK;gBACH,qBACE,6WAAC,+JAAA,CAAA,UAAoB;oBACnB,OAAO;wBACL,oBAAoB,SAAS,kBAAkB;wBAC/C,YAAY,SAAS,UAAU;wBAC/B,oBAAoB,SAAS,kBAAkB;wBAC/C,kBAAkB,SAAS,gBAAgB;wBAC3C,sBAAsB,SAAS,oBAAoB;oBACrD;oBACA,UAAU;oBACV,QAAQ;wBACN,oBAAoB,OAAO,kBAAkB;wBAC7C,YAAY,OAAO,UAAU;wBAC7B,oBAAoB,OAAO,kBAAkB;wBAC7C,kBAAkB,OAAO,gBAAgB;wBACzC,sBAAsB,OAAO,oBAAoB;oBACnD;;;;;;YAIN,KAAK;gBACH,qBACE,6WAAC,gIAAA,CAAA,OAAI;;sCACH,6WAAC,gIAAA,CAAA,aAAU;;8CACT,6WAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6WAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGhC,6WAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,6WAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,6WAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAC,wBAClB,6WAAC;oDAEC,WAAW,CAAC,uDAAuD,EACjE,SAAS,SAAS,CAAC,QAAQ,CAAC,WACxB,+BACA,oBACJ;oDACF,SAAS,IAAM,oBAAoB;8DAEnC,cAAA,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAI,WAAW,CAAC,yBAAyB,EACxC,SAAS,SAAS,CAAC,QAAQ,CAAC,WACxB,gCACA,mBACJ;0EACC,SAAS,SAAS,CAAC,QAAQ,CAAC,0BAC3B,6WAAC,+SAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;0EAG3B,6WAAC;gEAAK,WAAU;0EAAW;;;;;;;;;;;;mDAlBxB;;;;;;;;;;;;;;;;gCAyBZ,SAAS,SAAS,CAAC,MAAM,GAAG,mBAC3B,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,6WAAC;4CAAI,WAAU;sDACZ,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,wBACvB,6WAAC,iIAAA,CAAA,QAAK;oDAAe,SAAQ;8DAC1B;mDADS;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAW5B;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6WAAC,+IAAA,CAAA,UAAe;kBACd,cAAA,6WAAC;YAAI,WAAU;;8BAEb,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,OAAO,IAAI;4CAC1B,WAAU;;8DAEV,6WAAC,oSAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGxC,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,6WAAC;oDAAE,WAAU;8DAAmB;;;;;;;;;;;;;;;;;;8CAGpC,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;;0DAEV,6WAAC,oRAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ1C,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6WAAC;wCAAkB,WAAU;;0DAC3B,6WAAC;gDAAI,WAAW,CAAC,iEAAiE,EAChF,eAAe,KAAK,EAAE,GAClB,2CACA,iCACJ;0DACC,cAAA,oUAAA,CAAA,UAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;oDAAE,WAAW;gDAAU;;;;;;0DAEzD,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAE,WAAW,CAAC,oBAAoB,EACjC,eAAe,KAAK,EAAE,GAAG,kBAAkB,iBAC3C;8DACC,KAAK,KAAK;;;;;;;;;;;4CAGd,QAAQ,MAAM,MAAM,GAAG,mBACtB,6WAAC;gDAAI,WAAW,CAAC,gBAAgB,EAC/B,cAAc,KAAK,EAAE,GAAG,gBAAgB,eACxC;;;;;;;uCAlBI,KAAK,EAAE;;;;;;;;;;;;;;;sCA0BvB,6WAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIH,6WAAC;4BAAI,WAAU;;8CACb,6WAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU,gBAAgB;8CAC3B;;;;;;8CAID,6WAAC;oCAAI,WAAU;8CACZ,cAAc,MAAM,MAAM,iBACzB,6WAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;kDAAY;;;;;6DAI7B,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,2BACC;;8DACE,6WAAC;oDAAI,WAAU;;;;;;gDAAuE;;yEAIxF;;8DACE,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvD", "debugId": null}}]}