'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { useCreatePropertyMutation } from '@/store/api/propertiesApi'
import LocationPicker from '@/components/forms/property/LocationPicker'
import FinancialDetails from '@/components/forms/property/FinancialDetails'
import ConstructionTimeline from '@/components/forms/property/ConstructionTimeline'
import OwnerDeveloperSelector from '@/components/forms/property/OwnerDeveloperSelector'
import { PropertyFormSchema, type PropertyFormData } from '@/lib/validations/property'
import {
  ArrowLeft,
  Save,
  Building,
  MapPin,
  DollarSign,
  Calendar,
  User,
  Upload,
  Eye,
  Plus,
  Minus,
  AlertCircle,
  CheckCircle,
  Info,
  X
} from 'lucide-react'

const propertyTypes = [
  { value: 'residential', label: 'Residential', icon: '🏠' },
  { value: 'commercial', label: 'Commercial', icon: '🏢' },
  { value: 'industrial', label: 'Industrial', icon: '🏭' },
  { value: 'land', label: 'Land', icon: '🌍' },
  { value: 'mixed', label: 'Mixed Use', icon: '🏘️' },
  { value: 'luxury', label: 'Luxury', icon: '✨' },
  { value: 'eco_friendly', label: 'Eco-Friendly', icon: '🌱' }
]

const amenitiesList = [
  'Swimming Pool', 'Gym', 'Parking', 'Security', 'Garden', 'Playground',
  'Club House', 'Power Backup', 'Elevator', 'CCTV', 'Intercom', 'Water Supply'
]

export default function AddEnhancedPropertyPage() {
  const router = useRouter()
  const [createProperty, { isLoading: isCreating }] = useCreatePropertyMutation()
  const [currentStep, setCurrentStep] = useState(1)
  const [errors, setErrors] = useState<any>({})

  const [formData, setFormData] = useState<PropertyFormData>({
    // Basic Information
    name: '',
    description: '',
    propertyType: 'residential',
    
    // Location
    location: {
      address: '',
      city: '',
      state: '',
      pincode: '',
      coordinates: {
        latitude: 0,
        longitude: 0
      }
    },
    
    // Financial Details
    expectedReturns: 0,
    maturityPeriodMonths: 12,
    totalStocks: 100,
    pricePerStock: 1000,
    availableStocks: 100,
    minimumInvestment: 1000,
    maximumInvestment: 100000,
    stockPrefix: 'PROP',
    stockStartNumber: 1,
    referralCommissionRate: 2,
    salesCommissionRate: 1,
    referralCommissionPerStock: 20,
    salesCommissionPerStock: 10,
    commissionType: 'percentage',
    
    // Construction Details
    constructionStatus: 'planning',
    launchDate: '',
    expectedCompletion: '',
    actualCompletion: '',
    constructionTimeline: '',
    
    // Owner and Developer
    ownerId: '',
    developerId: '',
    
    // Property Features
    amenities: [],
    features: [],
    specifications: {},
    
    // Media Files
    images: [],
    documents: [],
    videos: [],
    legalDocuments: [],
    
    // Administrative
    status: 'active',
    featured: false,
    priorityOrder: 0
  })

  const steps = [
    { id: 1, title: 'Basic Information', icon: Building },
    { id: 2, title: 'Location Details', icon: MapPin },
    { id: 3, title: 'Owner & Developer', icon: User },
    { id: 4, title: 'Financial Details', icon: DollarSign },
    { id: 5, title: 'Construction Timeline', icon: Calendar },
    { id: 6, title: 'Features & Media', icon: Upload }
  ]

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const keys = field.split('.')
      setFormData(prev => {
        const updated = { ...prev }
        let current = updated as any
        for (let i = 0; i < keys.length - 1; i++) {
          current = current[keys[i]]
        }
        current[keys[keys.length - 1]] = value
        return updated
      })
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }))
    }
    
    // Clear error for this field
    if (errors[field]) {
      setErrors((prev: any) => ({
        ...prev,
        [field]: undefined
      }))
    }
  }

  const handleLocationChange = (location: any) => {
    setFormData(prev => ({
      ...prev,
      location
    }))
  }

  const handleFinancialChange = (financial: any) => {
    setFormData(prev => ({
      ...prev,
      ...financial
    }))
  }

  const handleConstructionChange = (construction: any) => {
    setFormData(prev => ({
      ...prev,
      ...construction
    }))
  }

  const handleOwnerDeveloperChange = (ownerDeveloper: any) => {
    setFormData(prev => ({
      ...prev,
      ...ownerDeveloper
    }))
  }

  const handleAmenityToggle = (amenity: string) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }))
  }

  const validateCurrentStep = () => {
    const result = PropertyFormSchema.safeParse(formData)
    if (!result.success) {
      const fieldErrors = result.error.flatten().fieldErrors
      setErrors(fieldErrors)
      return false
    }
    setErrors({})
    return true
  }

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = async () => {
    if (!validateCurrentStep()) {
      toast.error('Please fix the validation errors before submitting')
      return
    }

    try {
      const result = await createProperty(formData).unwrap()
      toast.success('Property created successfully!')
      router.push('/properties')
    } catch (error: any) {
      console.error('Error creating property:', error)
      toast.error(error?.data?.message || 'Failed to create property')
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Basic Information
              </CardTitle>
              <CardDescription>
                Enter the property's basic details and description
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Property Name *</Label>
                  <Input
                    id="name"
                    placeholder="Enter property name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.name[0]}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="propertyType">Property Type *</Label>
                  <Select
                    value={formData.propertyType}
                    onValueChange={(value) => handleInputChange('propertyType', value)}
                  >
                    <SelectTrigger className={errors.propertyType ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select property type" />
                    </SelectTrigger>
                    <SelectContent>
                      {propertyTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          <div className="flex items-center gap-2">
                            <span>{type.icon}</span>
                            {type.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.propertyType && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.propertyType[0]}
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  placeholder="Enter detailed property description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className={errors.description ? 'border-red-500' : ''}
                  rows={4}
                />
                {errors.description && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.description[0]}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        )

      case 2:
        return (
          <LocationPicker
            value={formData.location}
            onChange={handleLocationChange}
            errors={errors.location}
          />
        )

      case 3:
        return (
          <OwnerDeveloperSelector
            value={{
              ownerId: formData.ownerId,
              developerId: formData.developerId
            }}
            onChange={handleOwnerDeveloperChange}
            errors={{
              ownerId: errors.ownerId,
              developerId: errors.developerId
            }}
          />
        )

      case 4:
        return (
          <FinancialDetails
            value={{
              expectedReturns: formData.expectedReturns,
              maturityPeriodMonths: formData.maturityPeriodMonths,
              totalStocks: formData.totalStocks,
              pricePerStock: formData.pricePerStock,
              availableStocks: formData.availableStocks,
              minimumInvestment: formData.minimumInvestment,
              maximumInvestment: formData.maximumInvestment,
              stockPrefix: formData.stockPrefix,
              stockStartNumber: formData.stockStartNumber,
              referralCommissionRate: formData.referralCommissionRate,
              salesCommissionRate: formData.salesCommissionRate,
              referralCommissionPerStock: formData.referralCommissionPerStock,
              salesCommissionPerStock: formData.salesCommissionPerStock,
              commissionType: formData.commissionType
            }}
            onChange={handleFinancialChange}
            errors={errors}
          />
        )

      case 5:
        return (
          <ConstructionTimeline
            value={{
              constructionStatus: formData.constructionStatus,
              launchDate: formData.launchDate,
              expectedCompletion: formData.expectedCompletion,
              actualCompletion: formData.actualCompletion,
              constructionTimeline: formData.constructionTimeline
            }}
            onChange={handleConstructionChange}
            errors={{
              constructionStatus: errors.constructionStatus,
              launchDate: errors.launchDate,
              expectedCompletion: errors.expectedCompletion,
              actualCompletion: errors.actualCompletion,
              constructionTimeline: errors.constructionTimeline
            }}
          />
        )

      case 6:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Features & Amenities
              </CardTitle>
              <CardDescription>
                Select property amenities and features
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <Label>Amenities</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {amenitiesList.map((amenity) => (
                    <div
                      key={amenity}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        formData.amenities.includes(amenity)
                          ? 'bg-blue-50 border-blue-300'
                          : 'hover:bg-gray-50'
                      }`}
                      onClick={() => handleAmenityToggle(amenity)}
                    >
                      <div className="flex items-center gap-2">
                        <div className={`w-4 h-4 rounded border-2 ${
                          formData.amenities.includes(amenity)
                            ? 'bg-blue-500 border-blue-500'
                            : 'border-gray-300'
                        }`}>
                          {formData.amenities.includes(amenity) && (
                            <CheckCircle className="w-4 h-4 text-white" />
                          )}
                        </div>
                        <span className="text-sm">{amenity}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {formData.amenities.length > 0 && (
                <div className="p-3 bg-muted/50 rounded-lg">
                  <h4 className="font-medium text-sm mb-2">Selected Amenities</h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.amenities.map((amenity) => (
                      <Badge key={amenity} variant="secondary">
                        {amenity}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )

      default:
        return null
    }
  }

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-emerald-50">
        {/* Header */}
        <div className="bg-gradient-to-r from-emerald-600 to-blue-600 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  onClick={() => router.back()}
                  className="text-white hover:bg-white/20"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
                <div>
                  <h1 className="text-3xl font-bold">Add New Property</h1>
                  <p className="text-emerald-100">Create a new property listing with complete details</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  variant="secondary"
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Progress Steps */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                    currentStep >= step.id
                      ? 'bg-blue-500 border-blue-500 text-white'
                      : 'border-gray-300 text-gray-500'
                  }`}>
                    {React.createElement(step.icon, { className: "h-5 w-5" })}
                  </div>
                  <div className="ml-3">
                    <p className={`text-sm font-medium ${
                      currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'
                    }`}>
                      {step.title}
                    </p>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-16 h-0.5 mx-4 ${
                      currentStep > step.id ? 'bg-blue-500' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Step Content */}
          <div className="mb-8">
            {renderStepContent()}
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 1}
            >
              Previous
            </Button>
            
            <div className="flex items-center gap-3">
              {currentStep < steps.length ? (
                <Button onClick={handleNext}>
                  Next
                </Button>
              ) : (
                <Button
                  onClick={handleSubmit}
                  disabled={isCreating}
                  className="bg-emerald-600 hover:bg-emerald-700"
                >
                  {isCreating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Creating...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Create Property
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
