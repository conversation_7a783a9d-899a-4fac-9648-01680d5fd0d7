'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Calendar, Building, Clock, AlertCircle, CheckCircle, Info } from 'lucide-react'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface ConstructionData {
  constructionStatus: string
  launchDate: string
  expectedCompletion: string
  actualCompletion?: string
  constructionTimeline?: string
}

interface ConstructionTimelineProps {
  value: ConstructionData
  onChange: (construction: ConstructionData) => void
  errors?: {
    constructionStatus?: string[]
    launchDate?: string[]
    expectedCompletion?: string[]
    actualCompletion?: string[]
    constructionTimeline?: string[]
  }
}

const constructionStatuses = [
  { value: 'planning', label: 'Planning', color: 'bg-blue-500' },
  { value: 'foundation', label: 'Foundation', color: 'bg-orange-500' },
  { value: 'structure', label: 'Structure', color: 'bg-yellow-500' },
  { value: 'finishing', label: 'Finishing', color: 'bg-purple-500' },
  { value: 'completed', label: 'Completed', color: 'bg-green-500' }
]

export default function ConstructionTimeline({ value, onChange, errors }: ConstructionTimelineProps) {
  const handleInputChange = (field: keyof ConstructionData, inputValue: string) => {
    onChange({
      ...value,
      [field]: inputValue
    })
  }

  // Calculate project duration
  const calculateDuration = () => {
    if (value.launchDate && value.expectedCompletion) {
      const launch = new Date(value.launchDate)
      const completion = new Date(value.expectedCompletion)
      const diffTime = completion.getTime() - launch.getTime()
      const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30))
      return diffMonths > 0 ? diffMonths : 0
    }
    return 0
  }

  // Check if dates are valid
  const isValidDateRange = () => {
    if (value.launchDate && value.expectedCompletion) {
      return new Date(value.launchDate) < new Date(value.expectedCompletion)
    }
    return true
  }

  const currentStatus = constructionStatuses.find(status => status.value === value.constructionStatus)
  const projectDuration = calculateDuration()

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building className="h-5 w-5" />
          Construction Timeline
        </CardTitle>
        <CardDescription>
          Configure construction phases and project timeline
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Construction Status */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="constructionStatus">Construction Status *</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <Info className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Current phase of construction</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Select
            value={value.constructionStatus}
            onValueChange={(val) => handleInputChange('constructionStatus', val)}
          >
            <SelectTrigger className={errors?.constructionStatus ? 'border-red-500' : ''}>
              <SelectValue placeholder="Select construction status" />
            </SelectTrigger>
            <SelectContent>
              {constructionStatuses.map((status) => (
                <SelectItem key={status.value} value={status.value}>
                  <div className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${status.color}`} />
                    {status.label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors?.constructionStatus && (
            <p className="text-sm text-red-500 flex items-center gap-1">
              <AlertCircle className="h-4 w-4" />
              {errors.constructionStatus[0]}
            </p>
          )}
          {currentStatus && (
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${currentStatus.color}`} />
                Current: {currentStatus.label}
              </Badge>
            </div>
          )}
        </div>

        {/* Project Dates */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Label htmlFor="launchDate">Launch Date *</Label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="h-4 w-4 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Project launch or start date</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="relative">
              <Input
                id="launchDate"
                type="date"
                value={value.launchDate}
                onChange={(e) => handleInputChange('launchDate', e.target.value)}
                className={errors?.launchDate ? 'border-red-500' : ''}
              />
              <Calendar className="absolute right-3 top-3 h-4 w-4 text-muted-foreground pointer-events-none" />
            </div>
            {errors?.launchDate && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.launchDate[0]}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Label htmlFor="expectedCompletion">Expected Completion *</Label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="h-4 w-4 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Expected project completion date</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="relative">
              <Input
                id="expectedCompletion"
                type="date"
                value={value.expectedCompletion}
                onChange={(e) => handleInputChange('expectedCompletion', e.target.value)}
                className={errors?.expectedCompletion ? 'border-red-500' : ''}
                min={value.launchDate}
              />
              <Calendar className="absolute right-3 top-3 h-4 w-4 text-muted-foreground pointer-events-none" />
            </div>
            {errors?.expectedCompletion && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.expectedCompletion[0]}
              </p>
            )}
            {!isValidDateRange() && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                Completion date must be after launch date
              </p>
            )}
          </div>
        </div>

        {/* Actual Completion (if completed) */}
        {value.constructionStatus === 'completed' && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Label htmlFor="actualCompletion">Actual Completion Date</Label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="h-4 w-4 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Actual date when construction was completed</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="relative">
              <Input
                id="actualCompletion"
                type="date"
                value={value.actualCompletion || ''}
                onChange={(e) => handleInputChange('actualCompletion', e.target.value)}
                className={errors?.actualCompletion ? 'border-red-500' : ''}
                max={new Date().toISOString().split('T')[0]}
              />
              <Calendar className="absolute right-3 top-3 h-4 w-4 text-muted-foreground pointer-events-none" />
            </div>
            {errors?.actualCompletion && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.actualCompletion[0]}
              </p>
            )}
          </div>
        )}

        {/* Construction Timeline Description */}
        <div className="space-y-2">
          <Label htmlFor="constructionTimeline">Construction Timeline Description</Label>
          <Input
            id="constructionTimeline"
            placeholder="Brief description of construction phases and milestones"
            value={value.constructionTimeline || ''}
            onChange={(e) => handleInputChange('constructionTimeline', e.target.value)}
            className={errors?.constructionTimeline ? 'border-red-500' : ''}
          />
          {errors?.constructionTimeline && (
            <p className="text-sm text-red-500 flex items-center gap-1">
              <AlertCircle className="h-4 w-4" />
              {errors.constructionTimeline[0]}
            </p>
          )}
        </div>

        {/* Project Summary */}
        {(value.launchDate && value.expectedCompletion && isValidDateRange()) && (
          <div className="p-4 bg-muted/50 rounded-lg space-y-3">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Project Summary
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div className="text-center">
                <Badge variant="secondary" className="w-full justify-center">
                  Duration
                </Badge>
                <p className="text-sm font-medium mt-1">
                  {projectDuration} months
                </p>
              </div>
              
              <div className="text-center">
                <Badge variant="secondary" className="w-full justify-center">
                  Launch Date
                </Badge>
                <p className="text-sm font-medium mt-1">
                  {new Date(value.launchDate).toLocaleDateString('en-IN')}
                </p>
              </div>
              
              <div className="text-center">
                <Badge variant="secondary" className="w-full justify-center">
                  Expected Completion
                </Badge>
                <p className="text-sm font-medium mt-1">
                  {new Date(value.expectedCompletion).toLocaleDateString('en-IN')}
                </p>
              </div>
            </div>
            
            {value.actualCompletion && (
              <div className="text-center">
                <Badge variant="outline" className="flex items-center gap-1 justify-center">
                  <CheckCircle className="h-3 w-3" />
                  Completed on {new Date(value.actualCompletion).toLocaleDateString('en-IN')}
                </Badge>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
