'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Briefcase, Building, CreditCard, AlertCircle, Info } from 'lucide-react'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface BusinessData {
  company?: string
  businessType?: string
  gstNumber?: string
  panNumber?: string
  bankDetails: {
    accountNumber: string
    ifscCode: string
    bankName: string
    accountHolderName: string
  }
}

interface BusinessInformationProps {
  value: BusinessData
  onChange: (business: BusinessData) => void
  errors?: {
    company?: string[]
    businessType?: string[]
    gstNumber?: string[]
    panNumber?: string[]
    bankDetails?: {
      accountNumber?: string[]
      ifscCode?: string[]
      bankName?: string[]
      accountHolderName?: string[]
    }
  }
  showBusinessFields?: boolean
}

const businessTypes = [
  'Sole Proprietorship',
  'Partnership',
  'Private Limited Company',
  'Public Limited Company',
  'Limited Liability Partnership (LLP)',
  'One Person Company (OPC)',
  'Trust',
  'Society',
  'Cooperative Society',
  'Other'
]

export default function BusinessInformation({ 
  value, 
  onChange, 
  errors, 
  showBusinessFields = true 
}: BusinessInformationProps) {
  const handleInputChange = (field: string, inputValue: string) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      onChange({
        ...value,
        [parent]: {
          ...value[parent as keyof BusinessData],
          [child]: inputValue
        }
      })
    } else {
      onChange({
        ...value,
        [field]: inputValue
      })
    }
  }

  // Validate GST number format
  const validateGST = (gst: string) => {
    const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/
    return gstRegex.test(gst)
  }

  // Validate PAN number format
  const validatePAN = (pan: string) => {
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/
    return panRegex.test(pan)
  }

  // Validate IFSC code format
  const validateIFSC = (ifsc: string) => {
    const ifscRegex = /^[A-Z]{4}0[A-Z0-9]{6}$/
    return ifscRegex.test(ifsc)
  }

  return (
    <div className="space-y-6">
      {/* Business Information */}
      {showBusinessFields && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="h-5 w-5" />
              Business Information
            </CardTitle>
            <CardDescription>
              Company details and business registration information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="company">Company Name</Label>
                <div className="relative">
                  <Input
                    id="company"
                    placeholder="Enter company name"
                    value={value.company || ''}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    className={errors?.company ? 'border-red-500' : ''}
                  />
                  <Building className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                </div>
                {errors?.company && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.company[0]}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="businessType">Business Type</Label>
                <Select
                  value={value.businessType || ''}
                  onValueChange={(val) => handleInputChange('businessType', val)}
                >
                  <SelectTrigger className={errors?.businessType ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select business type" />
                  </SelectTrigger>
                  <SelectContent>
                    {businessTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors?.businessType && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.businessType[0]}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="gstNumber">GST Number</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>15-digit GST identification number</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="gstNumber"
                  placeholder="e.g., 22AAAAA0000A1Z5"
                  value={value.gstNumber || ''}
                  onChange={(e) => handleInputChange('gstNumber', e.target.value.toUpperCase())}
                  className={errors?.gstNumber ? 'border-red-500' : ''}
                  maxLength={15}
                />
                {errors?.gstNumber && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.gstNumber[0]}
                  </p>
                )}
                {value.gstNumber && !validateGST(value.gstNumber) && value.gstNumber.length === 15 && (
                  <p className="text-sm text-amber-600">
                    Invalid GST number format
                  </p>
                )}
                {value.gstNumber && validateGST(value.gstNumber) && (
                  <div className="flex items-center gap-1">
                    <Badge variant="secondary" className="text-green-600">
                      Valid GST Format
                    </Badge>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="panNumber">PAN Number</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>10-character PAN (Permanent Account Number)</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="panNumber"
                  placeholder="e.g., **********"
                  value={value.panNumber || ''}
                  onChange={(e) => handleInputChange('panNumber', e.target.value.toUpperCase())}
                  className={errors?.panNumber ? 'border-red-500' : ''}
                  maxLength={10}
                />
                {errors?.panNumber && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.panNumber[0]}
                  </p>
                )}
                {value.panNumber && !validatePAN(value.panNumber) && value.panNumber.length === 10 && (
                  <p className="text-sm text-amber-600">
                    Invalid PAN number format
                  </p>
                )}
                {value.panNumber && validatePAN(value.panNumber) && (
                  <div className="flex items-center gap-1">
                    <Badge variant="secondary" className="text-green-600">
                      Valid PAN Format
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bank Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Bank Details
          </CardTitle>
          <CardDescription>
            Banking information for transactions and payments
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="accountHolderName">Account Holder Name *</Label>
              <Input
                id="accountHolderName"
                placeholder="Enter account holder name"
                value={value.bankDetails.accountHolderName}
                onChange={(e) => handleInputChange('bankDetails.accountHolderName', e.target.value)}
                className={errors?.bankDetails?.accountHolderName ? 'border-red-500' : ''}
              />
              {errors?.bankDetails?.accountHolderName && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.bankDetails.accountHolderName[0]}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="bankName">Bank Name *</Label>
              <Input
                id="bankName"
                placeholder="Enter bank name"
                value={value.bankDetails.bankName}
                onChange={(e) => handleInputChange('bankDetails.bankName', e.target.value)}
                className={errors?.bankDetails?.bankName ? 'border-red-500' : ''}
              />
              {errors?.bankDetails?.bankName && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.bankDetails.bankName[0]}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="accountNumber">Account Number *</Label>
              <Input
                id="accountNumber"
                placeholder="Enter account number"
                value={value.bankDetails.accountNumber}
                onChange={(e) => handleInputChange('bankDetails.accountNumber', e.target.value)}
                className={errors?.bankDetails?.accountNumber ? 'border-red-500' : ''}
              />
              {errors?.bankDetails?.accountNumber && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.bankDetails.accountNumber[0]}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Label htmlFor="ifscCode">IFSC Code *</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>11-character bank IFSC code</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="ifscCode"
                placeholder="e.g., SBIN0001234"
                value={value.bankDetails.ifscCode}
                onChange={(e) => handleInputChange('bankDetails.ifscCode', e.target.value.toUpperCase())}
                className={errors?.bankDetails?.ifscCode ? 'border-red-500' : ''}
                maxLength={11}
              />
              {errors?.bankDetails?.ifscCode && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.bankDetails.ifscCode[0]}
                </p>
              )}
              {value.bankDetails.ifscCode && !validateIFSC(value.bankDetails.ifscCode) && value.bankDetails.ifscCode.length === 11 && (
                <p className="text-sm text-amber-600">
                  Invalid IFSC code format
                </p>
              )}
              {value.bankDetails.ifscCode && validateIFSC(value.bankDetails.ifscCode) && (
                <div className="flex items-center gap-1">
                  <Badge variant="secondary" className="text-green-600">
                    Valid IFSC Format
                  </Badge>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
