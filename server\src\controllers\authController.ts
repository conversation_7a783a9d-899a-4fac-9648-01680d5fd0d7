import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { User } from '../models/User';
import { UserWallet } from '../models/UserWallet';
import { OTP, OTPType, OTPStatus } from '../models/OTP';
import { JWTUtils } from '../utils/jwt';
import { CookieUtils } from '../utils/cookies';
import { ApiResponse, UserStatus } from '../types';
import { detectClientType } from '../middleware/universalAuth';
import { SettingsService } from '../services/settingsService';
import { emailService } from '../services/emailService';
import crypto from 'crypto';
import jwt from 'jsonwebtoken';

/**
 * Format authentication response based on client type
 */
const formatAuthResponse = (req: Request, res: Response, user: any, accessToken: string, refreshToken: string, message: string) => {
  const clientType = detectClientType(req);

  // Always set cookies for web clients, optionally for others
  if (clientType === 'web') {
    CookieUtils.setAuthCookies(res, accessToken, refreshToken);
  } else {
    // For mobile/API, set cookies as backup but don't rely on them
    CookieUtils.setAuthCookies(res, accessToken, refreshToken);
  }

  const response: ApiResponse = {
    success: true,
    message,
    data: {
      user: user.toJSON(),
      accessToken,
      refreshToken,
      clientType,
      authMethods: {
        primary: clientType === 'web' ? 'cookies' : 'authorization-header',
        cookies: {
          set: true,
          note: clientType === 'web' ? 'Use automatically' : 'Available as backup'
        },
        header: {
          format: 'Authorization: Bearer <accessToken>',
          recommended: clientType !== 'web'
        },
        body: {
          format: '{ "accessToken": "<token>" }',
          fallback: true
        }
      },
      implementation: getImplementationGuide(clientType)
    }
  };

  return response;
};

/**
 * Get implementation guide based on client type
 */
const getImplementationGuide = (clientType: string) => {
  switch (clientType) {
    case 'web':
      return {
        type: 'Web Browser',
        method: 'Cookies (Automatic)',
        code: `
// JavaScript/Fetch
fetch('/api/protected-endpoint', {
  credentials: 'include' // Important: includes cookies
});

// Axios
axios.defaults.withCredentials = true;
axios.get('/api/protected-endpoint');
        `.trim(),
        notes: [
          'Cookies are set automatically',
          'Use credentials: "include" in fetch',
          'Set withCredentials: true in axios',
          'No manual token management needed'
        ]
      };
    case 'mobile':
      return {
        type: 'Mobile App',
        method: 'Authorization Header',
        code: `
// Android (Kotlin/Retrofit)
@GET("protected-endpoint")
suspend fun getData(
  @Header("Authorization") token: String
): Response<Data>

// React Native
fetch('/api/protected-endpoint', {
  headers: {
    'Authorization': 'Bearer ' + accessToken
  }
});
        `.trim(),
        notes: [
          'Store accessToken securely',
          'Add Authorization header to requests',
          'Handle token refresh manually',
          'Use secure storage for tokens'
        ]
      };
    default:
      return {
        type: 'API Client',
        method: 'Authorization Header or Body',
        code: `
// Header method (recommended)
curl -H "Authorization: Bearer <token>" /api/endpoint

// Body method (alternative)
curl -d '{"accessToken":"<token>"}' /api/endpoint
        `.trim(),
        notes: [
          'Authorization header is preferred',
          'Body method available as fallback',
          'Both methods work simultaneously',
          'Choose based on your client capabilities'
        ]
      };
  }
};

/**
 * Register a new user
 */
export const register = async (req: Request, res: Response): Promise<void> => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        message: 'Validation failed',
        error: 'VALIDATION_ERROR',
        details: errors.array()
      });
      return;
    }

    const { email, password, firstName, lastName, phone, referralCode } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      res.status(409).json({
        success: false,
        message: 'User already exists with this email',
        error: 'USER_EXISTS'
      });
      return;
    }

    // Check referral code if provided, otherwise set default admin referral for user role
    let referredBy = null;
    let usedReferralCode = referralCode;
    if (!usedReferralCode && (!req.body.role || req.body.role === 'user')) {
      // Find admin user with referralCode
      const adminUser = await User.findOne({ role: 'admin', referralCode: { $exists: true, $ne: null } });
      if (adminUser && adminUser.referralCode) {
        usedReferralCode = adminUser.referralCode;
      }
    }
    if (usedReferralCode) {
      const referrer = await User.findOne({ referralCode: usedReferralCode.toUpperCase() });
      if (!referrer) {
        res.status(400).json({
          success: false,
          message: 'Invalid referral code',
          error: 'INVALID_REFERRAL_CODE'
        });
        return;
      }
      referredBy = referrer._id;
    }

    // Create new user
    const user = new User({
      email: email.toLowerCase(),
      passwordHash: password, // Will be hashed by pre-save middleware
      firstName,
      lastName,
      phone,
      referredBy,
      status: UserStatus.ACTIVE
    });

    await user.save();

    // Create wallet for user
    await UserWallet.create({ userId: user._id });

    // Check settings for verification requirements
    const emailVerificationEnabled = await SettingsService.isEmailVerificationEnabled();
    const emailOTPEnabled = await SettingsService.isEmailOTPEnabled();
    const kycPriorityOverEmail = await SettingsService.isKYCPriorityOverEmail();
    const kycRequiredForActivation = await SettingsService.isKYCRequiredForActivation();

    let verificationToken = null;
    let otpSent = false;
    let otpRequired = false;
    let message = 'User registered successfully!';

    // Handle email verification with OTP if both email verification and OTP are enabled
    if (emailVerificationEnabled && emailOTPEnabled && emailService.isEmailEnabled()) {
      otpRequired = true;
      try {
        // Generate OTP
        const otp = emailService.generateOTP(6);

        // Save OTP to database
        await OTP.createOTP({
          email: user.email,
          otp,
          type: OTPType.EMAIL_VERIFICATION,
          expiryMinutes: 10,
          maxAttempts: 3
        });

        // Send OTP email
        otpSent = await emailService.sendOTPEmail(user.email, {
          name: `${user.firstName} ${user.lastName}`,
          otp,
          expiryMinutes: 10
        });

        if (otpSent) {
          // Set user status to pending verification
          user.status = UserStatus.PENDING_VERIFICATION;
          await user.save();
          console.log(`✅ OTP sent to ${user.email} for email verification`);
        } else {
          console.log(`⚠️ Failed to send OTP to ${user.email}, activating account without verification`);
        }
      } catch (error) {
        console.error('Error sending OTP:', error);
        otpSent = false;
      }
    } else if (emailVerificationEnabled && !emailOTPEnabled) {
      // Use token-based verification when OTP is disabled but email verification is enabled
      verificationToken = jwt.sign(
        { userId: user._id, email: user.email, type: 'email_verification' },
        process.env.JWT_SECRET || 'fallback-secret',
        { expiresIn: '24h' }
      );
      user.status = UserStatus.ACTIVE;
      await user.save();
      console.log(`Email verification token for ${user.email}: ${verificationToken}`);
      message = 'Registration successful! Please check your email for verification instructions.';
    } else if (emailVerificationEnabled && !emailService.isEmailEnabled()) {
      // Fallback to token-based verification if email service is not configured
      verificationToken = jwt.sign(
        { userId: user._id, email: user.email, type: 'email_verification' },
        process.env.JWT_SECRET || 'fallback-secret',
        { expiresIn: '24h' }
      );
      user.status = UserStatus.ACTIVE;
      await user.save();
      console.log(`Email verification token for ${user.email}: ${verificationToken}`);
      message = 'Registration successful! Please check your email for verification instructions.';
    } else {
      // No email verification required, activate user immediately
      user.status = UserStatus.ACTIVE;
      user.emailVerified = true; // Mark email as verified since no verification is required
      await user.save();
      message = 'Registration successful! Your account is now active.';
      console.log(`✅ Account activated immediately for ${user.email} (email verification disabled)`);
    }

    // Determine message based on settings and OTP status
    if (kycPriorityOverEmail && kycRequiredForActivation) {
      message = 'User registered successfully! Please complete your KYC verification to activate your account.';
    } else if (emailVerificationEnabled && !kycPriorityOverEmail) {
      if (otpSent) {
        message = 'User registered successfully! Please check your email and enter the OTP to verify your account.';
      } else if (verificationToken) {
        message = 'User registered successfully. Please verify your email to activate your account.';
      } else {
        message = 'User registered successfully! Your account is now active.';
      }
    } else if (kycRequiredForActivation) {
      message = 'User registered successfully! Please complete your KYC verification.';
    } else {
      message = 'User registered successfully! Your account is now active.';
    }

    // Generate tokens for login
    const { accessToken, refreshToken } = JWTUtils.generateTokens(
      (user._id as any).toString(),
      user.email,
      user.role
    );

    // Use universal auth response formatting
    const response = formatAuthResponse(req, res, user, accessToken, refreshToken, message);

    // Add verification info to response
    if (response.data) {
      response.data.emailVerificationRequired = emailVerificationEnabled && !kycPriorityOverEmail;
      response.data.otpSent = otpSent;
      response.data.otpRequired = otpRequired;
      response.data.userStatus = user.status;
      response.data.kycRequired = kycRequiredForActivation;
      response.data.kycPriority = kycPriorityOverEmail;
      response.data.verificationToken = process.env.NODE_ENV === 'development' ? verificationToken : undefined;
      response.data.userStatus = user.status;
    }

    res.status(201).json(response);
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Registration failed',
      error: 'REGISTRATION_ERROR'
    });
  }
};

/**
 * Send OTP for email verification
 */
export const sendEmailOTP = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email } = req.body;

    if (!email) {
      res.status(400).json({
        success: false,
        message: 'Email is required',
        error: 'MISSING_EMAIL'
      });
      return;
    }

    // Check if user exists
    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found',
        error: 'USER_NOT_FOUND'
      });
      return;
    }

    // Check if email service is enabled
    if (!emailService.isEmailEnabled()) {
      res.status(503).json({
        success: false,
        message: 'Email service is not configured',
        error: 'EMAIL_SERVICE_UNAVAILABLE'
      });
      return;
    }

    try {
      // Generate OTP
      const otp = emailService.generateOTP(6);

      // Save OTP to database
      await OTP.createOTP({
        email: user.email,
        otp,
        type: OTPType.EMAIL_VERIFICATION,
        expiryMinutes: 10,
        maxAttempts: 3
      });

      // Send OTP email
      const otpSent = await emailService.sendOTPEmail(user.email, {
        name: `${user.firstName} ${user.lastName}`,
        otp,
        expiryMinutes: 10
      });

      if (otpSent) {
        res.status(200).json({
          success: true,
          message: 'OTP sent successfully to your email',
          data: {
            email: user.email,
            expiryMinutes: 10
          }
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to send OTP',
          error: 'OTP_SEND_FAILED'
        });
      }
    } catch (error) {
      console.error('Error sending OTP:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to send OTP',
        error: 'OTP_SEND_ERROR'
      });
    }
  } catch (error) {
    console.error('Send OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Verify email OTP
 */
export const verifyEmailOTP = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, otp } = req.body;

    if (!email || !otp) {
      res.status(400).json({
        success: false,
        message: 'Email and OTP are required',
        error: 'MISSING_FIELDS'
      });
      return;
    }

    // Find and verify OTP
    const otpDoc = await OTP.findValidOTP(email.toLowerCase(), otp, OTPType.EMAIL_VERIFICATION);

    if (!otpDoc) {
      res.status(400).json({
        success: false,
        message: 'Invalid or expired OTP',
        error: 'INVALID_OTP'
      });
      return;
    }

    try {
      // Mark OTP as verified
      await otpDoc.markAsVerified();

      // Find and activate user
      const user = await User.findOne({ email: email.toLowerCase() });
      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found',
          error: 'USER_NOT_FOUND'
        });
        return;
      }

      // Activate user account
      user.status = UserStatus.ACTIVE;
      user.emailVerified = true;
      // user.emailVerifiedAt = new Date(); // Field doesn't exist in User model
      await user.save();

      res.status(200).json({
        success: true,
        message: 'Email verified successfully! Your account is now active.',
        data: {
          email: user.email,
          status: user.status,
          emailVerified: user.emailVerified
        }
      });
    } catch (error) {
      // Increment attempts on error
      await otpDoc.incrementAttempts();
      throw error;
    }
  } catch (error) {
    console.error('Verify OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify OTP',
      error: 'VERIFICATION_ERROR'
    });
  }
};

/**
 * Login user
 */
export const login = async (req: Request, res: Response): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        message: 'Validation failed',
        error: 'VALIDATION_ERROR',
        details: errors.array()
      });
      return;
    }

    const { email, password, rememberMe, referralCode } = req.body;

    console.log('🔐 Login attempt:', { email, password: '***' });

    // Find user with password
    const user = await User.findOne({ email: email.toLowerCase() }).select('+passwordHash');
    console.log('👤 User found:', user ? `Yes (${user.email}, ${user.role})` : 'No');

    if (!user) {
      console.log('❌ User not found in database');
      res.status(401).json({
        success: false,
        message: 'Invalid email or password',
        error: 'INVALID_CREDENTIALS'
      });
      return;
    }

    // Check password
    console.log('🔑 Checking password...');
    const isPasswordValid = await user.comparePassword(password);
    console.log('🔑 Password valid:', isPasswordValid);

    if (!isPasswordValid) {
      console.log('❌ Password comparison failed');
      res.status(401).json({
        success: false,
        message: 'Invalid email or password',
        error: 'INVALID_CREDENTIALS'
      });
      return;
    }

    // Check user status
    if (user.status === UserStatus.SUSPENDED) {
      res.status(401).json({
        success: false,
        message: 'Account is suspended',
        error: 'ACCOUNT_SUSPENDED'
      });
      return;
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate tokens
    const { accessToken, refreshToken } = JWTUtils.generateTokens(
      (user._id as any).toString(),
      user.email,
      user.role
    );

    // Handle remember me for web clients
    const clientType = detectClientType(req);
    if (clientType === 'web' && rememberMe) {
      CookieUtils.setRememberMeCookie(res, accessToken, refreshToken);
    }

    // Use universal auth response formatting
    const response = formatAuthResponse(req, res, user, accessToken, refreshToken, 'Login successful');

    // Add remember me info and referral code if provided
    if (response.data) {
      response.data.rememberMe = !!rememberMe;
      if (referralCode) {
        response.data.referralCode = referralCode;
        response.data.referralCodeProvided = true;
      }
    }

    res.status(200).json(response);
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: 'LOGIN_ERROR'
    });
  }
};

/**
 * Logout user
 */
export const logout = async (req: Request, res: Response): Promise<void> => {
  try {
    // Clear cookies regardless of how user was authenticated
    CookieUtils.clearAuthCookies(res);

    const response: ApiResponse = {
      success: true,
      message: 'Logout successful',
      data: {
        cookiesCleared: true
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Logout failed',
      error: 'LOGOUT_ERROR'
    });
  }
};

/**
 * Refresh access token
 */
export const refreshToken = async (req: Request, res: Response): Promise<void> => {
  try {
    const refreshToken = req.cookies.refreshToken ||
                        req.body.refreshToken ||
                        req.headers['x-refresh-token'];

    if (!refreshToken) {
      res.status(401).json({
        success: false,
        message: 'Refresh token is required',
        error: 'MISSING_REFRESH_TOKEN'
      });
      return;
    }

    try {
      const newAccessToken = JWTUtils.refreshAccessToken(refreshToken);
      CookieUtils.setAccessTokenCookie(res, newAccessToken);

      const response: ApiResponse = {
        success: true,
        message: 'Token refreshed successfully',
        data: {
          accessToken: newAccessToken,
          cookieUpdated: true
        }
      };

      res.status(200).json(response);
    } catch (error) {
      CookieUtils.clearAuthCookies(res);
      res.status(401).json({
        success: false,
        message: 'Invalid refresh token',
        error: 'INVALID_REFRESH_TOKEN'
      });
    }
  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(500).json({
      success: false,
      message: 'Token refresh failed',
      error: 'TOKEN_REFRESH_ERROR'
    });
  }
};

/**
 * Get current user profile
 */
export const getProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: 'Authentication required',
        error: 'NOT_AUTHENTICATED'
      });
      return;
    }

    const user = await User.findById(req.user.id)
      .populate('referredBy', 'firstName lastName email referralCode');

    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found',
        error: 'USER_NOT_FOUND'
      });
      return;
    }

    const response: ApiResponse = {
      success: true,
      message: 'Profile retrieved successfully',
      data: {
        user: user.toJSON()
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve profile',
      error: 'PROFILE_ERROR'
    });
  }
};

/**
 * Send email verification
 */
export const sendEmailVerification = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email } = req.body;

    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found',
        error: 'USER_NOT_FOUND'
      });
      return;
    }

    if (user.emailVerified) {
      res.status(400).json({
        success: false,
        message: 'Email is already verified',
        error: 'EMAIL_ALREADY_VERIFIED'
      });
      return;
    }

    // Generate verification token
    const verificationToken = jwt.sign(
      { userId: user._id, email: user.email, type: 'email_verification' },
      process.env.JWT_SECRET || 'fallback-secret',
      { expiresIn: '24h' }
    );

    console.log(`Email verification token for ${user.email}: ${verificationToken}`);

    res.status(200).json({
      success: true,
      message: 'Verification email sent successfully',
      data: {
        verificationToken: process.env.NODE_ENV === 'development' ? verificationToken : undefined
      }
    });
  } catch (error) {
    console.error('Send email verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send verification email',
      error: 'EMAIL_VERIFICATION_ERROR'
    });
  }
};

/**
 * Verify email with token
 */
export const verifyEmail = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token } = req.body;

    if (!token) {
      res.status(400).json({
        success: false,
        message: 'Verification token is required',
        error: 'TOKEN_REQUIRED'
      });
      return;
    }

    let decoded: any;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret');
    } catch (error) {
      res.status(400).json({
        success: false,
        message: 'Invalid or expired verification token',
        error: 'INVALID_TOKEN'
      });
      return;
    }

    if (decoded.type !== 'email_verification') {
      res.status(400).json({
        success: false,
        message: 'Invalid token type',
        error: 'INVALID_TOKEN_TYPE'
      });
      return;
    }

    const user = await User.findById(decoded.userId);
    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found',
        error: 'USER_NOT_FOUND'
      });
      return;
    }

    if (user.emailVerified) {
      res.status(400).json({
        success: false,
        message: 'Email is already verified',
        error: 'EMAIL_ALREADY_VERIFIED'
      });
      return;
    }

    user.emailVerified = true;
    if (user.status === UserStatus.PENDING_VERIFICATION) {
      user.status = UserStatus.ACTIVE;
    }
    await user.save();

    res.status(200).json({
      success: true,
      message: 'Email verified successfully',
      data: {
        emailVerified: true,
        accountActivated: user.status === UserStatus.ACTIVE
      }
    });
  } catch (error) {
    console.error('Verify email error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify email',
      error: 'EMAIL_VERIFICATION_ERROR'
    });
  }
};

/**
 * Send password reset OTP
 */
export const sendPasswordResetOTP = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email } = req.body;

    if (!email) {
      res.status(400).json({
        success: false,
        message: 'Email is required',
        error: 'MISSING_EMAIL'
      });
      return;
    }

    // Check if user exists
    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      // Don't reveal if user exists or not for security
      res.status(200).json({
        success: true,
        message: 'If an account with this email exists, you will receive a password reset code.',
        data: {
          email: email.toLowerCase()
        }
      });
      return;
    }

    // Check if email OTP is enabled
    const emailOTPEnabled = await SettingsService.isEmailOTPEnabled();
    if (!emailOTPEnabled) {
      res.status(400).json({
        success: false,
        message: 'Password reset via OTP is currently disabled. Please contact support for assistance.',
        error: 'PASSWORD_RESET_DISABLED'
      });
      return;
    }

    // Check if email service is enabled
    if (!emailService.isEmailEnabled()) {
      res.status(503).json({
        success: false,
        message: 'Email service is not configured. Please contact support for assistance.',
        error: 'EMAIL_SERVICE_UNAVAILABLE'
      });
      return;
    }

    try {
      // Generate OTP
      const otp = emailService.generateOTP(6);

      // Save OTP to database
      await OTP.createOTP({
        email: user.email,
        otp,
        type: OTPType.PASSWORD_RESET,
        expiryMinutes: 15, // 15 minutes for password reset
        maxAttempts: 3
      });

      // Send password reset email
      const otpSent = await emailService.sendPasswordResetEmail(user.email, {
        name: `${user.firstName} ${user.lastName}`,
        otp,
        expiryMinutes: 15
      });

      if (otpSent) {
        res.status(200).json({
          success: true,
          message: 'Password reset code sent successfully to your email',
          data: {
            email: user.email,
            expiryMinutes: 15
          }
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to send password reset code',
          error: 'OTP_SEND_FAILED'
        });
      }
    } catch (error) {
      console.error('Error sending password reset OTP:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to send password reset code',
        error: 'OTP_SEND_ERROR'
      });
    }
  } catch (error) {
    console.error('Send password reset OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Verify password reset OTP
 */
export const verifyPasswordResetOTP = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, otp } = req.body;

    if (!email || !otp) {
      res.status(400).json({
        success: false,
        message: 'Email and OTP are required',
        error: 'MISSING_FIELDS'
      });
      return;
    }

    // Find and verify OTP
    const otpDoc = await OTP.findValidOTP(email.toLowerCase(), otp, OTPType.PASSWORD_RESET);

    if (!otpDoc) {
      res.status(400).json({
        success: false,
        message: 'Invalid or expired password reset code',
        error: 'INVALID_OTP'
      });
      return;
    }

    try {
      // Mark OTP as verified
      await otpDoc.markAsVerified();

      // Generate a temporary token for password reset (valid for 10 minutes)
      const resetToken = jwt.sign(
        {
          email: email.toLowerCase(),
          type: 'password_reset_verified',
          otpId: otpDoc._id
        },
        process.env.JWT_SECRET || 'fallback-secret',
        { expiresIn: '10m' }
      );

      res.status(200).json({
        success: true,
        message: 'Password reset code verified successfully',
        data: {
          resetToken,
          expiresIn: '10 minutes'
        }
      });
    } catch (error) {
      // Increment attempts on error
      await otpDoc.incrementAttempts();
      throw error;
    }
  } catch (error) {
    console.error('Verify password reset OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify password reset code',
      error: 'VERIFICATION_ERROR'
    });
  }
};

/**
 * Reset password with verified token
 */
export const resetPassword = async (req: Request, res: Response): Promise<void> => {
  try {
    const { resetToken, newPassword, confirmPassword } = req.body;

    if (!resetToken || !newPassword || !confirmPassword) {
      res.status(400).json({
        success: false,
        message: 'Reset token, new password, and confirm password are required',
        error: 'MISSING_FIELDS'
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      res.status(400).json({
        success: false,
        message: 'New password and confirm password do not match',
        error: 'PASSWORD_MISMATCH'
      });
      return;
    }

    // Validate password strength
    if (newPassword.length < 8) {
      res.status(400).json({
        success: false,
        message: 'Password must be at least 8 characters long',
        error: 'WEAK_PASSWORD'
      });
      return;
    }

    // Verify reset token
    let decoded: any;
    try {
      decoded = jwt.verify(resetToken, process.env.JWT_SECRET || 'fallback-secret');
    } catch (error) {
      res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token',
        error: 'INVALID_TOKEN'
      });
      return;
    }

    // Check token type
    if (decoded.type !== 'password_reset_verified') {
      res.status(400).json({
        success: false,
        message: 'Invalid token type',
        error: 'INVALID_TOKEN_TYPE'
      });
      return;
    }

    // Find user
    const user = await User.findOne({ email: decoded.email });
    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found',
        error: 'USER_NOT_FOUND'
      });
      return;
    }

    // Check if the OTP used for verification still exists and is verified
    const otpDoc = await OTP.findById(decoded.otpId);
    if (!otpDoc || otpDoc.status !== OTPStatus.VERIFIED) {
      res.status(400).json({
        success: false,
        message: 'Reset token is no longer valid',
        error: 'TOKEN_INVALIDATED'
      });
      return;
    }

    // Update user password
    user.passwordHash = newPassword; // This will be hashed by the pre-save middleware
    await user.save();

    // Invalidate the OTP to prevent reuse
    otpDoc.status = OTPStatus.EXPIRED;
    await otpDoc.save();

    // Send account activation email if user was previously inactive
    if (user.status === UserStatus.PENDING_VERIFICATION) {
      user.status = UserStatus.ACTIVE;
      await user.save();

      try {
        await emailService.sendAccountActivationEmail(user.email, {
          name: `${user.firstName} ${user.lastName}`,
          activationDate: new Date().toLocaleDateString()
        });
      } catch (emailError) {
        console.error('Failed to send activation email:', emailError);
        // Don't fail the password reset if email fails
      }
    }

    res.status(200).json({
      success: true,
      message: 'Password reset successfully! You can now login with your new password.',
      data: {
        email: user.email,
        accountActivated: user.status === UserStatus.ACTIVE
      }
    });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset password',
      error: 'RESET_ERROR'
    });
  }
};



