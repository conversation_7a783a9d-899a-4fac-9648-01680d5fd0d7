{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Format currency\nexport function formatCurrency(amount: number, currency = \"₹\"): string {\n  return `${currency}${amount.toLocaleString('en-IN', { \n    minimumFractionDigits: 2, \n    maximumFractionDigits: 2 \n  })}`\n}\n\n// Format date\nexport function formatDate(date: string | Date, format = \"dd/MM/yyyy\"): string {\n  const d = new Date(date)\n  \n  if (format === \"dd/MM/yyyy\") {\n    return d.toLocaleDateString('en-GB')\n  }\n  \n  if (format === \"relative\") {\n    const now = new Date()\n    const diffInMs = now.getTime() - d.getTime()\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))\n    \n    if (diffInDays === 0) return \"Today\"\n    if (diffInDays === 1) return \"Yesterday\"\n    if (diffInDays < 7) return `${diffInDays} days ago`\n    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`\n    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`\n    return `${Math.floor(diffInDays / 365)} years ago`\n  }\n  \n  return d.toLocaleDateString()\n}\n\n// Truncate text\nexport function truncateText(text: string, length = 50): string {\n  if (text.length <= length) return text\n  return text.substring(0, length) + \"...\"\n}\n\n// Generate initials\nexport function getInitials(firstName?: string, lastName?: string, email?: string): string {\n  if (firstName && lastName) {\n    return `${firstName[0]}${lastName[0]}`.toUpperCase()\n  }\n  \n  if (firstName) {\n    return firstName.substring(0, 2).toUpperCase()\n  }\n  \n  if (email) {\n    return email.substring(0, 2).toUpperCase()\n  }\n  \n  return \"U\"\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n// Sleep function\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\n// Generate random ID\nexport function generateId(prefix = \"\"): string {\n  const timestamp = Date.now().toString(36)\n  const random = Math.random().toString(36).substring(2, 8)\n  return `${prefix}${timestamp}${random}`.toUpperCase()\n}\n\n// Validate email\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// Validate phone\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\n// Format phone number\nexport function formatPhone(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '')\n  \n  if (cleaned.length === 10) {\n    return `+91 ${cleaned.substring(0, 5)} ${cleaned.substring(5)}`\n  }\n  \n  if (cleaned.length === 12 && cleaned.startsWith('91')) {\n    return `+${cleaned.substring(0, 2)} ${cleaned.substring(2, 7)} ${cleaned.substring(7)}`\n  }\n  \n  return phone\n}\n\n// Calculate percentage\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0\n  return Math.round((value / total) * 100)\n}\n\n// Get status color\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    active: \"text-green-600 bg-green-100\",\n    inactive: \"text-gray-600 bg-gray-100\",\n    pending: \"text-yellow-600 bg-yellow-100\",\n    suspended: \"text-red-600 bg-red-100\",\n    completed: \"text-green-600 bg-green-100\",\n    failed: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    new: \"text-blue-600 bg-blue-100\",\n    contacted: \"text-purple-600 bg-purple-100\",\n    qualified: \"text-indigo-600 bg-indigo-100\",\n    converted: \"text-green-600 bg-green-100\",\n    lost: \"text-red-600 bg-red-100\",\n  }\n  \n  return statusColors[status.toLowerCase()] || \"text-gray-600 bg-gray-100\"\n}\n\n// Copy to clipboard\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text)\n    return true\n  } catch (error) {\n    console.error('Failed to copy to clipboard:', error)\n    return false\n  }\n}\n\n// Download file\nexport function downloadFile(data: any, filename: string, type = 'application/json'): void {\n  const blob = new Blob([typeof data === 'string' ? data : JSON.stringify(data, null, 2)], { type })\n  const url = URL.createObjectURL(blob)\n  const link = document.createElement('a')\n  link.href = url\n  link.download = filename\n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n  URL.revokeObjectURL(url)\n}\n\n// Format file size\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// Get file extension\nexport function getFileExtension(filename: string): string {\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)\n}\n\n// Check if mobile device\nexport function isMobile(): boolean {\n  if (typeof window === 'undefined') return false\n  return window.innerWidth < 768\n}\n\n// Scroll to element\nexport function scrollToElement(elementId: string, offset = 0): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const top = element.offsetTop - offset\n    window.scrollTo({ top, behavior: 'smooth' })\n  }\n}\n\n// Local storage helpers\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    if (typeof window === 'undefined') return defaultValue || null\n    \n    try {\n      const item = localStorage.getItem(key)\n      return item ? JSON.parse(item) : defaultValue || null\n    } catch (error) {\n      console.error(`Error getting item from localStorage:`, error)\n      return defaultValue || null\n    }\n  },\n  \n  set: <T>(key: string, value: T): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.setItem(key, JSON.stringify(value))\n    } catch (error) {\n      console.error(`Error setting item in localStorage:`, error)\n    }\n  },\n  \n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.removeItem(key)\n    } catch (error) {\n      console.error(`Error removing item from localStorage:`, error)\n    }\n  },\n  \n  clear: (): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.clear()\n    } catch (error) {\n      console.error(`Error clearing localStorage:`, error)\n    }\n  }\n}\n\n// Format time\nexport function formatTime(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc,EAAE,WAAW,GAAG;IAC3D,OAAO,GAAG,WAAW,OAAO,cAAc,CAAC,SAAS;QAClD,uBAAuB;QACvB,uBAAuB;IACzB,IAAI;AACN;AAGO,SAAS,WAAW,IAAmB,EAAE,SAAS,YAAY;IACnE,MAAM,IAAI,IAAI,KAAK;IAEnB,IAAI,WAAW,cAAc;QAC3B,OAAO,EAAE,kBAAkB,CAAC;IAC9B;IAEA,IAAI,WAAW,YAAY;QACzB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,EAAE,OAAO;QAC1C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE7D,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,SAAS,CAAC;QACnD,IAAI,aAAa,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC;QACrE,IAAI,aAAa,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI,WAAW,CAAC;QACxE,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,KAAK,UAAU,CAAC;IACpD;IAEA,OAAO,EAAE,kBAAkB;AAC7B;AAGO,SAAS,aAAa,IAAY,EAAE,SAAS,EAAE;IACpD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,SAAS,CAAC,GAAG,UAAU;AACrC;AAGO,SAAS,YAAY,SAAkB,EAAE,QAAiB,EAAE,KAAc;IAC/E,IAAI,aAAa,UAAU;QACzB,OAAO,GAAG,SAAS,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW;IACpD;IAEA,IAAI,WAAW;QACb,OAAO,UAAU,SAAS,CAAC,GAAG,GAAG,WAAW;IAC9C;IAEA,IAAI,OAAO;QACT,OAAO,MAAM,SAAS,CAAC,GAAG,GAAG,WAAW;IAC1C;IAEA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,SAAS,WAAW,SAAS,EAAE;IACpC,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,CAAC;IACtC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACvD,OAAO,GAAG,SAAS,YAAY,QAAQ,CAAC,WAAW;AACrD;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAGO,SAAS,YAAY,KAAa;IACvC,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IAErC,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,CAAC,IAAI,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACjE;IAEA,IAAI,QAAQ,MAAM,KAAK,MAAM,QAAQ,UAAU,CAAC,OAAO;QACrD,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACzF;IAEA,OAAO;AACT;AAGO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAGO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,QAAQ;QACR,UAAU;QACV,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;QACR,WAAW;QACX,KAAK;QACL,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;IACR;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAGO,SAAS,aAAa,IAAS,EAAE,QAAgB,EAAE,OAAO,kBAAkB;IACjF,MAAM,OAAO,IAAI,KAAK;QAAC,OAAO,SAAS,WAAW,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM;KAAG,EAAE;QAAE;IAAK;IAChG,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAGO,SAAS;IACd,wCAAmC,OAAO;;AAE5C;AAGO,SAAS,gBAAgB,SAAiB,EAAE,SAAS,CAAC;IAC3D,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,MAAM,QAAQ,SAAS,GAAG;QAChC,OAAO,QAAQ,CAAC;YAAE;YAAK,UAAU;QAAS;IAC5C;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,wCAAmC,OAAO,gBAAgB;;IAS5D;IAEA,KAAK,CAAI,KAAa;QACpB,wCAAmC;;IAOrC;IAEA,QAAQ,CAAC;QACP,wCAAmC;;IAOrC;IAEA,OAAO;QACL,wCAAmC;;IAOrC;AACF;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"card-base text-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6BACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"btn-primary\",\n        destructive: \"bg-red-500 text-white hover:bg-red-600\",\n        outline: \"btn-outline\",\n        secondary: \"btn-secondary\",\n        ghost: \"hover:bg-primary-100 text-primary\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,mQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6WAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,+QAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6WAAC,+QAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,oSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,+QAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,wSAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,+QAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6WAAC,+QAAA,CAAA,SAAsB;kBACrB,cAAA,6WAAC,+QAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6WAAC;;;;;8BACD,6WAAC,+QAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6WAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,+QAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6WAAC;gBAAK,WAAU;0BACd,cAAA,6WAAC,+QAAA,CAAA,gBAA6B;8BAC5B,cAAA,6WAAC,wRAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6WAAC,+QAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,+QAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,+QAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"input-field flex h-10 w-full rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6WAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2RACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,8QAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,8QAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 675, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/logo.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'full' | 'icon' | 'text'\n  className?: string\n}\n\nconst Logo: React.FC<LogoProps> = ({ \n  size = 'md', \n  variant = 'full', \n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'h-6 w-6',\n    md: 'h-8 w-8', \n    lg: 'h-10 w-10',\n    xl: 'h-12 w-12'\n  }\n\n  const textSizeClasses = {\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-2xl', \n    xl: 'text-3xl'\n  }\n\n  const LogoIcon = () => (\n    <div className={`${sizeClasses[size]} bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center shadow-lg ${className}`}>\n      <div className=\"relative\">\n        {/* S */}\n        <div className=\"absolute -left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5 ml-1\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n        </div>\n        \n        {/* G */}\n        <div className=\"absolute left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-4 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"absolute right-0 top-2 w-1 h-2 bg-white rounded-full\"></div>\n          <div className=\"absolute right-0 top-3 w-1.5 h-1 bg-white rounded-full\"></div>\n        </div>\n        \n        {/* M */}\n        <div className=\"absolute left-4 top-0\">\n          <div className=\"w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-0.5 top-1 w-1 h-1 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1 top-0 w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1.5 top-0 w-1 h-5 bg-white rounded-full\"></div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const LogoText = () => (\n    <span className={`font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent ${textSizeClasses[size]} ${className}`}>\n      SGM\n    </span>\n  )\n\n  if (variant === 'icon') {\n    return <LogoIcon />\n  }\n\n  if (variant === 'text') {\n    return <LogoText />\n  }\n\n  return (\n    <div className={`flex items-center gap-3 ${className}`}>\n      <LogoIcon />\n      <LogoText />\n    </div>\n  )\n}\n\nexport default Logo\n"], "names": [], "mappings": ";;;;;AAQA,MAAM,OAA4B,CAAC,EACjC,OAAO,IAAI,EACX,UAAU,MAAM,EAChB,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,WAAW,kBACf,6WAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,mGAAmG,EAAE,WAAW;sBACnJ,cAAA,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMvB,MAAM,WAAW,kBACf,6WAAC;YAAK,WAAW,CAAC,mFAAmF,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;sBAAE;;;;;;IAK/I,IAAI,YAAY,QAAQ;QACtB,qBAAO,6WAAC;;;;;IACV;IAEA,IAAI,YAAY,QAAQ;QACtB,qBAAO,6WAAC;;;;;IACV;IAEA,qBACE,6WAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;0BACpD,6WAAC;;;;;0BACD,6WAAC;;;;;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 883, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAppSelector } from '@/store'\nimport { selectUser } from '@/store/slices/authSlice'\nimport { UserRole } from '@/types'\nimport { cn } from '@/lib/utils'\nimport Logo from '@/components/ui/logo'\nimport {\n  LayoutDashboard,\n  Users,\n  Building,\n  TrendingUp,\n  Target,\n  DollarSign,\n  Headphones,\n  Settings,\n  Plus,\n  UserPlus,\n  UserCheck,\n  BarChart3,\n  Shield,\n  Bell,\n  PieChart,\n  CheckSquare,\n  CalendarDays,\n  UserCog,\n  Award,\n  Minus,\n  Receipt,\n  CreditCard\n} from 'lucide-react'\n\ninterface SidebarProps {\n  collapsed?: boolean\n  onToggle?: () => void\n}\n\nexport default function Sidebar({ collapsed = false, onToggle }: SidebarProps) {\n  const pathname = usePathname()\n  const user = useAppSelector((state) => selectUser(state as any))\n\n  console.log('Sidebar rendering...', { collapsed, pathname, user })\n\n  const menuSections = [\n    {\n      title: \"Dashboard\",\n      items: [\n        {\n          id: \"main-dashboard\",\n          label: \"Overview\",\n          icon: LayoutDashboard,\n          href: \"/dashboard\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES, UserRole.USER]\n        }\n        \n      ]\n    },\n    {\n      title: \"User Management\",\n      items: [\n        {\n          id: \"users-overview\",\n          label: \"All Users\",\n          icon: Users,\n          href: \"/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-management-comprehensive\",\n          label: \"User Management\",\n          icon: Users,\n          href: \"/user-management\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-user\",\n          label: \"Add User\",\n          icon: UserPlus,\n          href: \"/users/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"role-management\",\n          label: \"Role Management\",\n          icon: UserCheck,\n          href: \"/users/roles\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Property Management\",\n      items: [\n        {\n          id: \"properties-overview\",\n          label: \"All Properties\",\n          icon: Building,\n          href: \"/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-property\",\n          label: \"Add Property\",\n          icon: Plus,\n          href: \"/properties/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-owners\",\n          label: \"Property Owners\",\n          icon: UserCheck,\n          href: \"/property-owners\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Stock Investments\",\n      items: [\n        {\n          id: \"stocks-overview\",\n          label: \"All Stocks\",\n          icon: TrendingUp,\n          href: \"/stocks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"create-stock\",\n          label: \"Create Stock\",\n          icon: Plus,\n          href: \"/stocks/create\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Lead Management\",\n      items: [\n        {\n          id: \"leads-overview\",\n          label: \"Lead Management\",\n          icon: Target,\n          href: \"/leads\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES]\n        },\n        {\n          id: \"sales-analytics\",\n          label: \"Sales Analytics\",\n          icon: BarChart3,\n          href: \"/sales-analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Sales Management\",\n      items: [\n        {\n          id: \"sales-team\",\n          label: \"Sales Team\",\n          icon: UserCog,\n          href: \"/sales-team\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-tasks\",\n          label: \"Sales Tasks\",\n          icon: CheckSquare,\n          href: \"/sales-tasks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-calendar\",\n          label: \"Sales Calendar\",\n          icon: CalendarDays,\n          href: \"/sales-calendar\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-targets\",\n          label: \"Sales Targets\",\n          icon: Award,\n          href: \"/sales-targets\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"commissions\",\n          label: \"Commissions\",\n          icon: DollarSign,\n          href: \"/commissions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Financial Management\",\n      items: [\n        {\n          id: \"finance-overview\",\n          label: \"Financial Management\",\n          icon: DollarSign,\n          href: \"/finance\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-funds\",\n          label: \"Add Funds\",\n          icon: Plus,\n          href: \"/add-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"deduct-funds\",\n          label: \"Deduct Funds\",\n          icon: Minus,\n          href: \"/deduct-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"admin-transactions\",\n          label: \"All Transactions\",\n          icon: Receipt,\n          href: \"/admin-transactions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"withdrawal-requests\",\n          label: \"Withdrawal Requests\",\n          icon: CreditCard,\n          href: \"/withdrawal-requests\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Support Management\",\n      items: [\n        {\n          id: \"support-dashboard\",\n          label: \"Support Management\",\n          icon: Headphones,\n          href: \"/support\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"System & Settings\",\n      items: [\n        {\n          id: \"system-settings\",\n          label: \"Settings Management\",\n          icon: Settings,\n          href: \"/settings\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Reports & Analytics\",\n      items: [\n        {\n          id: \"analytics-dashboard\",\n          label: \"Analytics Dashboard\",\n          icon: BarChart3,\n          href: \"/analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-reports\",\n          label: \"User Reports\",\n          icon: Users,\n          href: \"/reports/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-reports\",\n          label: \"Property Reports\",\n          icon: Building,\n          href: \"/reports/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"financial-reports\",\n          label: \"Financial Reports\",\n          icon: DollarSign,\n          href: \"/reports/financial\",\n          roles: [UserRole.ADMIN]\n        },\n       \n      ]\n    }\n  ]\n\n  // Filter menu sections based on user role\n  const userRole = user?.role as UserRole || UserRole.USER\n  const filteredSections = menuSections.map(section => ({\n    ...section,\n    items: section.items.filter(item => item.roles.includes(userRole))\n  })).filter(section => section.items.length > 0)\n\n  // Debug logging\n  console.log('User:', user)\n  console.log('User Role:', user?.role)\n  console.log('UserRole enum:', userRole)\n  console.log('Menu Sections:', menuSections.length)\n  console.log('Filtered Sections:', filteredSections.length)\n\n  // Use filtered sections based on user role\n  const sectionsToShow = filteredSections\n\n  const isActive = (href: string) => {\n    if (!pathname) return false\n    if (href === '/dashboard') {\n      return pathname === '/dashboard'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <div className={cn(\n      \"h-screen bg-white border-r border-sky-200 flex flex-col transition-all duration-300 ease-in-out shadow-lg\",\n      collapsed ? \"w-20\" : \"w-72\"\n    )}>\n      {/* Logo */}\n      <div className=\"flex items-center space-x-3 p-4 border-b border-sky-200 flex-shrink-0 bg-gradient-to-r from-sky-50 to-sky-100\">\n        <Logo size={collapsed ? \"lg\" : \"xl\"} variant={collapsed ? \"icon\" : \"full\"} />\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-sky-300 scrollbar-track-sky-100\">\n        <div className=\"space-y-2\">\n          {sectionsToShow.length === 0 ? (\n            <div className=\"text-center text-red-500 p-4\">\n              <p>No menu items found!</p>\n              <p>User: {user?.firstName || 'Not logged in'}</p>\n              <p>Role: {user?.role || 'No role'}</p>\n            </div>\n          ) : (\n            sectionsToShow.map((section) => (\n              <div key={section.title} className=\"space-y-1 mb-6\">\n                {!collapsed && (\n                  <h3 className=\"px-3 py-2 text-xs font-semibold text-sky-600 uppercase tracking-wider\">\n                    {section.title}\n                  </h3>\n                )}\n\n                {section.items.map((item) => (\n                  <Link\n                    key={item.id}\n                    href={item.href || '#'}\n                    className={cn(\n                      \"flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-200\",\n                      isActive(item.href || '')\n                        ? \"bg-sky-600 text-white shadow-md\"\n                        : \"text-gray-700 hover:text-sky-600 hover:bg-sky-50\"\n                    )}\n                  >\n                    <item.icon className=\"h-5 w-5 flex-shrink-0\" />\n                    {!collapsed && (\n                      <span className=\"flex-1 truncate\">{item.label}</span>\n                    )}\n                  </Link>\n                ))}\n              </div>\n            ))\n          )}\n        </div>\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-sky-200 bg-gradient-to-r from-sky-50 to-sky-100\">\n        {!collapsed && (\n          <div className=\"text-xs text-sky-600 text-center font-medium\">\n            © 2025 SGM. All rights reserved.\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;AAwCe,SAAS,QAAQ,EAAE,YAAY,KAAK,EAAE,QAAQ,EAAgB;IAC3E,MAAM,WAAW,CAAA,GAAA,iQAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,OAAO,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,QAAU,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;IAElD,QAAQ,GAAG,CAAC,wBAAwB;QAAE;QAAW;QAAU;IAAK;IAEhE,MAAM,eAAe;QACnB;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,gTAAA,CAAA,kBAAe;oBACrB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,IAAI;qBAAC;gBAC3E;aAED;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,oSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,oSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,0RAAA,CAAA,SAAM;oBACZ,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBAC5D;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,gSAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,+SAAA,CAAA,cAAW;oBACjB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,0SAAA,CAAA,eAAY;oBAClB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,4RAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aAED;QACH;KACD;IAED,0CAA0C;IAC1C,MAAM,WAAW,MAAM,QAAoB,qHAAA,CAAA,WAAQ,CAAC,IAAI;IACxD,MAAM,mBAAmB,aAAa,GAAG,CAAC,CAAA,UAAW,CAAC;YACpD,GAAG,OAAO;YACV,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC;QAC1D,CAAC,GAAG,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,CAAC,MAAM,GAAG;IAE7C,gBAAgB;IAChB,QAAQ,GAAG,CAAC,SAAS;IACrB,QAAQ,GAAG,CAAC,cAAc,MAAM;IAChC,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,QAAQ,GAAG,CAAC,kBAAkB,aAAa,MAAM;IACjD,QAAQ,GAAG,CAAC,sBAAsB,iBAAiB,MAAM;IAEzD,2CAA2C;IAC3C,MAAM,iBAAiB;IAEvB,MAAM,WAAW,CAAC;QAChB,IAAI,CAAC,UAAU,OAAO;QACtB,IAAI,SAAS,cAAc;YACzB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6WAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,6GACA,YAAY,SAAS;;0BAGrB,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC,gIAAA,CAAA,UAAI;oBAAC,MAAM,YAAY,OAAO;oBAAM,SAAS,YAAY,SAAS;;;;;;;;;;;0BAIrE,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;8BACZ,eAAe,MAAM,KAAK,kBACzB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;0CAAE;;;;;;0CACH,6WAAC;;oCAAE;oCAAO,MAAM,aAAa;;;;;;;0CAC7B,6WAAC;;oCAAE;oCAAO,MAAM,QAAQ;;;;;;;;;;;;+BAG1B,eAAe,GAAG,CAAC,CAAC,wBAClB,6WAAC;4BAAwB,WAAU;;gCAChC,CAAC,2BACA,6WAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;gCAIjB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6WAAC,2RAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI,IAAI;wCACnB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA,SAAS,KAAK,IAAI,IAAI,MAClB,oCACA;;0DAGN,6WAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,CAAC,2BACA,6WAAC;gDAAK,WAAU;0DAAmB,KAAK,KAAK;;;;;;;uCAX1C,KAAK,EAAE;;;;;;2BATR,QAAQ,KAAK;;;;;;;;;;;;;;;0BA+B/B,6WAAC;gBAAI,WAAU;0BACZ,CAAC,2BACA,6WAAC;oBAAI,WAAU;8BAA+C;;;;;;;;;;;;;;;;;AAOxE", "debugId": null}}, {"offset": {"line": 1418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAppSelector, useAppDispatch } from '@/store'\nimport { selectUser, logoutAsync } from '@/store/slices/authSlice'\nimport { Button } from '@/components/ui/button'\nimport Sidebar from './Sidebar'\nimport { Menu, Bell, Search, LogOut, User, Settings, HelpCircle, ChevronDown } from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)\n  const [userMenuOpen, setUserMenuOpen] = useState(false)\n  const router = useRouter()\n  const dispatch = useAppDispatch()\n  const user = useAppSelector(selectUser)\n\n  const handleLogout = async () => {\n    await dispatch(logoutAsync())\n    router.push('/login')\n  }\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed)\n  }\n\n  return (\n    <div className=\"h-screen bg-white overflow-hidden\">\n      <div className=\"flex h-full\">\n        {/* Sidebar */}\n        <Sidebar collapsed={sidebarCollapsed} onToggle={toggleSidebar} />\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col h-full overflow-hidden\">\n          {/* Header */}\n          <header className=\"bg-gradient-to-r from-sky-500 to-sky-600 shadow-lg border-b border-sky-700 flex-shrink-0 z-40\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              <div className=\"flex justify-between items-center py-4\">\n                {/* Left side */}\n                <div className=\"flex items-center space-x-4\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={toggleSidebar}\n                    className=\"lg:hidden hover:bg-white/20 text-white\"\n                  >\n                    <Menu className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Search Bar */}\n                  <div className=\"hidden md:flex items-center space-x-2 bg-white/20 rounded-lg px-4 py-2.5 min-w-[350px] border border-white/30\">\n                    <Search className=\"h-4 w-4 text-white/70\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search users, properties, transactions...\"\n                      className=\"bg-transparent border-none outline-none flex-1 text-sm placeholder:text-white/70 text-white\"\n                    />\n                    <kbd className=\"hidden lg:inline-flex items-center px-2 py-1 text-xs font-medium text-sky-600 bg-white rounded border border-white/30\">\n                      ⌘K\n                    </kbd>\n                  </div>\n                </div>\n\n                {/* Right side */}\n                <div className=\"flex items-center space-x-3\">\n                  {/* Quick Actions */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"hover:bg-white/20 text-white\">\n                    <HelpCircle className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Notifications */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"relative hover:bg-white/20 text-white\">\n                    <Bell className=\"h-5 w-5\" />\n                    <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-yellow-500 text-black text-xs rounded-full flex items-center justify-center font-semibold\">\n                      5\n                    </span>\n                  </Button>\n\n                  {/* User Menu */}\n                  <div className=\"relative\">\n                    <Button\n                      variant=\"ghost\"\n                      onClick={() => setUserMenuOpen(!userMenuOpen)}\n                      className=\"flex items-center space-x-3 px-3 py-2 hover:bg-white/20 rounded-lg text-white\"\n                    >\n                      <div className=\"hidden sm:block text-right\">\n                        <p className=\"text-sm font-medium text-white\">\n                          {user?.firstName} {user?.lastName}\n                        </p>\n                        <div className=\"flex items-center justify-end space-x-1\">\n                          <div className=\"bg-yellow-500 px-2 py-0.5 rounded-full\">\n                            <span className=\"text-xs font-medium text-black capitalize\">\n                              {user?.role}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"h-10 w-10 bg-white/20 rounded-full flex items-center justify-center shadow-sm border border-white/30\">\n                        <User className=\"h-5 w-5 text-white\" />\n                      </div>\n\n                      <ChevronDown className=\"h-4 w-4 text-white/70\" />\n                    </Button>\n\n                    {/* User Dropdown Menu */}\n                    {userMenuOpen && (\n                      <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-sky-200 py-2 z-50\">\n                        <div className=\"px-4 py-2 border-b border-sky-100\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {user?.firstName} {user?.lastName}\n                          </p>\n                          <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                        </div>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <User className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Profile\n                        </Button>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <Settings className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Settings\n                        </Button>\n\n                        <div className=\"border-t border-sky-100 mt-2 pt-2\">\n                          <Button\n                            variant=\"ghost\"\n                            className=\"w-full justify-start px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                            onClick={handleLogout}\n                          >\n                            <LogOut className=\"h-4 w-4 mr-3\" />\n                            Logout\n                          </Button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </header>\n\n          {/* Main Content Area */}\n          <main className=\"flex-1 overflow-y-auto bg-gray-50\">\n            {children}\n          </main>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAce,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,OAAO,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,mIAAA,CAAA,aAAU;IAEtC,MAAM,eAAe;QACnB,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB;QACpB,oBAAoB,CAAC;IACvB;IAEA,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BAEb,6WAAC,uIAAA,CAAA,UAAO;oBAAC,WAAW;oBAAkB,UAAU;;;;;;8BAGhD,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC;4BAAO,WAAU;sCAChB,cAAA,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDAEb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DAEV,cAAA,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAIlB,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,0RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6WAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;sEAEZ,6WAAC;4DAAI,WAAU;sEAAwH;;;;;;;;;;;;;;;;;;sDAO3I,6WAAC;4CAAI,WAAU;;8DAEb,6WAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;8DAC5C,cAAA,6WAAC,kTAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAIxB,6WAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;;sEAC5C,6WAAC,sRAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6WAAC;4DAAK,WAAU;sEAAgI;;;;;;;;;;;;8DAMlJ,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS,IAAM,gBAAgB,CAAC;4DAChC,WAAU;;8EAEV,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,6WAAC;4EAAI,WAAU;sFACb,cAAA,6WAAC;gFAAI,WAAU;0FACb,cAAA,6WAAC;oFAAK,WAAU;8FACb,MAAM;;;;;;;;;;;;;;;;;;;;;;8EAMf,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAGlB,6WAAC,wSAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;;wDAIxB,8BACC,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,6WAAC;4EAAE,WAAU;sFAAyB,MAAM;;;;;;;;;;;;8EAG9C,6WAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,6WAAC,sRAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIhD,6WAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,6WAAC,8RAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIpD,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,WAAU;wEACV,SAAS;;0FAET,6WAAC,8RAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAarD,6WAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1825, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6WAAC,8QAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,8QAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,8QAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1864, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/lib/s3Upload.ts"], "sourcesContent": ["// Updated S3 Upload Service - Using RTK Query API\nimport { store } from '@/store'\nimport { usersApi } from '@/store/api/usersApi'\nimport { propertiesApi } from '@/store/api/propertiesApi'\n\nexport interface UploadResult {\n  success: boolean\n  url?: string\n  key?: string\n  error?: string\n  fileKey?: string\n  publicUrl?: string\n  presignedUrl?: string\n}\n\nexport interface PresignedUrlResponse {\n  success: boolean\n  data?: {\n    presignedUrl: string\n    publicUrl: string\n    fileKey: string\n    uploadUrl: string\n    expiresIn: number\n    bucket: string\n    region: string\n    uploadInstructions?: {\n      method: string\n      headers: Record<string, string>\n      note: string\n    }\n  }\n  message?: string\n  error?: string\n}\n\n/**\n * Sanitize file name to match backend validation requirements\n */\nconst sanitizeFileName = (fileName: string): string => {\n  return fileName\n    .replace(/[<>:\"/\\\\|?*]/g, '_') // Replace dangerous characters\n    .replace(/\\s+/g, '_') // Replace spaces with underscores\n    .replace(/_{2,}/g, '_') // Replace multiple underscores with single\n    .toLowerCase() // Convert to lowercase\n}\n\n/**\n * Normalize file type to match backend expectations\n */\nconst normalizeFileType = (fileType: string): string => {\n  // Handle common MIME type variations\n  const typeMap: Record<string, string> = {\n    'image/jpg': 'image/jpeg',\n    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n    'application/msword': 'application/msword'\n  }\n\n  return typeMap[fileType] || fileType\n}\n\n/**\n * Get presigned URL from server API using RTK Query\n */\nexport const getPresignedUploadUrl = async (\n  fileName: string,\n  fileType: string,\n  uploadType: 'property-image' | 'property-document' | 'user-avatar' | 'user-document' = 'property-image',\n  fileSize?: number,\n  entityId?: string\n): Promise<PresignedUrlResponse> => {\n  try {\n    // Sanitize file name to match backend validation\n    const sanitizedFileName = sanitizeFileName(fileName)\n    const normalizedFileType = normalizeFileType(fileType)\n\n    // Log upload request for debugging\n    console.log('S3 Upload Request:', {\n      fileName: sanitizedFileName,\n      fileType: normalizedFileType,\n      uploadType,\n      entityId: entityId || 'none'\n    })\n\n    let result: any\n\n    if (uploadType.startsWith('user-')) {\n      // Use user API for user-related uploads\n      const requestData: any = {\n        fileName: sanitizedFileName,\n        fileType: normalizedFileType,\n        fileSize: fileSize || 0,\n        uploadType: uploadType as 'user-document' | 'user-avatar'\n      }\n      if (entityId) {\n        requestData.userId = entityId\n      }\n\n      result = await store.dispatch(\n        usersApi.endpoints.getUserPresignedUrl.initiate(requestData)\n      ).unwrap()\n    } else {\n      // Use properties API for property-related uploads\n      const requestData: any = {\n        fileName: sanitizedFileName,\n        fileType: normalizedFileType,\n        fileSize: fileSize || 0,\n        uploadType: uploadType as 'property-image' | 'property-document'\n      }\n      if (entityId) {\n        requestData.propertyId = entityId\n      }\n\n      result = await store.dispatch(\n        propertiesApi.endpoints.getPresignedUrl.initiate(requestData)\n      ).unwrap()\n    }\n\n    return {\n      success: true,\n      data: result.data,\n      message: result.message\n    }\n  } catch (error: any) {\n    console.error('Presigned URL error:', error?.data?.message || error.message)\n    return {\n      success: false,\n      error: error?.data?.message || error.message || 'Failed to generate upload URL',\n    }\n  }\n}\n\n/**\n * Upload file to S3 using presigned URL\n */\nexport const uploadFileToS3 = async (\n  file: File,\n  presignedUrl: string\n): Promise<UploadResult> => {\n  try {\n    const response = await fetch(presignedUrl, {\n      method: 'PUT',\n      body: file,\n      headers: {\n        'Content-Type': file.type,\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Upload failed with status ${response.status}`)\n    }\n\n    return {\n      success: true,\n      url: presignedUrl,\n    }\n  } catch (error) {\n    console.error('S3 upload error:', error)\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Upload failed',\n    }\n  }\n}\n\n/**\n * Complete file upload process (get presigned URL + upload + confirm)\n */\nexport const uploadFileComplete = async (\n  file: File,\n  uploadType: 'property-image' | 'property-document' | 'user-avatar' | 'user-document' = 'property-image',\n  entityId?: string\n): Promise<UploadResult> => {\n  try {\n    // Step 1: Get presigned URL\n    const presignedResponse = await getPresignedUploadUrl(\n      file.name,\n      file.type,\n      uploadType,\n      file.size,\n      entityId\n    )\n\n    if (!presignedResponse.success || !presignedResponse.data) {\n      throw new Error(presignedResponse.error || 'Failed to get presigned URL')\n    }\n\n    // Step 2: Upload file to S3\n    const uploadResponse = await uploadFileToS3(file, presignedResponse.data.presignedUrl)\n\n    if (!uploadResponse.success) {\n      throw new Error(uploadResponse.error || 'Failed to upload file')\n    }\n\n    // Step 3: Confirm upload with backend\n    let confirmResult: any = null\n    const sanitizedFileNameForConfirm = sanitizeFileName(file.name)\n    const normalizedFileTypeForConfirm = normalizeFileType(file.type)\n\n    if (uploadType.startsWith('user-')) {\n      const confirmData: any = {\n        fileKey: presignedResponse.data.fileKey,\n        fileName: sanitizedFileNameForConfirm,\n        fileType: normalizedFileTypeForConfirm,\n        uploadType: uploadType as 'user-document' | 'user-avatar'\n      }\n      if (entityId) {\n        confirmData.userId = entityId\n      }\n\n      confirmResult = await store.dispatch(\n        usersApi.endpoints.confirmUserFileUpload.initiate(confirmData)\n      ).unwrap()\n    } else {\n      const confirmData: any = {\n        fileKey: presignedResponse.data.fileKey,\n        fileName: sanitizedFileNameForConfirm,\n        fileType: normalizedFileTypeForConfirm,\n        uploadType: uploadType as 'property-image' | 'property-document'\n      }\n      if (entityId) {\n        confirmData.propertyId = entityId\n      }\n\n      confirmResult = await store.dispatch(\n        propertiesApi.endpoints.confirmFileUpload.initiate(confirmData)\n      ).unwrap()\n    }\n\n    return {\n      success: true,\n      url: confirmResult?.data?.fileUrl || presignedResponse.data.publicUrl,\n      key: presignedResponse.data.fileKey,\n      fileKey: presignedResponse.data.fileKey,\n      publicUrl: confirmResult?.data?.fileUrl || presignedResponse.data.publicUrl,\n      presignedUrl: presignedResponse.data.presignedUrl,\n    }\n  } catch (error: any) {\n    console.error('Complete upload error:', error)\n    return {\n      success: false,\n      error: error?.data?.message || error.message || 'Upload failed',\n    }\n  }\n}\n\n/**\n * Upload multiple files to S3\n */\nexport const uploadMultipleFiles = async (\n  files: File[],\n  uploadType: 'property-image' | 'property-document' | 'user-avatar' | 'user-document' = 'property-image',\n  entityId?: string\n): Promise<UploadResult[]> => {\n  const uploadPromises = files.map(file => uploadFileComplete(file, uploadType, entityId))\n  return Promise.all(uploadPromises)\n}\n\n/**\n * Upload property images with optimization\n */\nexport const uploadPropertyImages = async (files: File[]): Promise<UploadResult[]> => {\n  return uploadMultipleFiles(files, 'property-image')\n}\n\n/**\n * Upload property documents\n */\nexport const uploadPropertyDocuments = async (files: File[]): Promise<UploadResult[]> => {\n  return uploadMultipleFiles(files, 'property-document')\n}\n\n/**\n * Upload user avatar\n */\nexport const uploadUserAvatar = async (file: File): Promise<UploadResult> => {\n  return uploadFileComplete(file, 'user-avatar')\n}\n\n/**\n * Upload user documents\n */\nexport const uploadUserDocuments = async (files: File[]): Promise<UploadResult[]> => {\n  return uploadMultipleFiles(files, 'user-document')\n}\n\n/**\n * Check S3 service health\n */\nexport const checkS3Health = async (): Promise<{ success: boolean; data?: any; error?: string }> => {\n  try {\n    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'\n    const response = await fetch(`${API_BASE_URL}/s3/health`)\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`)\n    }\n\n    const data = await response.json()\n    return data\n  } catch (error) {\n    console.error('S3 health check error:', error)\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Health check failed',\n    }\n  }\n}\n\n/**\n * Get file type information for preview purposes\n */\nexport const getFileTypeInfo = (fileName: string) => {\n  const extension = fileName.split('.').pop()?.toLowerCase() || ''\n\n  const typeMap = {\n    // Images\n    jpg: { type: 'image', icon: '🖼️', color: 'text-green-600', bgColor: 'bg-green-100' },\n    jpeg: { type: 'image', icon: '🖼️', color: 'text-green-600', bgColor: 'bg-green-100' },\n    png: { type: 'image', icon: '🖼️', color: 'text-green-600', bgColor: 'bg-green-100' },\n    webp: { type: 'image', icon: '🖼️', color: 'text-green-600', bgColor: 'bg-green-100' },\n\n    // Documents\n    pdf: { type: 'document', icon: '📄', color: 'text-red-600', bgColor: 'bg-red-100', label: 'PDF' },\n    doc: { type: 'document', icon: '📝', color: 'text-blue-600', bgColor: 'bg-blue-100', label: 'DOC' },\n    docx: { type: 'document', icon: '📝', color: 'text-blue-600', bgColor: 'bg-blue-100', label: 'DOCX' },\n    xls: { type: 'document', icon: '📊', color: 'text-green-600', bgColor: 'bg-green-100', label: 'XLS' },\n    xlsx: { type: 'document', icon: '📊', color: 'text-green-600', bgColor: 'bg-green-100', label: 'XLSX' },\n\n    // Default\n    default: { type: 'file', icon: '📁', color: 'text-gray-600', bgColor: 'bg-gray-100', label: extension.toUpperCase() }\n  }\n\n  return typeMap[extension as keyof typeof typeMap] || typeMap.default\n}\n\n/**\n * Check if file type supports inline preview\n */\nexport const supportsInlinePreview = (fileName: string): boolean => {\n  const fileInfo = getFileTypeInfo(fileName)\n  return fileInfo.type === 'image'\n}\n\n/**\n * Validate file type and size\n */\nexport const validateFile = (\n  file: File,\n  allowedTypes: string[] = [],\n  maxSizeMB: number = 10\n): { valid: boolean; error?: string } => {\n  // Check file size\n  const maxSizeBytes = maxSizeMB * 1024 * 1024\n  if (file.size > maxSizeBytes) {\n    return {\n      valid: false,\n      error: `File size must be less than ${maxSizeMB}MB`,\n    }\n  }\n\n  // Check file type if specified\n  if (allowedTypes.length > 0) {\n    const fileType = file.type.toLowerCase()\n    const isValidType = allowedTypes.some(type => \n      fileType.includes(type.toLowerCase()) || \n      file.name.toLowerCase().endsWith(type.toLowerCase())\n    )\n\n    if (!isValidType) {\n      return {\n        valid: false,\n        error: `File type must be one of: ${allowedTypes.join(', ')}`,\n      }\n    }\n  }\n\n  return { valid: true }\n}\n\n/**\n * Validate image file\n */\nexport const validateImageFile = (file: File): { valid: boolean; error?: string } => {\n  return validateFile(file, ['jpg', 'jpeg', 'png', 'webp', 'gif'], 5)\n}\n\n/**\n * Validate document file\n */\nexport const validateDocumentFile = (file: File): { valid: boolean; error?: string } => {\n  return validateFile(file, ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png'], 10)\n}\n\n/**\n * Get file preview URL\n */\nexport const getFilePreviewUrl = (file: File): string => {\n  return URL.createObjectURL(file)\n}\n\n/**\n * Format file size for display\n */\nexport const formatFileSize = (bytes: number): string => {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n/**\n * Get file type icon\n */\nexport const getFileTypeIcon = (fileName: string): string => {\n  const extension = fileName.split('.').pop()?.toLowerCase()\n  \n  switch (extension) {\n    case 'pdf':\n      return '📄'\n    case 'doc':\n    case 'docx':\n      return '📝'\n    case 'xls':\n    case 'xlsx':\n      return '📊'\n    case 'jpg':\n    case 'jpeg':\n    case 'png':\n    case 'gif':\n    case 'webp':\n      return '🖼️'\n    default:\n      return '📎'\n  }\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;;;;;;;;;;;;;;;AAClD;AACA;AACA;;;;AAgCA;;CAEC,GACD,MAAM,mBAAmB,CAAC;IACxB,OAAO,SACJ,OAAO,CAAC,iBAAiB,KAAK,+BAA+B;KAC7D,OAAO,CAAC,QAAQ,KAAK,kCAAkC;KACvD,OAAO,CAAC,UAAU,KAAK,2CAA2C;KAClE,WAAW,GAAG,uBAAuB;;AAC1C;AAEA;;CAEC,GACD,MAAM,oBAAoB,CAAC;IACzB,qCAAqC;IACrC,MAAM,UAAkC;QACtC,aAAa;QACb,2EAA2E;QAC3E,sBAAsB;IACxB;IAEA,OAAO,OAAO,CAAC,SAAS,IAAI;AAC9B;AAKO,MAAM,wBAAwB,OACnC,UACA,UACA,aAAuF,gBAAgB,EACvG,UACA;IAEA,IAAI;QACF,iDAAiD;QACjD,MAAM,oBAAoB,iBAAiB;QAC3C,MAAM,qBAAqB,kBAAkB;QAE7C,mCAAmC;QACnC,QAAQ,GAAG,CAAC,sBAAsB;YAChC,UAAU;YACV,UAAU;YACV;YACA,UAAU,YAAY;QACxB;QAEA,IAAI;QAEJ,IAAI,WAAW,UAAU,CAAC,UAAU;YAClC,wCAAwC;YACxC,MAAM,cAAmB;gBACvB,UAAU;gBACV,UAAU;gBACV,UAAU,YAAY;gBACtB,YAAY;YACd;YACA,IAAI,UAAU;gBACZ,YAAY,MAAM,GAAG;YACvB;YAEA,SAAS,MAAM,qHAAA,CAAA,QAAK,CAAC,QAAQ,CAC3B,+HAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,mBAAmB,CAAC,QAAQ,CAAC,cAChD,MAAM;QACV,OAAO;YACL,kDAAkD;YAClD,MAAM,cAAmB;gBACvB,UAAU;gBACV,UAAU;gBACV,UAAU,YAAY;gBACtB,YAAY;YACd;YACA,IAAI,UAAU;gBACZ,YAAY,UAAU,GAAG;YAC3B;YAEA,SAAS,MAAM,qHAAA,CAAA,QAAK,CAAC,QAAQ,CAC3B,oIAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,cACjD,MAAM;QACV;QAEA,OAAO;YACL,SAAS;YACT,MAAM,OAAO,IAAI;YACjB,SAAS,OAAO,OAAO;QACzB;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,wBAAwB,OAAO,MAAM,WAAW,MAAM,OAAO;QAC3E,OAAO;YACL,SAAS;YACT,OAAO,OAAO,MAAM,WAAW,MAAM,OAAO,IAAI;QAClD;IACF;AACF;AAKO,MAAM,iBAAiB,OAC5B,MACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,cAAc;YACzC,QAAQ;YACR,MAAM;YACN,SAAS;gBACP,gBAAgB,KAAK,IAAI;YAC3B;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,MAAM,EAAE;QAChE;QAEA,OAAO;YACL,SAAS;YACT,KAAK;QACP;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAKO,MAAM,qBAAqB,OAChC,MACA,aAAuF,gBAAgB,EACvG;IAEA,IAAI;QACF,4BAA4B;QAC5B,MAAM,oBAAoB,MAAM,sBAC9B,KAAK,IAAI,EACT,KAAK,IAAI,EACT,YACA,KAAK,IAAI,EACT;QAGF,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;YACzD,MAAM,IAAI,MAAM,kBAAkB,KAAK,IAAI;QAC7C;QAEA,4BAA4B;QAC5B,MAAM,iBAAiB,MAAM,eAAe,MAAM,kBAAkB,IAAI,CAAC,YAAY;QAErF,IAAI,CAAC,eAAe,OAAO,EAAE;YAC3B,MAAM,IAAI,MAAM,eAAe,KAAK,IAAI;QAC1C;QAEA,sCAAsC;QACtC,IAAI,gBAAqB;QACzB,MAAM,8BAA8B,iBAAiB,KAAK,IAAI;QAC9D,MAAM,+BAA+B,kBAAkB,KAAK,IAAI;QAEhE,IAAI,WAAW,UAAU,CAAC,UAAU;YAClC,MAAM,cAAmB;gBACvB,SAAS,kBAAkB,IAAI,CAAC,OAAO;gBACvC,UAAU;gBACV,UAAU;gBACV,YAAY;YACd;YACA,IAAI,UAAU;gBACZ,YAAY,MAAM,GAAG;YACvB;YAEA,gBAAgB,MAAM,qHAAA,CAAA,QAAK,CAAC,QAAQ,CAClC,+HAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,qBAAqB,CAAC,QAAQ,CAAC,cAClD,MAAM;QACV,OAAO;YACL,MAAM,cAAmB;gBACvB,SAAS,kBAAkB,IAAI,CAAC,OAAO;gBACvC,UAAU;gBACV,UAAU;gBACV,YAAY;YACd;YACA,IAAI,UAAU;gBACZ,YAAY,UAAU,GAAG;YAC3B;YAEA,gBAAgB,MAAM,qHAAA,CAAA,QAAK,CAAC,QAAQ,CAClC,oIAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,iBAAiB,CAAC,QAAQ,CAAC,cACnD,MAAM;QACV;QAEA,OAAO;YACL,SAAS;YACT,KAAK,eAAe,MAAM,WAAW,kBAAkB,IAAI,CAAC,SAAS;YACrE,KAAK,kBAAkB,IAAI,CAAC,OAAO;YACnC,SAAS,kBAAkB,IAAI,CAAC,OAAO;YACvC,WAAW,eAAe,MAAM,WAAW,kBAAkB,IAAI,CAAC,SAAS;YAC3E,cAAc,kBAAkB,IAAI,CAAC,YAAY;QACnD;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YACL,SAAS;YACT,OAAO,OAAO,MAAM,WAAW,MAAM,OAAO,IAAI;QAClD;IACF;AACF;AAKO,MAAM,sBAAsB,OACjC,OACA,aAAuF,gBAAgB,EACvG;IAEA,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAA,OAAQ,mBAAmB,MAAM,YAAY;IAC9E,OAAO,QAAQ,GAAG,CAAC;AACrB;AAKO,MAAM,uBAAuB,OAAO;IACzC,OAAO,oBAAoB,OAAO;AACpC;AAKO,MAAM,0BAA0B,OAAO;IAC5C,OAAO,oBAAoB,OAAO;AACpC;AAKO,MAAM,mBAAmB,OAAO;IACrC,OAAO,mBAAmB,MAAM;AAClC;AAKO,MAAM,sBAAsB,OAAO;IACxC,OAAO,oBAAoB,OAAO;AACpC;AAKO,MAAM,gBAAgB;IAC3B,IAAI;QACF,MAAM,eAAe,iEAAmC;QACxD,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC;QAExD,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAKO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB;IAE9D,MAAM,UAAU;QACd,SAAS;QACT,KAAK;YAAE,MAAM;YAAS,MAAM;YAAO,OAAO;YAAkB,SAAS;QAAe;QACpF,MAAM;YAAE,MAAM;YAAS,MAAM;YAAO,OAAO;YAAkB,SAAS;QAAe;QACrF,KAAK;YAAE,MAAM;YAAS,MAAM;YAAO,OAAO;YAAkB,SAAS;QAAe;QACpF,MAAM;YAAE,MAAM;YAAS,MAAM;YAAO,OAAO;YAAkB,SAAS;QAAe;QAErF,YAAY;QACZ,KAAK;YAAE,MAAM;YAAY,MAAM;YAAM,OAAO;YAAgB,SAAS;YAAc,OAAO;QAAM;QAChG,KAAK;YAAE,MAAM;YAAY,MAAM;YAAM,OAAO;YAAiB,SAAS;YAAe,OAAO;QAAM;QAClG,MAAM;YAAE,MAAM;YAAY,MAAM;YAAM,OAAO;YAAiB,SAAS;YAAe,OAAO;QAAO;QACpG,KAAK;YAAE,MAAM;YAAY,MAAM;YAAM,OAAO;YAAkB,SAAS;YAAgB,OAAO;QAAM;QACpG,MAAM;YAAE,MAAM;YAAY,MAAM;YAAM,OAAO;YAAkB,SAAS;YAAgB,OAAO;QAAO;QAEtG,UAAU;QACV,SAAS;YAAE,MAAM;YAAQ,MAAM;YAAM,OAAO;YAAiB,SAAS;YAAe,OAAO,UAAU,WAAW;QAAG;IACtH;IAEA,OAAO,OAAO,CAAC,UAAkC,IAAI,QAAQ,OAAO;AACtE;AAKO,MAAM,wBAAwB,CAAC;IACpC,MAAM,WAAW,gBAAgB;IACjC,OAAO,SAAS,IAAI,KAAK;AAC3B;AAKO,MAAM,eAAe,CAC1B,MACA,eAAyB,EAAE,EAC3B,YAAoB,EAAE;IAEtB,kBAAkB;IAClB,MAAM,eAAe,YAAY,OAAO;IACxC,IAAI,KAAK,IAAI,GAAG,cAAc;QAC5B,OAAO;YACL,OAAO;YACP,OAAO,CAAC,4BAA4B,EAAE,UAAU,EAAE,CAAC;QACrD;IACF;IAEA,+BAA+B;IAC/B,IAAI,aAAa,MAAM,GAAG,GAAG;QAC3B,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;QACtC,MAAM,cAAc,aAAa,IAAI,CAAC,CAAA,OACpC,SAAS,QAAQ,CAAC,KAAK,WAAW,OAClC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,WAAW;QAGnD,IAAI,CAAC,aAAa;YAChB,OAAO;gBACL,OAAO;gBACP,OAAO,CAAC,0BAA0B,EAAE,aAAa,IAAI,CAAC,OAAO;YAC/D;QACF;IACF;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB;AAKO,MAAM,oBAAoB,CAAC;IAChC,OAAO,aAAa,MAAM;QAAC;QAAO;QAAQ;QAAO;QAAQ;KAAM,EAAE;AACnE;AAKO,MAAM,uBAAuB,CAAC;IACnC,OAAO,aAAa,MAAM;QAAC;QAAO;QAAO;QAAQ;QAAO;QAAQ;QAAO;QAAQ;KAAM,EAAE;AACzF;AAKO,MAAM,oBAAoB,CAAC;IAChC,OAAO,IAAI,eAAe,CAAC;AAC7B;AAKO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAKO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;IAE7C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 2239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/FileUpload/EnhancedS3Upload.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback } from 'react'\nimport { useDropzone } from 'react-dropzone'\nimport { toast } from 'sonner'\nimport { Upload, X, FileText, Image, Video, CheckCircle, AlertCircle, Loader2 } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Progress } from '@/components/ui/progress'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  uploadFileComplete, \n  uploadMultipleFiles, \n  validateImageFile, \n  validateDocumentFile,\n  formatFileSize,\n  getFileTypeIcon \n} from '@/lib/s3Upload'\n\ninterface EnhancedS3UploadProps {\n  uploadType: 'property-image' | 'property-document' | 'user-avatar' | 'user-document'\n  entityId?: string // propertyId or userId depending on uploadType\n  onUploadComplete?: (results: Array<{ fileKey: string; publicUrl: string; fileName: string }>) => void\n  onUploadError?: (error: string) => void\n  maxFiles?: number\n  disabled?: boolean\n  className?: string\n  showPreview?: boolean\n  allowMultiple?: boolean\n}\n\ninterface UploadingFile {\n  file: File\n  progress: number\n  status: 'uploading' | 'success' | 'error'\n  fileKey?: string\n  publicUrl?: string\n  error?: string\n  id: string\n}\n\nconst EnhancedS3Upload: React.FC<EnhancedS3UploadProps> = ({\n  uploadType,\n  entityId,\n  onUploadComplete,\n  onUploadError,\n  maxFiles = 5,\n  disabled = false,\n  className = '',\n  showPreview = true,\n  allowMultiple = true,\n}) => {\n  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([])\n  const [isUploading, setIsUploading] = useState(false)\n\n  const getFileTypeValidation = () => {\n    const validations: Record<string, { accept: Record<string, string[]>; maxSize: number }> = {\n      'property-image': {\n        accept: { 'image/*': ['.jpg', '.jpeg', '.png', '.webp'] },\n        maxSize: 10 * 1024 * 1024, // 10MB\n      },\n      'property-document': {\n        accept: { \n          'application/pdf': ['.pdf'], \n          'application/msword': ['.doc', '.docx'],\n          'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']\n        },\n        maxSize: 5 * 1024 * 1024, // 5MB\n      },\n      'user-document': {\n        accept: { \n          'application/pdf': ['.pdf'], \n          'image/*': ['.jpg', '.jpeg', '.png'] \n        },\n        maxSize: 5 * 1024 * 1024, // 5MB\n      },\n      'user-avatar': {\n        accept: { 'image/*': ['.jpg', '.jpeg', '.png', '.webp'] },\n        maxSize: 2 * 1024 * 1024, // 2MB\n      },\n    }\n    return validations[uploadType] || validations['property-image']\n  }\n\n  const validateFile = (file: File): { valid: boolean; error?: string } => {\n    if (uploadType.includes('image') || uploadType === 'user-avatar') {\n      return validateImageFile(file)\n    } else {\n      return validateDocumentFile(file)\n    }\n  }\n\n  const uploadFileWithProgress = async (file: File, fileId: string): Promise<void> => {\n    return new Promise((resolve, reject) => {\n      // Simulate progress for presigned URL upload\n      let progress = 0\n      const progressInterval = setInterval(() => {\n        progress += Math.random() * 30\n        if (progress > 90) progress = 90\n        \n        setUploadingFiles(prev =>\n          prev.map(f => f.id === fileId ? { ...f, progress: Math.round(progress) } : f)\n        )\n      }, 200)\n\n      uploadFileComplete(file, uploadType, entityId)\n        .then(result => {\n          clearInterval(progressInterval)\n          \n          if (result.success) {\n            setUploadingFiles(prev =>\n              prev.map(f => f.id === fileId ? {\n                ...f,\n                progress: 100,\n                status: 'success' as const,\n                fileKey: result.fileKey,\n                publicUrl: result.publicUrl,\n              } : f)\n            )\n            resolve()\n          } else {\n            throw new Error(result.error || 'Upload failed')\n          }\n        })\n        .catch(error => {\n          clearInterval(progressInterval)\n          setUploadingFiles(prev =>\n            prev.map(f => f.id === fileId ? {\n              ...f,\n              status: 'error' as const,\n              error: error.message,\n            } : f)\n          )\n          reject(error)\n        })\n    })\n  }\n\n  const handleFileUpload = async (files: File[]) => {\n    if (disabled || isUploading) return\n\n    setIsUploading(true)\n    const newFiles: UploadingFile[] = files.map(file => ({\n      file,\n      progress: 0,\n      status: 'uploading' as const,\n      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n    }))\n\n    // Validate files\n    const validFiles: UploadingFile[] = []\n    const invalidFiles: string[] = []\n\n    newFiles.forEach(fileData => {\n      const validation = validateFile(fileData.file)\n      if (validation.valid) {\n        validFiles.push(fileData)\n      } else {\n        invalidFiles.push(`${fileData.file.name}: ${validation.error}`)\n      }\n    })\n\n    if (invalidFiles.length > 0) {\n      toast.error(`Invalid files: ${invalidFiles.join(', ')}`)\n      setIsUploading(false)\n      return\n    }\n\n    // Add files to uploading list\n    setUploadingFiles(prev => [...prev, ...validFiles])\n\n    try {\n      // Upload files\n      await Promise.all(\n        validFiles.map(fileData => \n          uploadFileWithProgress(fileData.file, fileData.id)\n        )\n      )\n\n      // Get successful uploads\n      const successfulUploads = uploadingFiles\n        .concat(validFiles)\n        .filter(f => f.status === 'success' && f.fileKey && f.publicUrl)\n        .map(f => ({\n          fileKey: f.fileKey!,\n          publicUrl: f.publicUrl!,\n          fileName: f.file.name,\n        }))\n\n      if (successfulUploads.length > 0) {\n        onUploadComplete?.(successfulUploads)\n        toast.success(`${successfulUploads.length} file(s) uploaded successfully`)\n      }\n\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Upload failed'\n      onUploadError?.(errorMessage)\n      toast.error(`Upload failed: ${errorMessage}`)\n    } finally {\n      setIsUploading(false)\n    }\n  }\n\n  const onDrop = useCallback((acceptedFiles: File[]) => {\n    const filesToUpload = allowMultiple ? acceptedFiles : acceptedFiles.slice(0, 1)\n    handleFileUpload(filesToUpload)\n  }, [uploadType, allowMultiple, disabled, isUploading])\n\n  const validation = getFileTypeValidation()\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: validation.accept,\n    maxSize: validation.maxSize,\n    maxFiles: allowMultiple ? maxFiles : 1,\n    disabled: disabled || isUploading,\n    multiple: allowMultiple,\n  })\n\n  const removeFile = (fileId: string) => {\n    setUploadingFiles(prev => prev.filter(f => f.id !== fileId))\n  }\n\n  const getFileIcon = (file: File) => {\n    if (file.type.startsWith('image/')) return <Image className=\"w-4 h-4\" />\n    if (file.type.startsWith('video/')) return <Video className=\"w-4 h-4\" />\n    return <FileText className=\"w-4 h-4\" />\n  }\n\n  const getStatusIcon = (status: UploadingFile['status']) => {\n    switch (status) {\n      case 'uploading':\n        return <Loader2 className=\"w-4 h-4 animate-spin text-blue-500\" />\n      case 'success':\n        return <CheckCircle className=\"w-4 h-4 text-green-500\" />\n      case 'error':\n        return <AlertCircle className=\"w-4 h-4 text-red-500\" />\n      default:\n        return null\n    }\n  }\n\n  const getStatusBadge = (status: UploadingFile['status']) => {\n    switch (status) {\n      case 'uploading':\n        return <Badge variant=\"secondary\">Uploading</Badge>\n      case 'success':\n        return <Badge variant=\"default\" className=\"bg-green-500\">Success</Badge>\n      case 'error':\n        return <Badge variant=\"destructive\">Error</Badge>\n      default:\n        return null\n    }\n  }\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* Dropzone */}\n      <div\n        {...getRootProps()}\n        className={`\n          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-200\n          ${isDragActive ? 'border-blue-500 bg-blue-50 scale-105' : 'border-gray-300 hover:border-gray-400'}\n          ${disabled || isUploading ? 'opacity-50 cursor-not-allowed' : ''}\n        `}\n      >\n        <input {...getInputProps()} />\n        <Upload className={`w-8 h-8 mx-auto mb-2 ${isDragActive ? 'text-blue-500' : 'text-gray-400'}`} />\n        <p className=\"text-sm text-gray-600 font-medium\">\n          {isDragActive\n            ? 'Drop files here...'\n            : `Drag & drop ${allowMultiple ? 'files' : 'a file'} here, or click to select`\n          }\n        </p>\n        <p className=\"text-xs text-gray-500 mt-1\">\n          Max size: {Math.round(validation.maxSize / (1024 * 1024))}MB\n          {allowMultiple && ` • Max files: ${maxFiles}`}\n        </p>\n        <p className=\"text-xs text-gray-400 mt-1\">\n          Supported: {Object.values(validation.accept).flat().join(', ')}\n        </p>\n      </div>\n\n      {/* Uploading files list */}\n      {uploadingFiles.length > 0 && (\n        <div className=\"space-y-3\">\n          <h4 className=\"text-sm font-medium text-gray-700\">\n            Upload Progress ({uploadingFiles.length} file{uploadingFiles.length !== 1 ? 's' : ''})\n          </h4>\n          {uploadingFiles.map((uploadingFile) => (\n            <div key={uploadingFile.id} className=\"flex items-center space-x-3 p-4 bg-gray-50 rounded-lg border\">\n              {getFileIcon(uploadingFile.file)}\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center justify-between mb-1\">\n                  <p className=\"text-sm font-medium text-gray-900 truncate\">\n                    {uploadingFile.file.name}\n                  </p>\n                  <div className=\"flex items-center space-x-2\">\n                    {getStatusIcon(uploadingFile.status)}\n                    {getStatusBadge(uploadingFile.status)}\n                  </div>\n                </div>\n                <p className=\"text-xs text-gray-500 mb-2\">\n                  {formatFileSize(uploadingFile.file.size)} • {uploadingFile.file.type}\n                </p>\n                {uploadingFile.status === 'uploading' && (\n                  <div className=\"flex items-center space-x-2\">\n                    <Progress value={uploadingFile.progress} className=\"flex-1\" />\n                    <span className=\"text-xs text-gray-500 min-w-[3rem]\">\n                      {uploadingFile.progress}%\n                    </span>\n                  </div>\n                )}\n                {uploadingFile.status === 'success' && uploadingFile.publicUrl && (\n                  <div className=\"mt-2 space-y-2\">\n                    {/* Preview for images */}\n                    {uploadingFile.file.type.startsWith('image/') && (\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-16 h-16 rounded-lg overflow-hidden bg-gray-100 border\">\n                          <img\n                            src={uploadingFile.publicUrl}\n                            alt={uploadingFile.file.name}\n                            className=\"w-full h-full object-cover\"\n                            onError={(e) => {\n                              const target = e.target as HTMLImageElement;\n                              target.style.display = 'none';\n                            }}\n                          />\n                        </div>\n                        <div className=\"flex-1\">\n                          <a\n                            href={uploadingFile.publicUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"text-xs text-blue-600 hover:text-blue-800 underline block\"\n                          >\n                            View full image\n                          </a>\n                          <p className=\"text-xs text-gray-500 mt-1\">\n                            Image uploaded successfully\n                          </p>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Preview for documents */}\n                    {!uploadingFile.file.type.startsWith('image/') && (\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-16 h-16 rounded-lg bg-gray-100 border flex items-center justify-center\">\n                          {uploadingFile.file.type === 'application/pdf' ? (\n                            <FileText className=\"w-8 h-8 text-red-500\" />\n                          ) : uploadingFile.file.type.includes('word') ? (\n                            <FileText className=\"w-8 h-8 text-blue-500\" />\n                          ) : (\n                            <FileText className=\"w-8 h-8 text-gray-500\" />\n                          )}\n                        </div>\n                        <div className=\"flex-1\">\n                          <a\n                            href={uploadingFile.publicUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"text-xs text-blue-600 hover:text-blue-800 underline block\"\n                          >\n                            Download document\n                          </a>\n                          <p className=\"text-xs text-gray-500 mt-1\">\n                            {uploadingFile.file.type === 'application/pdf' ? 'PDF Document' :\n                             uploadingFile.file.type.includes('word') ? 'Word Document' :\n                             'Document'} uploaded successfully\n                          </p>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                )}\n                {uploadingFile.status === 'error' && uploadingFile.error && (\n                  <p className=\"text-xs text-red-600 mt-1 bg-red-50 p-2 rounded\">\n                    {uploadingFile.error}\n                  </p>\n                )}\n              </div>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => removeFile(uploadingFile.id)}\n                className=\"text-gray-400 hover:text-gray-600\"\n                disabled={uploadingFile.status === 'uploading'}\n              >\n                <X className=\"w-4 h-4\" />\n              </Button>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default EnhancedS3Upload\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAwCA,MAAM,mBAAoD,CAAC,EACzD,UAAU,EACV,QAAQ,EACR,gBAAgB,EAChB,aAAa,EACb,WAAW,CAAC,EACZ,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,cAAc,IAAI,EAClB,gBAAgB,IAAI,EACrB;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,wBAAwB;QAC5B,MAAM,cAAqF;YACzF,kBAAkB;gBAChB,QAAQ;oBAAE,WAAW;wBAAC;wBAAQ;wBAAS;wBAAQ;qBAAQ;gBAAC;gBACxD,SAAS,KAAK,OAAO;YACvB;YACA,qBAAqB;gBACnB,QAAQ;oBACN,mBAAmB;wBAAC;qBAAO;oBAC3B,sBAAsB;wBAAC;wBAAQ;qBAAQ;oBACvC,2EAA2E;wBAAC;qBAAQ;gBACtF;gBACA,SAAS,IAAI,OAAO;YACtB;YACA,iBAAiB;gBACf,QAAQ;oBACN,mBAAmB;wBAAC;qBAAO;oBAC3B,WAAW;wBAAC;wBAAQ;wBAAS;qBAAO;gBACtC;gBACA,SAAS,IAAI,OAAO;YACtB;YACA,eAAe;gBACb,QAAQ;oBAAE,WAAW;wBAAC;wBAAQ;wBAAS;wBAAQ;qBAAQ;gBAAC;gBACxD,SAAS,IAAI,OAAO;YACtB;QACF;QACA,OAAO,WAAW,CAAC,WAAW,IAAI,WAAW,CAAC,iBAAiB;IACjE;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,WAAW,QAAQ,CAAC,YAAY,eAAe,eAAe;YAChE,OAAO,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE;QAC3B,OAAO;YACL,OAAO,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EAAE;QAC9B;IACF;IAEA,MAAM,yBAAyB,OAAO,MAAY;QAChD,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,6CAA6C;YAC7C,IAAI,WAAW;YACf,MAAM,mBAAmB,YAAY;gBACnC,YAAY,KAAK,MAAM,KAAK;gBAC5B,IAAI,WAAW,IAAI,WAAW;gBAE9B,kBAAkB,CAAA,OAChB,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;4BAAE,GAAG,CAAC;4BAAE,UAAU,KAAK,KAAK,CAAC;wBAAU,IAAI;YAE/E,GAAG;YAEH,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,YAAY,UAClC,IAAI,CAAC,CAAA;gBACJ,cAAc;gBAEd,IAAI,OAAO,OAAO,EAAE;oBAClB,kBAAkB,CAAA,OAChB,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;gCAC9B,GAAG,CAAC;gCACJ,UAAU;gCACV,QAAQ;gCACR,SAAS,OAAO,OAAO;gCACvB,WAAW,OAAO,SAAS;4BAC7B,IAAI;oBAEN;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;gBAClC;YACF,GACC,KAAK,CAAC,CAAA;gBACL,cAAc;gBACd,kBAAkB,CAAA,OAChB,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;4BAC9B,GAAG,CAAC;4BACJ,QAAQ;4BACR,OAAO,MAAM,OAAO;wBACtB,IAAI;gBAEN,OAAO;YACT;QACJ;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,YAAY,aAAa;QAE7B,eAAe;QACf,MAAM,WAA4B,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACnD;gBACA,UAAU;gBACV,QAAQ;gBACR,IAAI,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAChE,CAAC;QAED,iBAAiB;QACjB,MAAM,aAA8B,EAAE;QACtC,MAAM,eAAyB,EAAE;QAEjC,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,aAAa,aAAa,SAAS,IAAI;YAC7C,IAAI,WAAW,KAAK,EAAE;gBACpB,WAAW,IAAI,CAAC;YAClB,OAAO;gBACL,aAAa,IAAI,CAAC,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,KAAK,EAAE;YAChE;QACF;QAEA,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,aAAa,IAAI,CAAC,OAAO;YACvD,eAAe;YACf;QACF;QAEA,8BAA8B;QAC9B,kBAAkB,CAAA,OAAQ;mBAAI;mBAAS;aAAW;QAElD,IAAI;YACF,eAAe;YACf,MAAM,QAAQ,GAAG,CACf,WAAW,GAAG,CAAC,CAAA,WACb,uBAAuB,SAAS,IAAI,EAAE,SAAS,EAAE;YAIrD,yBAAyB;YACzB,MAAM,oBAAoB,eACvB,MAAM,CAAC,YACP,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,EAAE,OAAO,IAAI,EAAE,SAAS,EAC9D,GAAG,CAAC,CAAA,IAAK,CAAC;oBACT,SAAS,EAAE,OAAO;oBAClB,WAAW,EAAE,SAAS;oBACtB,UAAU,EAAE,IAAI,CAAC,IAAI;gBACvB,CAAC;YAEH,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAChC,mBAAmB;gBACnB,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,kBAAkB,MAAM,CAAC,8BAA8B,CAAC;YAC3E;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,gBAAgB;YAChB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,cAAc;QAC9C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,SAAS,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1B,MAAM,gBAAgB,gBAAgB,gBAAgB,cAAc,KAAK,CAAC,GAAG;QAC7E,iBAAiB;IACnB,GAAG;QAAC;QAAY;QAAe;QAAU;KAAY;IAErD,MAAM,aAAa;IACnB,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,6PAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ,WAAW,MAAM;QACzB,SAAS,WAAW,OAAO;QAC3B,UAAU,gBAAgB,WAAW;QACrC,UAAU,YAAY;QACtB,UAAU;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,kBAAkB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACtD;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,qBAAO,6WAAC,wRAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAC5D,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,qBAAO,6WAAC,wRAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAC5D,qBAAO,6WAAC,kSAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6WAAC,qSAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6WAAC,+SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,6WAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAY;;;;;;YACpC,KAAK;gBACH,qBAAO,6WAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAe;;;;;;YAC3D,KAAK;gBACH,qBAAO,6WAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAc;;;;;;YACtC;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6WAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,6WAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAC;;UAEV,EAAE,eAAe,yCAAyC,wCAAwC;UAClG,EAAE,YAAY,cAAc,kCAAkC,GAAG;QACnE,CAAC;;kCAED,6WAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,6WAAC,0RAAA,CAAA,SAAM;wBAAC,WAAW,CAAC,qBAAqB,EAAE,eAAe,kBAAkB,iBAAiB;;;;;;kCAC7F,6WAAC;wBAAE,WAAU;kCACV,eACG,uBACA,CAAC,YAAY,EAAE,gBAAgB,UAAU,SAAS,yBAAyB,CAAC;;;;;;kCAGlF,6WAAC;wBAAE,WAAU;;4BAA6B;4BAC7B,KAAK,KAAK,CAAC,WAAW,OAAO,GAAG,CAAC,OAAO,IAAI;4BAAG;4BACzD,iBAAiB,CAAC,cAAc,EAAE,UAAU;;;;;;;kCAE/C,6WAAC;wBAAE,WAAU;;4BAA6B;4BAC5B,OAAO,MAAM,CAAC,WAAW,MAAM,EAAE,IAAI,GAAG,IAAI,CAAC;;;;;;;;;;;;;YAK5D,eAAe,MAAM,GAAG,mBACvB,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;;4BAAoC;4BAC9B,eAAe,MAAM;4BAAC;4BAAM,eAAe,MAAM,KAAK,IAAI,MAAM;4BAAG;;;;;;;oBAEtF,eAAe,GAAG,CAAC,CAAC,8BACnB,6WAAC;4BAA2B,WAAU;;gCACnC,YAAY,cAAc,IAAI;8CAC/B,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAE,WAAU;8DACV,cAAc,IAAI,CAAC,IAAI;;;;;;8DAE1B,6WAAC;oDAAI,WAAU;;wDACZ,cAAc,cAAc,MAAM;wDAClC,eAAe,cAAc,MAAM;;;;;;;;;;;;;sDAGxC,6WAAC;4CAAE,WAAU;;gDACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,IAAI,CAAC,IAAI;gDAAE;gDAAI,cAAc,IAAI,CAAC,IAAI;;;;;;;wCAErE,cAAc,MAAM,KAAK,6BACxB,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,oIAAA,CAAA,WAAQ;oDAAC,OAAO,cAAc,QAAQ;oDAAE,WAAU;;;;;;8DACnD,6WAAC;oDAAK,WAAU;;wDACb,cAAc,QAAQ;wDAAC;;;;;;;;;;;;;wCAI7B,cAAc,MAAM,KAAK,aAAa,cAAc,SAAS,kBAC5D,6WAAC;4CAAI,WAAU;;gDAEZ,cAAc,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,2BAClC,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAI,WAAU;sEACb,cAAA,6WAAC;gEACC,KAAK,cAAc,SAAS;gEAC5B,KAAK,cAAc,IAAI,CAAC,IAAI;gEAC5B,WAAU;gEACV,SAAS,CAAC;oEACR,MAAM,SAAS,EAAE,MAAM;oEACvB,OAAO,KAAK,CAAC,OAAO,GAAG;gEACzB;;;;;;;;;;;sEAGJ,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEACC,MAAM,cAAc,SAAS;oEAC7B,QAAO;oEACP,KAAI;oEACJ,WAAU;8EACX;;;;;;8EAGD,6WAAC;oEAAE,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;gDAQ/C,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,2BACnC,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAI,WAAU;sEACZ,cAAc,IAAI,CAAC,IAAI,KAAK,kCAC3B,6WAAC,kSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;uEAClB,cAAc,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,wBACnC,6WAAC,kSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;qFAEpB,6WAAC,kSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAGxB,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEACC,MAAM,cAAc,SAAS;oEAC7B,QAAO;oEACP,KAAI;oEACJ,WAAU;8EACX;;;;;;8EAGD,6WAAC;oEAAE,WAAU;;wEACV,cAAc,IAAI,CAAC,IAAI,KAAK,oBAAoB,iBAChD,cAAc,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,kBAC3C;wEAAW;;;;;;;;;;;;;;;;;;;;;;;;;wCAOvB,cAAc,MAAM,KAAK,WAAW,cAAc,KAAK,kBACtD,6WAAC;4CAAE,WAAU;sDACV,cAAc,KAAK;;;;;;;;;;;;8CAI1B,6WAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,WAAW,cAAc,EAAE;oCAC1C,WAAU;oCACV,UAAU,cAAc,MAAM,KAAK;8CAEnC,cAAA,6WAAC,gRAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;2BAnGP,cAAc,EAAE;;;;;;;;;;;;;;;;;AA2GtC;uCAEe", "debugId": null}}, {"offset": {"line": 2875, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/app/properties/add/PropertyDocumentUpload.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport EnhancedS3Upload from '@/components/FileUpload/EnhancedS3Upload'\n\ninterface DocumentUploadProps {\n  documentType: string\n  label: string\n  description?: string\n  maxFiles?: number\n  onUploadComplete: (results: Array<{ fileKey: string; publicUrl: string; fileName: string }>) => void\n  onUploadError: (error: string) => void\n  existingFiles?: Array<{ url: string; name: string; size?: number }>\n}\n\nconst PropertyDocumentUpload: React.FC<DocumentUploadProps> = ({\n  documentType,\n  label,\n  description,\n  maxFiles = 5,\n  onUploadComplete,\n  onUploadError,\n  existingFiles = []\n}) => {\n  return (\n    <div>\n      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n        {label}\n      </label>\n      <EnhancedS3Upload\n        uploadType=\"property-document\"\n        onUploadComplete={onUploadComplete}\n        onUploadError={onUploadError}\n        maxFiles={maxFiles}\n        allowMultiple={true}\n        className=\"border border-gray-300 rounded-lg\"\n      />\n      {description && (\n        <p className=\"text-xs text-gray-500 mt-1\">{description}</p>\n      )}\n      \n      {/* Show existing files with enhanced previews */}\n      {existingFiles.length > 0 && (\n        <div className=\"mt-3\">\n          <p className=\"text-sm font-medium text-gray-700 mb-2\">\n            Uploaded Files ({existingFiles.length})\n          </p>\n          <div className=\"space-y-3\">\n            {existingFiles.map((file, index) => {\n              const fileExtension = file.name.split('.').pop()?.toLowerCase()\n              const isPDF = fileExtension === 'pdf'\n              const isWord = fileExtension === 'doc' || fileExtension === 'docx'\n              const isExcel = fileExtension === 'xls' || fileExtension === 'xlsx'\n\n              return (\n                <div key={index} className=\"flex items-center gap-3 p-3 bg-gray-50 rounded-lg border hover:bg-gray-100 transition-colors\">\n                  {/* Enhanced file icon with preview */}\n                  <div className=\"flex-shrink-0\">\n                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${\n                      isPDF ? 'bg-red-100' :\n                      isWord ? 'bg-blue-100' :\n                      isExcel ? 'bg-green-100' :\n                      'bg-gray-100'\n                    }`}>\n                      {isPDF ? (\n                        <div className=\"text-center\">\n                          <div className=\"text-red-600 text-xs font-bold\">PDF</div>\n                        </div>\n                      ) : isWord ? (\n                        <div className=\"text-center\">\n                          <div className=\"text-blue-600 text-xs font-bold\">DOC</div>\n                        </div>\n                      ) : isExcel ? (\n                        <div className=\"text-center\">\n                          <div className=\"text-green-600 text-xs font-bold\">XLS</div>\n                        </div>\n                      ) : (\n                        <span className=\"text-gray-600 text-xs font-semibold\">\n                          {fileExtension?.toUpperCase()}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* File info */}\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"text-sm font-medium text-gray-900 truncate\">{file.name}</p>\n                    <div className=\"flex items-center gap-2 mt-1\">\n                      {file.size && (\n                        <span className=\"text-xs text-gray-500\">\n                          {(file.size / 1024 / 1024).toFixed(2)} MB\n                        </span>\n                      )}\n                      <span className=\"text-xs text-gray-400\">•</span>\n                      <span className={`text-xs px-2 py-1 rounded-full ${\n                        isPDF ? 'bg-red-100 text-red-700' :\n                        isWord ? 'bg-blue-100 text-blue-700' :\n                        isExcel ? 'bg-green-100 text-green-700' :\n                        'bg-gray-100 text-gray-700'\n                      }`}>\n                        {isPDF ? 'PDF Document' :\n                         isWord ? 'Word Document' :\n                         isExcel ? 'Excel Spreadsheet' :\n                         'Document'}\n                      </span>\n                    </div>\n                  </div>\n\n                  {/* Actions */}\n                  <div className=\"flex items-center gap-2\">\n                    <a\n                      href={file.url}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\n                    >\n                      View\n                    </a>\n                    <button\n                      type=\"button\"\n                      className=\"px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors\"\n                      onClick={() => {\n                        // Handle file removal\n                        console.log('Remove file:', file.name)\n                      }}\n                    >\n                      Remove\n                    </button>\n                  </div>\n                </div>\n              )\n            })}\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default PropertyDocumentUpload\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAeA,MAAM,yBAAwD,CAAC,EAC7D,YAAY,EACZ,KAAK,EACL,WAAW,EACX,WAAW,CAAC,EACZ,gBAAgB,EAChB,aAAa,EACb,gBAAgB,EAAE,EACnB;IACC,qBACE,6WAAC;;0BACC,6WAAC;gBAAM,WAAU;0BACd;;;;;;0BAEH,6WAAC,oJAAA,CAAA,UAAgB;gBACf,YAAW;gBACX,kBAAkB;gBAClB,eAAe;gBACf,UAAU;gBACV,eAAe;gBACf,WAAU;;;;;;YAEX,6BACC,6WAAC;gBAAE,WAAU;0BAA8B;;;;;;YAI5C,cAAc,MAAM,GAAG,mBACtB,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAE,WAAU;;4BAAyC;4BACnC,cAAc,MAAM;4BAAC;;;;;;;kCAExC,6WAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,MAAM;4BACxB,MAAM,gBAAgB,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;4BAClD,MAAM,QAAQ,kBAAkB;4BAChC,MAAM,SAAS,kBAAkB,SAAS,kBAAkB;4BAC5D,MAAM,UAAU,kBAAkB,SAAS,kBAAkB;4BAE7D,qBACE,6WAAC;gCAAgB,WAAU;;kDAEzB,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC;4CAAI,WAAW,CAAC,sDAAsD,EACrE,QAAQ,eACR,SAAS,gBACT,UAAU,iBACV,eACA;sDACC,sBACC,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;8DAAiC;;;;;;;;;;uDAEhD,uBACF,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;8DAAkC;;;;;;;;;;uDAEjD,wBACF,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;8DAAmC;;;;;;;;;;qEAGpD,6WAAC;gDAAK,WAAU;0DACb,eAAe;;;;;;;;;;;;;;;;kDAOxB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAE,WAAU;0DAA8C,KAAK,IAAI;;;;;;0DACpE,6WAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI,kBACR,6WAAC;wDAAK,WAAU;;4DACb,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;4DAAG;;;;;;;kEAG1C,6WAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6WAAC;wDAAK,WAAW,CAAC,+BAA+B,EAC/C,QAAQ,4BACR,SAAS,8BACT,UAAU,gCACV,6BACA;kEACC,QAAQ,iBACR,SAAS,kBACT,UAAU,sBACV;;;;;;;;;;;;;;;;;;kDAMP,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDACC,MAAM,KAAK,GAAG;gDACd,QAAO;gDACP,KAAI;gDACJ,WAAU;0DACX;;;;;;0DAGD,6WAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS;oDACP,sBAAsB;oDACtB,QAAQ,GAAG,CAAC,gBAAgB,KAAK,IAAI;gDACvC;0DACD;;;;;;;;;;;;;+BAtEK;;;;;wBA4Ed;;;;;;;;;;;;;;;;;;AAMZ;uCAEe", "debugId": null}}, {"offset": {"line": 3121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/app/properties/add/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { toast } from 'sonner'\nimport DashboardLayout from '@/components/layout/DashboardLayout'\nimport EnhancedS3Upload from '@/components/FileUpload/EnhancedS3Upload'\nimport PropertyDocumentUpload from './PropertyDocumentUpload'\nimport { useCreatePropertyMutation } from '@/store/api/propertiesApi'\nimport { useGetPropertyOwnersQuery } from '@/store/api/propertyOwnersApi'\nimport { Property } from '@/types'\nimport { useRouter } from 'next/navigation'\nimport {\n  Building,\n  Save,\n  ArrowLeft,\n  Upload,\n  MapPin,\n  Calendar,\n  DollarSign,\n  Home,\n  Building2,\n  Star,\n  Image,\n  Plus,\n  X,\n  AlertCircle,\n  CheckCircle,\n  Eye,\n  FileText,\n  Navigation,\n  Loader2\n} from 'lucide-react'\n\ninterface PropertyFormData {\n  name: string\n  description: string\n  propertyType: string // Changed from 'type' to match model\n  location: {\n    address: string\n    city: string\n    state: string\n    pincode: string\n    coordinates?: {\n      latitude: number\n      longitude: number\n    }\n  }\n\n  // Required financial fields from model\n  expectedReturns: number\n  maturityPeriodMonths: number\n\n  // Construction details\n  constructionStatus: string\n  launchDate: string\n  expectedCompletion: string\n  actualCompletion?: string\n\n  // Developer details (structured object from model)\n  developer: {\n    name: string\n    contact: string\n    email: string\n    experience?: number\n  }\n\n  amenities: string[]\n\n  // Images with proper structure from model\n  images: Array<{\n    key: string\n    name: string\n    type: string\n    url: string\n    uploadedAt?: Date\n  }>\n\n  // Documents with proper structure from model\n  documents: Array<{\n    key: string\n    name: string\n    type: string\n    url: string\n    uploadedAt?: Date\n  }>\n\n  // Videos with proper structure from model\n  videos: Array<{\n    key: string\n    name: string\n    type: string\n    url: string\n    uploadedAt?: Date\n  }>\n\n  // Legal documents array\n  legalDocuments: string[]\n\n  // Required fields from model\n  ownerId: string\n  status: string\n  featured: boolean\n  priorityOrder: number\n}\n\nexport default function AddPropertyPage() {\n  const router = useRouter()\n  const [createProperty, { isLoading: isCreating }] = useCreatePropertyMutation()\n  const { data: propertyOwnersData, isLoading: isLoadingOwners } = useGetPropertyOwnersQuery({\n    page: 1,\n    limit: 100,\n    status: 'active'\n  })\n\n  // Coordinate detection state\n  const [isDetectingLocation, setIsDetectingLocation] = useState(false)\n  const [locationError, setLocationError] = useState<string | null>(null)\n\n  const [formData, setFormData] = useState<PropertyFormData>({\n    name: '',\n    description: '',\n    propertyType: 'residential',\n    location: {\n      address: '',\n      city: '',\n      state: '',\n      pincode: '',\n      coordinates: {\n        latitude: 0,\n        longitude: 0\n      }\n    },\n    expectedReturns: 0,\n    maturityPeriodMonths: 12,\n    constructionStatus: 'planning',\n    launchDate: '',\n    expectedCompletion: '',\n    actualCompletion: '',\n    developer: {\n      name: '',\n      contact: '',\n      email: '',\n      experience: 0\n    },\n    amenities: [],\n    images: [],\n    documents: [],\n    videos: [],\n    legalDocuments: [],\n    ownerId: '',\n    status: 'active',\n    featured: false,\n    priorityOrder: 0\n  })\n\n  const [newAmenity, setNewAmenity] = useState('')\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [errors, setErrors] = useState<Record<string, string>>({})\n\n\n\n  // Owner selection state\n  const [ownerSearchQuery, setOwnerSearchQuery] = useState('')\n  const [showOwnerDropdown, setShowOwnerDropdown] = useState(false)\n  const [selectedOwner, setSelectedOwner] = useState<any>(null)\n  const [highlightedIndex, setHighlightedIndex] = useState(-1)\n\n  // Dummy owners data for development\n  const dummyOwners = [\n    {\n      id: 'owner1',\n      name: 'Rajesh Kumar',\n      email: '<EMAIL>',\n      company: 'Kumar Enterprises',\n      status: 'verified',\n      properties: 5,\n      phone: '+91 98765 43210'\n    },\n    {\n      id: 'owner2',\n      name: 'Priya Sharma',\n      email: '<EMAIL>',\n      company: 'Sharma Constructions',\n      status: 'verified',\n      properties: 3,\n      phone: '+91 98765 43211'\n    },\n    {\n      id: 'owner3',\n      name: 'Amit Patel',\n      email: '<EMAIL>',\n      company: null,\n      status: 'pending',\n      properties: 1,\n      phone: '+91 98765 43212'\n    },\n    {\n      id: 'owner4',\n      name: 'Sunita Gupta',\n      email: '<EMAIL>',\n      company: 'Gupta Real Estate',\n      status: 'verified',\n      properties: 8,\n      phone: '+91 98765 43213'\n    },\n    {\n      id: 'owner5',\n      name: 'Vikram Singh',\n      email: '<EMAIL>',\n      company: 'Singh Properties',\n      status: 'verified',\n      properties: 12,\n      phone: '+91 98765 43214'\n    },\n    {\n      id: 'owner6',\n      name: 'Neha Agarwal',\n      email: '<EMAIL>',\n      company: null,\n      status: 'verified',\n      properties: 2,\n      phone: '+91 98765 43215'\n    },\n    {\n      id: 'owner7',\n      name: 'Ravi Mehta',\n      email: '<EMAIL>',\n      company: 'Mehta Developers',\n      status: 'pending',\n      properties: 0,\n      phone: '+91 98765 43216'\n    },\n    {\n      id: 'owner8',\n      name: 'Kavita Joshi',\n      email: '<EMAIL>',\n      company: 'Joshi Investments',\n      status: 'verified',\n      properties: 6,\n      phone: '+91 98765 43217'\n    }\n  ]\n\n  // Use real API data\n  const realOwners = propertyOwnersData?.data?.data || []\n\n  // Use real data if available, fallback to dummy data for development\n  const availableOwners = realOwners.length > 0 ? realOwners.map(owner => ({\n    id: owner._id || owner.id,\n    name: owner.fullName || `${owner.firstName} ${owner.lastName}`,\n    email: owner.email,\n    company: owner.company || null,\n    status: owner.verificationStatus || 'pending',\n    properties: owner.totalProperties || 0,\n    phone: owner.phone\n  })) : dummyOwners\n\n  // Filter owners based on search query\n  const filteredOwners = availableOwners.filter(owner => {\n    if (!ownerSearchQuery) return true\n\n    const searchLower = ownerSearchQuery.toLowerCase()\n    return (\n      owner.name.toLowerCase().includes(searchLower) ||\n      owner.email.toLowerCase().includes(searchLower) ||\n      (owner.company && owner.company.toLowerCase().includes(searchLower)) ||\n      owner.phone.includes(ownerSearchQuery)\n    )\n  })\n\n  // Owner selection functions\n  const selectOwner = (owner: any) => {\n    setSelectedOwner(owner)\n    setOwnerSearchQuery(owner.name)\n    setShowOwnerDropdown(false)\n    handleInputChange('ownerId', owner.id)\n  }\n\n  const clearSelectedOwner = () => {\n    setSelectedOwner(null)\n    setOwnerSearchQuery('')\n    setHighlightedIndex(-1)\n    handleInputChange('ownerId', '')\n  }\n\n  // Handle keyboard navigation\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (!showOwnerDropdown) return\n\n    switch (e.key) {\n      case 'ArrowDown':\n        e.preventDefault()\n        setHighlightedIndex(prev =>\n          prev < filteredOwners.length - 1 ? prev + 1 : 0\n        )\n        break\n      case 'ArrowUp':\n        e.preventDefault()\n        setHighlightedIndex(prev =>\n          prev > 0 ? prev - 1 : filteredOwners.length - 1\n        )\n        break\n      case 'Enter':\n        e.preventDefault()\n        if (highlightedIndex >= 0 && filteredOwners[highlightedIndex]) {\n          selectOwner(filteredOwners[highlightedIndex])\n        }\n        break\n      case 'Escape':\n        setShowOwnerDropdown(false)\n        setHighlightedIndex(-1)\n        break\n    }\n  }\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      const target = event.target as HTMLElement\n      if (!target.closest('.owner-search-container')) {\n        setShowOwnerDropdown(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  // Property types from server enums\n  const propertyTypes = [\n    { value: 'residential', label: 'Residential', icon: Home, description: 'Houses, apartments, condos' },\n    { value: 'commercial', label: 'Commercial', icon: Building2, description: 'Offices, retail, warehouses' },\n    { value: 'mixed', label: 'Mixed Use', icon: Building, description: 'Residential + Commercial' },\n    { value: 'luxury', label: 'Luxury', icon: Star, description: 'High-end premium properties' },\n    { value: 'eco_friendly', label: 'Eco-Friendly', icon: Home, description: 'Sustainable green buildings' }\n  ]\n\n  // Construction status from server enums\n  const constructionStatuses = [\n    { value: 'planning', label: 'Planning', description: 'In planning phase' },\n    { value: 'under_construction', label: 'Under Construction', description: 'Currently being built' },\n    { value: 'completed', label: 'Completed', description: 'Construction finished' },\n    { value: 'ready_to_move', label: 'Ready to Move', description: 'Ready for occupancy' }\n  ]\n\n  // Property status from server enums\n  const propertyStatuses = [\n    { value: 'active', label: 'Active', description: 'Available for investment' },\n    { value: 'inactive', label: 'Inactive', description: 'Not available' },\n    { value: 'sold_out', label: 'Sold Out', description: 'All stocks sold' },\n    { value: 'completed', label: 'Completed', description: 'Investment completed' }\n  ]\n\n  const categories = [\n    { value: 'Luxury', label: 'Luxury', description: 'High-end premium properties' },\n    { value: 'Premium', label: 'Premium', description: 'Premium quality properties' },\n    { value: 'Mid-Range', label: 'Mid-Range', description: 'Affordable quality properties' },\n    { value: 'Budget', label: 'Budget', description: 'Budget-friendly properties' }\n  ]\n\n  const statusOptions = [\n    { value: 'upcoming', label: 'Upcoming' },\n    { value: 'active', label: 'Active' },\n    { value: 'sold', label: 'Sold Out' },\n    { value: 'inactive', label: 'Inactive' }\n  ]\n\n  const commonAmenities = [\n    'Swimming Pool', 'Gym', 'Parking', 'Security', 'Garden', 'Clubhouse',\n    'Playground', 'Elevator', 'Power Backup', 'Water Supply', 'CCTV',\n    'Intercom', 'Fire Safety', 'Waste Management'\n  ]\n\n  const handleInputChange = (field: string, value: string | boolean | number) => {\n    if (field.includes('.')) {\n      const [parent, child] = field.split('.')\n\n      // Handle nested objects like location and developer\n      if (parent === 'location' && (child === 'latitude' || child === 'longitude')) {\n        setFormData(prev => ({\n          ...prev,\n          location: {\n            ...prev.location,\n            coordinates: {\n              latitude: child === 'latitude' ? (typeof value === 'string' ? parseFloat(value) || 0 : value as number) : prev.location.coordinates?.latitude || 0,\n              longitude: child === 'longitude' ? (typeof value === 'string' ? parseFloat(value) || 0 : value as number) : prev.location.coordinates?.longitude || 0\n            }\n          }\n        }))\n      } else if (parent === 'developer') {\n        setFormData(prev => ({\n          ...prev,\n          developer: {\n            ...prev.developer,\n            [child]: value\n          }\n        }))\n      } else {\n        setFormData(prev => ({\n          ...prev,\n          [parent]: {\n            ...prev[parent as keyof typeof prev] as any,\n            [child]: value\n          }\n        }))\n      }\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [field]: value\n      }))\n    }\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }))\n    }\n  }\n\n  const addAmenity = (amenity: string) => {\n    if (amenity && !formData.amenities.includes(amenity)) {\n      setFormData(prev => ({\n        ...prev,\n        amenities: [...prev.amenities, amenity]\n      }))\n    }\n    setNewAmenity('')\n  }\n\n  const removeAmenity = (amenity: string) => {\n    setFormData(prev => ({\n      ...prev,\n      amenities: prev.amenities.filter(a => a !== amenity)\n    }))\n  }\n\n  // Remove feature functions as they're not in the model\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {}\n\n    if (!formData.name.trim()) newErrors.name = 'Property name is required'\n    if (!formData.description.trim()) newErrors.description = 'Description is required'\n    if (!formData.location.address.trim()) newErrors['location.address'] = 'Address is required'\n    if (!formData.location.city.trim()) newErrors['location.city'] = 'City is required'\n    if (!formData.launchDate) newErrors.launchDate = 'Launch date is required'\n    if (!formData.expectedCompletion) newErrors.expectedCompletion = 'Expected completion date is required'\n    if (!formData.ownerId) newErrors.ownerId = 'Property owner is required'\n    if (!formData.developer.name.trim()) newErrors['developer.name'] = 'Developer name is required'\n    if (!formData.developer.contact.trim()) newErrors['developer.contact'] = 'Developer contact is required'\n    if (!formData.developer.email.trim()) newErrors['developer.email'] = 'Developer email is required'\n    if (formData.expectedReturns <= 0) newErrors.expectedReturns = 'Expected returns must be greater than 0'\n    if (formData.maturityPeriodMonths <= 0) newErrors.maturityPeriodMonths = 'Maturity period must be greater than 0'\n\n    // Basic document validation\n    if (formData.documents.length === 0) {\n      newErrors.documents = 'At least one document is required'\n    }\n\n    // Image validation - at least one image should be provided\n    if (formData.images.length === 0) {\n      newErrors.images = 'Property images are required - please upload at least 1 image (max 10 images)'\n    } else if (formData.images.length > 10) {\n      newErrors.images = 'Maximum 10 images allowed'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleDocumentUpload = (documentType: string, uploadResults: Array<{ fileKey: string; publicUrl: string; fileName: string }>) => {\n    const documents = uploadResults.map(result => ({\n      key: result.fileKey,\n      name: result.fileName,\n      type: documentType,\n      url: result.publicUrl,\n      uploadedAt: new Date()\n    }))\n\n    setFormData(prev => ({\n      ...prev,\n      documents: [...prev.documents, ...documents]\n    }))\n\n    toast.success(`${uploadResults.length} document(s) uploaded successfully for ${documentType}`)\n  }\n\n  const handleImageUpload = (uploadResults: Array<{ fileKey: string; publicUrl: string; fileName: string }>) => {\n    const images = uploadResults.map((result) => ({\n      key: result.fileKey,\n      name: result.fileName,\n      type: 'image',\n      url: result.publicUrl,\n      uploadedAt: new Date()\n    }))\n\n    setFormData(prev => ({\n      ...prev,\n      images: [...prev.images, ...images]\n    }))\n\n    toast.success(`${uploadResults.length} image(s) uploaded successfully`)\n  }\n\n  const handleUploadError = (error: string) => {\n    toast.error(`Upload failed: ${error}`)\n  }\n\n\n\n  // Coordinate detection and address auto-fill\n  const detectCurrentLocation = async () => {\n    setIsDetectingLocation(true)\n    setLocationError(null)\n\n    try {\n      if (!navigator.geolocation) {\n        throw new Error('Geolocation is not supported by this browser')\n      }\n\n      const position = await new Promise<GeolocationPosition>((resolve, reject) => {\n        navigator.geolocation.getCurrentPosition(resolve, reject, {\n          enableHighAccuracy: true,\n          timeout: 10000,\n          maximumAge: 60000\n        })\n      })\n\n      const { latitude, longitude } = position.coords\n\n      // Update coordinates in form\n      setFormData(prev => ({\n        ...prev,\n        location: {\n          ...prev.location,\n          latitude: latitude.toString(),\n          longitude: longitude.toString()\n        }\n      }))\n\n      // Reverse geocoding to get address details\n      await reverseGeocode(latitude, longitude)\n\n      toast.success('Location detected successfully!')\n    } catch (error: any) {\n      const errorMessage = error.code === 1 ? 'Location access denied' :\n                          error.code === 2 ? 'Location unavailable' :\n                          error.code === 3 ? 'Location request timeout' :\n                          error.message || 'Failed to detect location'\n\n      setLocationError(errorMessage)\n      toast.error(errorMessage)\n    } finally {\n      setIsDetectingLocation(false)\n    }\n  }\n\n  // Reverse geocoding to get address from coordinates\n  const reverseGeocode = async (lat: number, lng: number) => {\n    try {\n      // Using a CORS-friendly geocoding service\n      const response = await fetch(\n        `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`\n      )\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch address details')\n      }\n\n      const data = await response.json()\n\n      if (data) {\n        setFormData(prev => ({\n          ...prev,\n          location: {\n            ...prev.location,\n            address: data.locality || data.city || data.countryName || prev.location.address,\n            city: data.city || data.locality || prev.location.city,\n            state: data.principalSubdivision || data.principalSubdivisionCode || prev.location.state,\n            pincode: data.postcode || data.postalCode || prev.location.pincode,\n            coordinates: {\n              latitude: lat,\n              longitude: lng\n            }\n          }\n        }))\n\n        toast.success('Address details filled automatically!')\n      }\n    } catch (error) {\n      console.error('Reverse geocoding error:', error)\n      // Fallback: Just fill coordinates and let user fill address manually\n      toast.success('Location coordinates detected! Please fill address details manually.')\n    }\n  }\n\n  const removeImage = (index: number) => {\n    setFormData(prev => ({\n      ...prev,\n      images: prev.images.filter((_, i) => i !== index)\n    }))\n  }\n\n  const removeDocument = (index: number) => {\n    setFormData(prev => ({\n      ...prev,\n      documents: prev.documents.filter((_, i) => i !== index)\n    }))\n  }\n\n  const setPrimaryImage = (index: number) => {\n    setFormData(prev => ({\n      ...prev,\n      images: prev.images.map((img, i) => ({\n        ...img,\n        isPrimary: i === index\n      }))\n    }))\n  }\n\n  const handlePreview = () => {\n    if (!validateForm()) {\n      toast.error('Please fill in all required fields before previewing')\n      return\n    }\n    // You can implement preview functionality here\n    toast.info('Preview functionality coming soon!')\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!validateForm()) return\n\n    setIsSubmitting(true)\n\n    try {\n      // Transform form data to match backend Property model\n      const propertyData = {\n        name: formData.name,\n        description: formData.description,\n        location: {\n          address: formData.location.address,\n          city: formData.location.city,\n          state: formData.location.state,\n          pincode: formData.location.pincode,\n          coordinates: formData.location.coordinates\n        },\n        propertyType: formData.propertyType.toUpperCase(),\n        expectedReturns: formData.expectedReturns,\n        maturityPeriodMonths: formData.maturityPeriodMonths,\n        constructionStatus: formData.constructionStatus.toUpperCase(),\n        launchDate: formData.launchDate,\n        expectedCompletion: formData.expectedCompletion,\n        actualCompletion: formData.actualCompletion,\n        developer: formData.developer,\n        amenities: formData.amenities,\n        images: formData.images,\n        documents: formData.documents,\n        videos: formData.videos,\n        legalDocuments: formData.legalDocuments,\n        ownerId: formData.ownerId,\n        status: formData.status.toUpperCase(),\n        featured: formData.featured,\n        priorityOrder: formData.priorityOrder\n      }\n\n      console.log('Creating property with data:', propertyData)\n\n      const result = await createProperty(propertyData).unwrap()\n\n      toast.success('Property created successfully!')\n      console.log('Property created:', result)\n\n      // Redirect to properties list or property detail page\n      router.push('/properties')\n\n    } catch (error: any) {\n      console.error('Error creating property:', error)\n      toast.error(error?.data?.message || 'Failed to create property')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n\n\n  return (\n    <DashboardLayout>\n      <div className=\"h-full overflow-auto\">\n        <div className=\"max-w-6xl mx-auto p-6 space-y-6\">\n        {/* Beautiful Header with Gradient */}\n        <div className=\"bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg p-6 text-white\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\n            <div className=\"flex items-center gap-4\">\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={() => window.history.back()}\n                className=\"bg-white/20 hover:bg-white/30 text-white border-white/30\"\n              >\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                Back to Properties\n              </Button>\n              <div>\n                <h1 className=\"text-3xl font-bold mb-2 flex items-center gap-3\">\n                  <Building className=\"h-8 w-8\" />\n                  Add New Property\n                </h1>\n                <p className=\"text-emerald-100\">Create a new property listing with complete details and documentation</p>\n              </div>\n            </div>\n            <div className=\"flex items-center gap-3\">\n              <Button\n                variant=\"secondary\"\n                onClick={handlePreview}\n                className=\"bg-white/20 hover:bg-white/30 text-white border-white/30\"\n              >\n                <Eye className=\"h-4 w-4 mr-2\" />\n                Preview\n              </Button>\n              <Button\n                onClick={handleSubmit}\n                disabled={isSubmitting || isCreating}\n                className=\"bg-yellow-500 hover:bg-yellow-600 text-black font-semibold\"\n              >\n                {(isSubmitting || isCreating) ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2\"></div>\n                    Creating...\n                  </>\n                ) : (\n                  <>\n                    <Save className=\"h-4 w-4 mr-2\" />\n                    Create Property\n                  </>\n                )}\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Form Content */}\n        {/* <div className=\"flex items-center gap-4\">\n          <Button variant=\"outline\" size=\"sm\">\n            <ArrowLeft className=\"h-4 w-4 mr-2\" />\n            Back to Properties\n          </Button>\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 flex items-center gap-3\">\n              <Building className=\"h-8 w-8 text-blue-600\" />\n              Add New Property\n            </h1>\n            <p className=\"text-gray-600 mt-1\">Create a new property listing for investment</p>\n          </div>\n        </div> */}\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* Basic Information */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Building className=\"h-5 w-5\" />\n                Basic Information\n              </CardTitle>\n              <CardDescription>Enter the property's basic details</CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Property Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.name}\n                    onChange={(e) => handleInputChange('name', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                      errors.name ? 'border-red-500' : 'border-gray-300'\n                    }`}\n                    placeholder=\"Enter property name\"\n                  />\n                  {errors.name && (\n                    <p className=\"text-red-500 text-sm mt-1 flex items-center gap-1\">\n                      <AlertCircle className=\"h-4 w-4\" />\n                      {errors.name}\n                    </p>\n                  )}\n                </div>\n\n                {/* Developer Information */}\n                <div className=\"space-y-4 p-4 border border-gray-200 rounded-lg\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Developer Information *</h3>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        Developer Name *\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={formData.developer.name}\n                        onChange={(e) => handleInputChange('developer.name', e.target.value)}\n                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                          errors['developer.name'] ? 'border-red-500' : 'border-gray-300'\n                        }`}\n                        placeholder=\"Enter developer name\"\n                      />\n                      {errors['developer.name'] && (\n                        <p className=\"text-red-500 text-sm mt-1\">{errors['developer.name']}</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        Contact Number *\n                      </label>\n                      <input\n                        type=\"tel\"\n                        value={formData.developer.contact}\n                        onChange={(e) => handleInputChange('developer.contact', e.target.value)}\n                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                          errors['developer.contact'] ? 'border-red-500' : 'border-gray-300'\n                        }`}\n                        placeholder=\"Enter contact number\"\n                      />\n                      {errors['developer.contact'] && (\n                        <p className=\"text-red-500 text-sm mt-1\">{errors['developer.contact']}</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        Email Address *\n                      </label>\n                      <input\n                        type=\"email\"\n                        value={formData.developer.email}\n                        onChange={(e) => handleInputChange('developer.email', e.target.value)}\n                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                          errors['developer.email'] ? 'border-red-500' : 'border-gray-300'\n                        }`}\n                        placeholder=\"Enter email address\"\n                      />\n                      {errors['developer.email'] && (\n                        <p className=\"text-red-500 text-sm mt-1\">{errors['developer.email']}</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        Experience (Years)\n                      </label>\n                      <input\n                        type=\"number\"\n                        min=\"0\"\n                        value={formData.developer.experience || ''}\n                        onChange={(e) => handleInputChange('developer.experience', parseInt(e.target.value) || 0)}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        placeholder=\"Years of experience\"\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Property Owner (Optional)\n                  </label>\n                  <div className=\"space-y-2 owner-search-container\">\n                    <div className=\"relative\">\n                      <input\n                        type=\"text\"\n                        value={ownerSearchQuery}\n                        onChange={(e) => {\n                          setOwnerSearchQuery(e.target.value)\n                          setHighlightedIndex(-1)\n                        }}\n                        onFocus={() => setShowOwnerDropdown(true)}\n                        onKeyDown={handleKeyDown}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        placeholder=\"Search for property owner...\"\n                        autoComplete=\"off\"\n                      />\n\n                      {showOwnerDropdown && (\n                        <div className=\"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto\">\n                          {filteredOwners.length > 0 ? (\n                            <>\n                              <div className=\"px-3 py-2 text-xs text-gray-500 bg-gray-50 border-b\">\n                                {filteredOwners.length} owner(s) found\n                              </div>\n                              {filteredOwners.map((owner, index) => (\n                                <div\n                                  key={owner.id}\n                                  onClick={() => selectOwner(owner)}\n                                  className={`px-3 py-2 cursor-pointer border-b border-gray-100 last:border-b-0 ${\n                                    index === highlightedIndex\n                                      ? 'bg-blue-100 text-blue-900'\n                                      : 'hover:bg-blue-50'\n                                  }`}\n                                >\n                                  <div className=\"flex items-center justify-between\">\n                                    <div>\n                                      <p className=\"font-medium text-gray-900\">{owner.name}</p>\n                                      <p className=\"text-sm text-gray-600\">{owner.email}</p>\n                                      {owner.company && (\n                                        <p className=\"text-xs text-gray-500\">{owner.company}</p>\n                                      )}\n                                    </div>\n                                    <div className=\"text-right\">\n                                      <span className={`inline-block px-2 py-1 text-xs rounded-full ${\n                                        owner.status === 'verified'\n                                          ? 'bg-green-100 text-green-800'\n                                          : 'bg-yellow-100 text-yellow-800'\n                                      }`}>\n                                        {owner.status}\n                                      </span>\n                                      {owner.properties && (\n                                        <p className=\"text-xs text-gray-500 mt-1\">\n                                          {owner.properties} properties\n                                        </p>\n                                      )}\n                                    </div>\n                                  </div>\n                                </div>\n                              ))}\n                            </>\n                          ) : ownerSearchQuery ? (\n                            <div className=\"px-3 py-4 text-center text-gray-500\">\n                              <p>No owners found matching \"{ownerSearchQuery}\"</p>\n                              <button\n                                type=\"button\"\n                                onClick={() => {\n                                  setShowOwnerDropdown(false)\n                                  // You could open a \"Create New Owner\" modal here\n                                }}\n                                className=\"mt-2 text-blue-600 hover:text-blue-800 text-sm\"\n                              >\n                                Create new owner\n                              </button>\n                            </div>\n                          ) : (\n                            <div className=\"px-3 py-4 text-center text-gray-500\">\n                              <p>Start typing to search for owners</p>\n                            </div>\n                          )}\n                        </div>\n                      )}\n                    </div>\n\n                    {selectedOwner && (\n                      <div className=\"flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n                        <div className=\"flex items-center gap-3\">\n                          <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                            <span className=\"text-blue-600 font-semibold\">\n                              {selectedOwner.name.charAt(0).toUpperCase()}\n                            </span>\n                          </div>\n                          <div>\n                            <p className=\"font-medium text-gray-900\">{selectedOwner.name}</p>\n                            <p className=\"text-sm text-gray-600\">{selectedOwner.email}</p>\n                            {selectedOwner.company && (\n                              <p className=\"text-xs text-gray-500\">{selectedOwner.company}</p>\n                            )}\n                          </div>\n                        </div>\n                        <button\n                          type=\"button\"\n                          onClick={clearSelectedOwner}\n                          className=\"text-gray-400 hover:text-gray-600\"\n                        >\n                          <X className=\"h-5 w-5\" />\n                        </button>\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"flex items-center justify-between mt-2\">\n                    <p className=\"text-gray-500 text-xs\">\n                      You can assign an owner now or later from the property management page\n                    </p>\n                    <div className=\"flex items-center gap-2\">\n                      {isLoadingOwners && (\n                        <Loader2 className=\"h-3 w-3 animate-spin text-blue-600\" />\n                      )}\n                      <div className=\"text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded\">\n                        💡 {availableOwners.length} owners available\n                       \n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Description *\n                </label>\n                <textarea\n                  value={formData.description}\n                  onChange={(e) => handleInputChange('description', e.target.value)}\n                  rows={3}\n                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                    errors.description ? 'border-red-500' : 'border-gray-300'\n                  }`}\n                  placeholder=\"Enter property description\"\n                />\n                {errors.description && (\n                  <p className=\"text-red-500 text-sm mt-1 flex items-center gap-1\">\n                    <AlertCircle className=\"h-4 w-4\" />\n                    {errors.description}\n                  </p>\n                )}\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                  <Label className=\"text-sm font-medium text-gray-700\">\n                    Property Type *\n                  </Label>\n                  <Select\n                    value={formData.propertyType}\n                    onValueChange={(value) => handleInputChange('propertyType', value)}\n                  >\n                    <SelectTrigger className=\"mt-1\">\n                      <SelectValue placeholder=\"Select property type\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {propertyTypes.map((type) => (\n                        <SelectItem key={type.value} value={type.value}>\n                          <div className=\"flex items-center gap-2\">\n                            <type.icon className=\"h-4 w-4\" />\n                            <div>\n                              <div className=\"font-medium\">{type.label}</div>\n                              <div className=\"text-xs text-gray-500\">{type.description}</div>\n                            </div>\n                          </div>\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  {errors.propertyType && (\n                    <p className=\"text-red-500 text-sm mt-1 flex items-center gap-1\">\n                      <AlertCircle className=\"h-4 w-4\" />\n                      {errors.propertyType}\n                    </p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Expected Returns (%) *\n                  </label>\n                  <input\n                    type=\"number\"\n                    min=\"0\"\n                    max=\"100\"\n                    step=\"0.1\"\n                    value={formData.expectedReturns}\n                    onChange={(e) => handleInputChange('expectedReturns', parseFloat(e.target.value) || 0)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"e.g., 12.5\"\n                  />\n                  {errors.expectedReturns && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.expectedReturns}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Maturity Period (Months) *\n                  </label>\n                  <input\n                    type=\"number\"\n                    min=\"1\"\n                    value={formData.maturityPeriodMonths}\n                    onChange={(e) => handleInputChange('maturityPeriodMonths', parseInt(e.target.value) || 0)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"e.g., 24\"\n                  />\n                  {errors.maturityPeriodMonths && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.maturityPeriodMonths}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Construction Status *\n                  </label>\n                  <select\n                    value={formData.constructionStatus}\n                    onChange={(e) => handleInputChange('constructionStatus', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"planning\">Planning</option>\n                    <option value=\"under_construction\">Under Construction</option>\n                    <option value=\"completed\">Completed</option>\n                    <option value=\"ready_to_move\">Ready to Move</option>\n                  </select>\n                  {errors.constructionStatus && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.constructionStatus}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Status\n                  </label>\n                  <select\n                    value={formData.status}\n                    onChange={(e) => handleInputChange('status', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    {statusOptions.map((status) => (\n                      <option key={status.value} value={status.value}>\n                        {status.label}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"flex items-center gap-2\">\n                <input\n                  type=\"checkbox\"\n                  id=\"featured\"\n                  checked={formData.featured}\n                  onChange={(e) => handleInputChange('featured', e.target.checked)}\n                  className=\"rounded border-gray-300\"\n                />\n                <label htmlFor=\"featured\" className=\"text-sm font-medium text-gray-700 flex items-center gap-1\">\n                  <Star className=\"h-4 w-4 text-yellow-500\" />\n                  Mark as Featured Property\n                </label>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Location Details */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <MapPin className=\"h-5 w-5\" />\n                Location Details\n              </CardTitle>\n              <CardDescription>Enter the property's location information</CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              {/* Coordinates Detection - Top Priority */}\n              <div>\n                <div className=\"flex items-center justify-between mb-3\">\n                  <h4 className=\"text-sm font-semibold text-gray-900\">📍 Smart Location Detection</h4>\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={detectCurrentLocation}\n                    disabled={isDetectingLocation}\n                    className=\"flex items-center gap-2\"\n                  >\n                    {isDetectingLocation ? (\n                      <>\n                        <Loader2 className=\"h-4 w-4 animate-spin\" />\n                        Detecting...\n                      </>\n                    ) : (\n                      <>\n                        <Navigation className=\"h-4 w-4\" />\n                        Auto-Detect Location\n                      </>\n                    )}\n                  </Button>\n                </div>\n\n                {locationError && (\n                  <div className=\"bg-red-50 border border-red-200 rounded-lg p-3 mb-3\">\n                    <p className=\"text-red-800 text-sm\">\n                      <AlertCircle className=\"h-4 w-4 inline mr-1\" />\n                      {locationError}\n                    </p>\n                  </div>\n                )}\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <Label className=\"text-sm font-medium text-gray-700\">\n                      Latitude\n                    </Label>\n                    <Input\n                      type=\"number\"\n                      step=\"any\"\n                      value={formData.location.coordinates?.latitude || ''}\n                      onChange={(e) => handleInputChange('location.latitude', e.target.value)}\n                      placeholder=\"e.g., 28.6139\"\n                      className=\"mt-1\"\n                    />\n                    <p className=\"text-xs text-gray-500 mt-1\">Decimal degrees format</p>\n                  </div>\n\n                  <div>\n                    <Label className=\"text-sm font-medium text-gray-700\">\n                      Longitude\n                    </Label>\n                    <Input\n                      type=\"number\"\n                      step=\"any\"\n                      value={formData.location.coordinates?.longitude || ''}\n                      onChange={(e) => handleInputChange('location.longitude', e.target.value)}\n                      placeholder=\"e.g., 77.2090\"\n                      className=\"mt-1\"\n                    />\n                    <p className=\"text-xs text-gray-500 mt-1\">Decimal degrees format</p>\n                  </div>\n                </div>\n\n                <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-3 mt-3\">\n                  <p className=\"text-blue-800 text-sm\">\n                    <strong>💡 Smart Detection:</strong>\n                    Click \"Auto-Detect Location\" to automatically fill coordinates and all address details below\n                  </p>\n                </div>\n              </div>\n\n              {/* Address Information */}\n              <div>\n                <h4 className=\"text-sm font-semibold text-gray-900 mb-3\">📍 Address Details</h4>\n                <div>\n                  <Label className=\"text-sm font-medium text-gray-700\">\n                    Full Address *\n                  </Label>\n                  <Input\n                    type=\"text\"\n                    value={formData.location.address}\n                    onChange={(e) => handleInputChange('location.address', e.target.value)}\n                    className={`mt-1 ${errors['location.address'] ? 'border-red-500' : ''}`}\n                    placeholder=\"Enter complete address\"\n                  />\n                  {errors['location.address'] && (\n                    <p className=\"text-red-500 text-sm mt-1 flex items-center gap-1\">\n                      <AlertCircle className=\"h-4 w-4\" />\n                      {errors['location.address']}\n                    </p>\n                  )}\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                  <div>\n                    <Label className=\"text-sm font-medium text-gray-700\">\n                      City *\n                    </Label>\n                    <Input\n                      type=\"text\"\n                      value={formData.location.city}\n                      onChange={(e) => handleInputChange('location.city', e.target.value)}\n                      className={`mt-1 ${errors['location.city'] ? 'border-red-500' : ''}`}\n                      placeholder=\"Auto-filled from location\"\n                    />\n                    {errors['location.city'] && (\n                      <p className=\"text-red-500 text-sm mt-1 flex items-center gap-1\">\n                        <AlertCircle className=\"h-4 w-4\" />\n                        {errors['location.city']}\n                      </p>\n                    )}\n                  </div>\n\n                  <div>\n                    <Label className=\"text-sm font-medium text-gray-700\">\n                      State\n                    </Label>\n                    <Input\n                      type=\"text\"\n                      value={formData.location.state}\n                      onChange={(e) => handleInputChange('location.state', e.target.value)}\n                      className=\"mt-1\"\n                      placeholder=\"Auto-filled from location\"\n                    />\n                  </div>\n\n\n\n                  <div>\n                    <Label className=\"text-sm font-medium text-gray-700\">\n                      Pincode\n                    </Label>\n                    <Input\n                      type=\"text\"\n                      value={formData.location.pincode}\n                      onChange={(e) => handleInputChange('location.pincode', e.target.value)}\n                      className=\"mt-1\"\n                      placeholder=\"Auto-filled from location\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n\n            </CardContent>\n          </Card>\n\n\n\n\n\n          {/* Timeline */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Calendar className=\"h-5 w-5\" />\n                Project Timeline\n              </CardTitle>\n              <CardDescription>Set important project dates</CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Launch Date *\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={formData.launchDate}\n                    onChange={(e) => handleInputChange('launchDate', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                      errors.launchDate ? 'border-red-500' : 'border-gray-300'\n                    }`}\n                  />\n                  {errors.launchDate && (\n                    <p className=\"text-red-500 text-sm mt-1 flex items-center gap-1\">\n                      <AlertCircle className=\"h-4 w-4\" />\n                      {errors.launchDate}\n                    </p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Expected Completion Date *\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={formData.expectedCompletion}\n                    onChange={(e) => handleInputChange('expectedCompletion', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                      errors.expectedCompletion ? 'border-red-500' : 'border-gray-300'\n                    }`}\n                  />\n                  {errors.expectedCompletion && (\n                    <p className=\"text-red-500 text-sm mt-1 flex items-center gap-1\">\n                      <AlertCircle className=\"h-4 w-4\" />\n                      {errors.expectedCompletion}\n                    </p>\n                  )}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Amenities */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Amenities & Features</CardTitle>\n              <CardDescription>Add property amenities and special features</CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Common Amenities\n                </label>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-2\">\n                  {commonAmenities.map((amenity) => (\n                    <Button\n                      key={amenity}\n                      type=\"button\"\n                      variant={formData.amenities.includes(amenity) ? \"default\" : \"outline\"}\n                      size=\"sm\"\n                      onClick={() => \n                        formData.amenities.includes(amenity) \n                          ? removeAmenity(amenity)\n                          : addAmenity(amenity)\n                      }\n                      className=\"justify-start\"\n                    >\n                      {amenity}\n                    </Button>\n                  ))}\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Custom Amenity\n                </label>\n                <div className=\"flex gap-2\">\n                  <input\n                    type=\"text\"\n                    value={newAmenity}\n                    onChange={(e) => setNewAmenity(e.target.value)}\n                    className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"Enter custom amenity\"\n                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAmenity(newAmenity))}\n                  />\n                  <Button type=\"button\" onClick={() => addAmenity(newAmenity)}>\n                    <Plus className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              </div>\n\n              {formData.amenities.length > 0 && (\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Selected Amenities\n                  </label>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {formData.amenities.map((amenity) => (\n                      <Badge key={amenity} variant=\"secondary\" className=\"flex items-center gap-1\">\n                        {amenity}\n                        <button\n                          type=\"button\"\n                          onClick={() => removeAmenity(amenity)}\n                          className=\"ml-1 hover:text-red-600\"\n                        >\n                          <X className=\"h-3 w-3\" />\n                        </button>\n                      </Badge>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Property Images */}\n          <Card className=\"border-2 border-blue-200 bg-blue-50/30\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2 text-blue-900\">\n                <Image className=\"h-5 w-5\" />\n                Property Images *\n              </CardTitle>\n              <CardDescription className=\"text-blue-700\">\n                Upload high-quality images of the property. \n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              {errors.images && (\n                <div className=\"bg-red-50 border border-red-200 rounded-lg p-3\">\n                  <p className=\"text-red-600 text-sm flex items-center gap-1\">\n                    <AlertCircle className=\"h-4 w-4\" />\n                    {errors.images}\n                  </p>\n                </div>\n              )}\n\n              <EnhancedS3Upload\n                uploadType=\"property-image\"\n                onUploadComplete={handleImageUpload}\n                onUploadError={handleUploadError}\n                maxFiles={10}\n                allowMultiple={true}\n                showPreview={true}\n                className=\"border-2 border-dashed border-blue-300 rounded-lg\"\n              />\n\n            \n\n              {/* Image Management */}\n              {formData.images.length > 0 && (\n                <div className=\"space-y-4\">\n                  <h4 className=\"text-sm font-semibold text-gray-900\">Image Gallery ({formData.images.length})</h4>\n                  <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n                    {formData.images.map((image, index) => (\n                      <div key={index} className=\"relative group\">\n                        <div className=\"aspect-square rounded-lg overflow-hidden bg-gray-100\">\n                          <img\n                            src={image.url}\n                            alt={image.name}\n                            className=\"w-full h-full object-cover\"\n                          />\n                        </div>\n\n                        {/* Image Controls */}\n                        <div className=\"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center space-x-2\">\n                          <Button\n                            type=\"button\"\n                            variant=\"secondary\"\n                            size=\"sm\"\n                            onClick={() => setPrimaryImage(index)}\n                            className={image.isPrimary ? 'bg-blue-600 text-white' : ''}\n                          >\n                            <Star className=\"h-4 w-4\" />\n                          </Button>\n                          <Button\n                            type=\"button\"\n                            variant=\"secondary\"\n                            size=\"sm\"\n                            onClick={() => window.open(image.url, '_blank')}\n                          >\n                            <Eye className=\"h-4 w-4\" />\n                          </Button>\n                          <Button\n                            type=\"button\"\n                            variant=\"destructive\"\n                            size=\"sm\"\n                            onClick={() => removeImage(index)}\n                          >\n                            <X className=\"h-4 w-4\" />\n                          </Button>\n                        </div>\n\n                        {/* Primary Badge */}\n                        {image.isPrimary && (\n                          <div className=\"absolute top-2 left-2\">\n                            <Badge className=\"bg-blue-600 text-white text-xs\">\n                              Primary\n                            </Badge>\n                          </div>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n\n                  <div className=\"text-sm text-gray-600\">\n                    <p>• Click the star icon to set an image as primary</p>\n                    <p>• Primary image will be displayed as the main property image</p>\n                    <p>• You can upload up to 10 images, max 5MB each</p>\n                    <p>• Images are securely stored in AWS S3 cloud storage</p>\n                  </div>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Upload Summary */}\n          {(formData.images.length > 0 || formData.documents.length > 0) && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                  Upload Summary\n                </CardTitle>\n                <CardDescription>\n                  Overview of all uploaded files for this property\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  {/* Images Summary */}\n                  {formData.images.length > 0 && (\n                    <div className=\"space-y-3\">\n                      <h4 className=\"text-sm font-semibold text-gray-900 flex items-center gap-2\">\n                        <Image className=\"h-4 w-4\" />\n                        Images ({formData.images.length})\n                      </h4>\n                      <div className=\"space-y-2\">\n                        {formData.images.slice(0, 3).map((image, index) => (\n                          <div key={index} className=\"flex items-center gap-3 p-2 bg-gray-50 rounded\">\n                            <div className=\"w-8 h-8 rounded overflow-hidden bg-gray-100\">\n                              <img\n                                src={image.url}\n                                alt={image.name}\n                                className=\"w-full h-full object-cover\"\n                              />\n                            </div>\n                            <div className=\"flex-1 min-w-0\">\n                              <p className=\"text-xs font-medium text-gray-900 truncate\">{image.name}</p>\n                              {image.isPrimary && (\n                                <span className=\"text-xs text-blue-600\">Primary Image</span>\n                              )}\n                            </div>\n                          </div>\n                        ))}\n                        {formData.images.length > 3 && (\n                          <p className=\"text-xs text-gray-500\">\n                            +{formData.images.length - 3} more images\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Documents Summary */}\n                  {formData.documents.length > 0 && (\n                    <div className=\"space-y-3\">\n                      <h4 className=\"text-sm font-semibold text-gray-900 flex items-center gap-2\">\n                        <FileText className=\"h-4 w-4\" />\n                        Documents ({formData.documents.length})\n                      </h4>\n                      <div className=\"space-y-2\">\n                        {formData.documents.slice(0, 3).map((doc, index) => (\n                          <div key={index} className=\"flex items-center gap-2 p-1 text-xs text-gray-600\">\n                            <div className=\"w-4 h-4 bg-blue-100 rounded flex items-center justify-center\">\n                              <span className=\"text-blue-600 text-[8px] font-bold\">\n                                {doc.type.toUpperCase()}\n                              </span>\n                            </div>\n                            <span className=\"truncate\">{doc.name}</span>\n                          </div>\n                        ))}\n                        {formData.documents.length > 3 && (\n                          <p className=\"text-xs text-gray-400 ml-6\">+{formData.documents.length - 3} more</p>\n                        )}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Property Documents */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Upload className=\"h-5 w-5\" />\n                Property Documents\n              </CardTitle>\n              <CardDescription>\n                Upload required legal and regulatory documents. At least one key document is required.\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              {errors.documents && (\n                <div className=\"bg-red-50 border border-red-200 rounded-lg p-3\">\n                  <p className=\"text-red-600 text-sm flex items-center gap-1\">\n                    <AlertCircle className=\"h-4 w-4\" />\n                    {errors.documents}\n                  </p>\n                </div>\n              )}\n\n              {/* Key Documents */}\n              <div>\n                <h4 className=\"text-sm font-semibold text-gray-900 mb-3\">Key Documents (At least one required)</h4>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Property Title Document\n                    </label>\n                    <EnhancedS3Upload\n                      uploadType=\"property-document\"\n                      onUploadComplete={(results) => handleDocumentUpload('propertyTitle', results)}\n                      onUploadError={handleUploadError}\n                      maxFiles={5}\n                      allowMultiple={true}\n                      className=\"border border-gray-300 rounded-lg\"\n                    />\n                    <p className=\"text-xs text-gray-500 mt-1\">Title deed, ownership certificate</p>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Approval Certificate\n                    </label>\n                    <EnhancedS3Upload\n                      uploadType=\"property-document\"\n                      onUploadComplete={(results) => handleDocumentUpload('approvalCertificate', results)}\n                      onUploadError={handleUploadError}\n                      maxFiles={5}\n                      allowMultiple={true}\n                      className=\"border border-gray-300 rounded-lg\"\n                    />\n                    <p className=\"text-xs text-gray-500 mt-1\">Government approval, NOC</p>\n                  </div>\n\n                  <PropertyDocumentUpload\n                    documentType=\"landDocuments\"\n                    label=\"Land Documents\"\n                    description=\"Land records, survey documents\"\n                    maxFiles={5}\n                    onUploadComplete={(results) => handleDocumentUpload('landDocuments', results)}\n                    onUploadError={handleUploadError}\n                    existingFiles={formData.documents.landDocuments || []}\n                  />\n\n                  <PropertyDocumentUpload\n                    documentType=\"constructionPermit\"\n                    label=\"Construction Permit\"\n                    description=\"Building permit, construction license\"\n                    maxFiles={5}\n                    onUploadComplete={(results) => handleDocumentUpload('constructionPermit', results)}\n                    onUploadError={handleUploadError}\n                    existingFiles={formData.documents.constructionPermit || []}\n                  />\n                </div>\n              </div>\n\n              {/* Additional Documents */}\n              <div>\n                <h4 className=\"text-sm font-semibold text-gray-900 mb-3\">Additional Documents (Optional)</h4>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <PropertyDocumentUpload\n                    documentType=\"environmentalClearance\"\n                    label=\"Environmental Clearance\"\n                    description=\"Environmental impact assessment\"\n                    maxFiles={3}\n                    onUploadComplete={(results) => handleDocumentUpload('environmentalClearance', results)}\n                    onUploadError={handleUploadError}\n                    existingFiles={formData.documents.environmentalClearance || []}\n                  />\n\n                  <PropertyDocumentUpload\n                    documentType=\"financialProjections\"\n                    label=\"Financial Projections\"\n                    description=\"ROI calculations, financial analysis\"\n                    maxFiles={3}\n                    onUploadComplete={(results) => handleDocumentUpload('financialProjections', results)}\n                    onUploadError={handleUploadError}\n                    existingFiles={formData.documents.financialProjections || []}\n                  />\n\n                  <PropertyDocumentUpload\n                    documentType=\"legalDocuments\"\n                    label=\"Legal Documents\"\n                    description=\"Legal agreements, contracts\"\n                    maxFiles={3}\n                    onUploadComplete={(results) => handleDocumentUpload('legalDocuments', results)}\n                    onUploadError={handleUploadError}\n                    existingFiles={formData.documents.legalDocuments || []}\n                  />\n\n                  <PropertyDocumentUpload\n                    documentType=\"insuranceDocuments\"\n                    label=\"Insurance Documents\"\n                    description=\"Property insurance, liability coverage\"\n                    maxFiles={3}\n                    onUploadComplete={(results) => handleDocumentUpload('insuranceDocuments', results)}\n                    onUploadError={handleUploadError}\n                    existingFiles={formData.documents.insuranceDocuments || []}\n                  />\n                </div>\n              </div>\n\n            </CardContent>\n          </Card>\n\n          {/* Submit Buttons */}\n          <div className=\"flex items-center justify-end gap-4\">\n            <Button type=\"button\" variant=\"outline\">\n              Save as Draft\n            </Button>\n            <Button type=\"submit\" disabled={isSubmitting}>\n              {isSubmitting ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Creating Property...\n                </>\n              ) : (\n                <>\n                  <Save className=\"h-4 w-4 mr-2\" />\n                  Create Property\n                </>\n              )}\n            </Button>\n          </div>\n        </form>\n        </div>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAlBA;;;;;;;;;;;;;;;;;AAgHe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,gBAAgB,EAAE,WAAW,UAAU,EAAE,CAAC,GAAG,CAAA,GAAA,oIAAA,CAAA,4BAAyB,AAAD;IAC5E,MAAM,EAAE,MAAM,kBAAkB,EAAE,WAAW,eAAe,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,4BAAyB,AAAD,EAAE;QACzF,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IAEA,6BAA6B;IAC7B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,MAAM;QACN,aAAa;QACb,cAAc;QACd,UAAU;YACR,SAAS;YACT,MAAM;YACN,OAAO;YACP,SAAS;YACT,aAAa;gBACX,UAAU;gBACV,WAAW;YACb;QACF;QACA,iBAAiB;QACjB,sBAAsB;QACtB,oBAAoB;QACpB,YAAY;QACZ,oBAAoB;QACpB,kBAAkB;QAClB,WAAW;YACT,MAAM;YACN,SAAS;YACT,OAAO;YACP,YAAY;QACd;QACA,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB,EAAE;QAClB,SAAS;QACT,QAAQ;QACR,UAAU;QACV,eAAe;IACjB;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAI9D,wBAAwB;IACxB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAO;IACxD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAE1D,oCAAoC;IACpC,MAAM,cAAc;QAClB;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;KACD;IAED,oBAAoB;IACpB,MAAM,aAAa,oBAAoB,MAAM,QAAQ,EAAE;IAEvD,qEAAqE;IACrE,MAAM,kBAAkB,WAAW,MAAM,GAAG,IAAI,WAAW,GAAG,CAAC,CAAA,QAAS,CAAC;YACvE,IAAI,MAAM,GAAG,IAAI,MAAM,EAAE;YACzB,MAAM,MAAM,QAAQ,IAAI,GAAG,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,QAAQ,EAAE;YAC9D,OAAO,MAAM,KAAK;YAClB,SAAS,MAAM,OAAO,IAAI;YAC1B,QAAQ,MAAM,kBAAkB,IAAI;YACpC,YAAY,MAAM,eAAe,IAAI;YACrC,OAAO,MAAM,KAAK;QACpB,CAAC,KAAK;IAEN,sCAAsC;IACtC,MAAM,iBAAiB,gBAAgB,MAAM,CAAC,CAAA;QAC5C,IAAI,CAAC,kBAAkB,OAAO;QAE9B,MAAM,cAAc,iBAAiB,WAAW;QAChD,OACE,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAClC,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAClC,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACvD,MAAM,KAAK,CAAC,QAAQ,CAAC;IAEzB;IAEA,4BAA4B;IAC5B,MAAM,cAAc,CAAC;QACnB,iBAAiB;QACjB,oBAAoB,MAAM,IAAI;QAC9B,qBAAqB;QACrB,kBAAkB,WAAW,MAAM,EAAE;IACvC;IAEA,MAAM,qBAAqB;QACzB,iBAAiB;QACjB,oBAAoB;QACpB,oBAAoB,CAAC;QACrB,kBAAkB,WAAW;IAC/B;IAEA,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,mBAAmB;QAExB,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,EAAE,cAAc;gBAChB,oBAAoB,CAAA,OAClB,OAAO,eAAe,MAAM,GAAG,IAAI,OAAO,IAAI;gBAEhD;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,oBAAoB,CAAA,OAClB,OAAO,IAAI,OAAO,IAAI,eAAe,MAAM,GAAG;gBAEhD;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,oBAAoB,KAAK,cAAc,CAAC,iBAAiB,EAAE;oBAC7D,YAAY,cAAc,CAAC,iBAAiB;gBAC9C;gBACA;YACF,KAAK;gBACH,qBAAqB;gBACrB,oBAAoB,CAAC;gBACrB;QACJ;IACF;IAEA,uCAAuC;IACvC,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,MAAM,SAAS,MAAM,MAAM;YAC3B,IAAI,CAAC,OAAO,OAAO,CAAC,4BAA4B;gBAC9C,qBAAqB;YACvB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAe,OAAO;YAAe,MAAM,uRAAA,CAAA,OAAI;YAAE,aAAa;QAA6B;QACpG;YAAE,OAAO;YAAc,OAAO;YAAc,MAAM,oSAAA,CAAA,YAAS;YAAE,aAAa;QAA8B;QACxG;YAAE,OAAO;YAAS,OAAO;YAAa,MAAM,8RAAA,CAAA,WAAQ;YAAE,aAAa;QAA2B;QAC9F;YAAE,OAAO;YAAU,OAAO;YAAU,MAAM,sRAAA,CAAA,OAAI;YAAE,aAAa;QAA8B;QAC3F;YAAE,OAAO;YAAgB,OAAO;YAAgB,MAAM,uRAAA,CAAA,OAAI;YAAE,aAAa;QAA8B;KACxG;IAED,wCAAwC;IACxC,MAAM,uBAAuB;QAC3B;YAAE,OAAO;YAAY,OAAO;YAAY,aAAa;QAAoB;QACzE;YAAE,OAAO;YAAsB,OAAO;YAAsB,aAAa;QAAwB;QACjG;YAAE,OAAO;YAAa,OAAO;YAAa,aAAa;QAAwB;QAC/E;YAAE,OAAO;YAAiB,OAAO;YAAiB,aAAa;QAAsB;KACtF;IAED,oCAAoC;IACpC,MAAM,mBAAmB;QACvB;YAAE,OAAO;YAAU,OAAO;YAAU,aAAa;QAA2B;QAC5E;YAAE,OAAO;YAAY,OAAO;YAAY,aAAa;QAAgB;QACrE;YAAE,OAAO;YAAY,OAAO;YAAY,aAAa;QAAkB;QACvE;YAAE,OAAO;YAAa,OAAO;YAAa,aAAa;QAAuB;KAC/E;IAED,MAAM,aAAa;QACjB;YAAE,OAAO;YAAU,OAAO;YAAU,aAAa;QAA8B;QAC/E;YAAE,OAAO;YAAW,OAAO;YAAW,aAAa;QAA6B;QAChF;YAAE,OAAO;YAAa,OAAO;YAAa,aAAa;QAAgC;QACvF;YAAE,OAAO;YAAU,OAAO;YAAU,aAAa;QAA6B;KAC/E;IAED,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAU,OAAO;QAAS;QACnC;YAAE,OAAO;YAAQ,OAAO;QAAW;QACnC;YAAE,OAAO;YAAY,OAAO;QAAW;KACxC;IAED,MAAM,kBAAkB;QACtB;QAAiB;QAAO;QAAW;QAAY;QAAU;QACzD;QAAc;QAAY;QAAgB;QAAgB;QAC1D;QAAY;QAAe;KAC5B;IAED,MAAM,oBAAoB,CAAC,OAAe;QACxC,IAAI,MAAM,QAAQ,CAAC,MAAM;YACvB,MAAM,CAAC,QAAQ,MAAM,GAAG,MAAM,KAAK,CAAC;YAEpC,oDAAoD;YACpD,IAAI,WAAW,cAAc,CAAC,UAAU,cAAc,UAAU,WAAW,GAAG;gBAC5E,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,UAAU;4BACR,GAAG,KAAK,QAAQ;4BAChB,aAAa;gCACX,UAAU,UAAU,aAAc,OAAO,UAAU,WAAW,WAAW,UAAU,IAAI,QAAmB,KAAK,QAAQ,CAAC,WAAW,EAAE,YAAY;gCACjJ,WAAW,UAAU,cAAe,OAAO,UAAU,WAAW,WAAW,UAAU,IAAI,QAAmB,KAAK,QAAQ,CAAC,WAAW,EAAE,aAAa;4BACtJ;wBACF;oBACF,CAAC;YACH,OAAO,IAAI,WAAW,aAAa;gBACjC,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,WAAW;4BACT,GAAG,KAAK,SAAS;4BACjB,CAAC,MAAM,EAAE;wBACX;oBACF,CAAC;YACH,OAAO;gBACL,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,CAAC,OAAO,EAAE;4BACR,GAAG,IAAI,CAAC,OAA4B;4BACpC,CAAC,MAAM,EAAE;wBACX;oBACF,CAAC;YACH;QACF,OAAO;YACL,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE;gBACX,CAAC;QACH;QAEA,sCAAsC;QACtC,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE;gBACX,CAAC;QACH;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,WAAW,CAAC,SAAS,SAAS,CAAC,QAAQ,CAAC,UAAU;YACpD,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,WAAW;2BAAI,KAAK,SAAS;wBAAE;qBAAQ;gBACzC,CAAC;QACH;QACA,cAAc;IAChB;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,WAAW,KAAK,SAAS,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;YAC9C,CAAC;IACH;IAEA,uDAAuD;IAEvD,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;QAC5C,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI,UAAU,WAAW,GAAG;QAC1D,IAAI,CAAC,SAAS,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAI,SAAS,CAAC,mBAAmB,GAAG;QACvE,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,SAAS,CAAC,gBAAgB,GAAG;QACjE,IAAI,CAAC,SAAS,UAAU,EAAE,UAAU,UAAU,GAAG;QACjD,IAAI,CAAC,SAAS,kBAAkB,EAAE,UAAU,kBAAkB,GAAG;QACjE,IAAI,CAAC,SAAS,OAAO,EAAE,UAAU,OAAO,GAAG;QAC3C,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,SAAS,CAAC,iBAAiB,GAAG;QACnE,IAAI,CAAC,SAAS,SAAS,CAAC,OAAO,CAAC,IAAI,IAAI,SAAS,CAAC,oBAAoB,GAAG;QACzE,IAAI,CAAC,SAAS,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC,kBAAkB,GAAG;QACrE,IAAI,SAAS,eAAe,IAAI,GAAG,UAAU,eAAe,GAAG;QAC/D,IAAI,SAAS,oBAAoB,IAAI,GAAG,UAAU,oBAAoB,GAAG;QAEzE,4BAA4B;QAC5B,IAAI,SAAS,SAAS,CAAC,MAAM,KAAK,GAAG;YACnC,UAAU,SAAS,GAAG;QACxB;QAEA,2DAA2D;QAC3D,IAAI,SAAS,MAAM,CAAC,MAAM,KAAK,GAAG;YAChC,UAAU,MAAM,GAAG;QACrB,OAAO,IAAI,SAAS,MAAM,CAAC,MAAM,GAAG,IAAI;YACtC,UAAU,MAAM,GAAG;QACrB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,uBAAuB,CAAC,cAAsB;QAClD,MAAM,YAAY,cAAc,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC7C,KAAK,OAAO,OAAO;gBACnB,MAAM,OAAO,QAAQ;gBACrB,MAAM;gBACN,KAAK,OAAO,SAAS;gBACrB,YAAY,IAAI;YAClB,CAAC;QAED,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,WAAW;uBAAI,KAAK,SAAS;uBAAK;iBAAU;YAC9C,CAAC;QAED,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,cAAc,MAAM,CAAC,uCAAuC,EAAE,cAAc;IAC/F;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,SAAS,cAAc,GAAG,CAAC,CAAC,SAAW,CAAC;gBAC5C,KAAK,OAAO,OAAO;gBACnB,MAAM,OAAO,QAAQ;gBACrB,MAAM;gBACN,KAAK,OAAO,SAAS;gBACrB,YAAY,IAAI;YAClB,CAAC;QAED,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ;uBAAI,KAAK,MAAM;uBAAK;iBAAO;YACrC,CAAC;QAED,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,cAAc,MAAM,CAAC,+BAA+B,CAAC;IACxE;IAEA,MAAM,oBAAoB,CAAC;QACzB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,OAAO;IACvC;IAIA,6CAA6C;IAC7C,MAAM,wBAAwB;QAC5B,uBAAuB;QACvB,iBAAiB;QAEjB,IAAI;YACF,IAAI,CAAC,UAAU,WAAW,EAAE;gBAC1B,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,IAAI,QAA6B,CAAC,SAAS;gBAChE,UAAU,WAAW,CAAC,kBAAkB,CAAC,SAAS,QAAQ;oBACxD,oBAAoB;oBACpB,SAAS;oBACT,YAAY;gBACd;YACF;YAEA,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,SAAS,MAAM;YAE/C,6BAA6B;YAC7B,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,UAAU;wBACR,GAAG,KAAK,QAAQ;wBAChB,UAAU,SAAS,QAAQ;wBAC3B,WAAW,UAAU,QAAQ;oBAC/B;gBACF,CAAC;YAED,2CAA2C;YAC3C,MAAM,eAAe,UAAU;YAE/B,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,IAAI,KAAK,IAAI,2BACpB,MAAM,IAAI,KAAK,IAAI,yBACnB,MAAM,IAAI,KAAK,IAAI,6BACnB,MAAM,OAAO,IAAI;YAErC,iBAAiB;YACjB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,oDAAoD;IACpD,MAAM,iBAAiB,OAAO,KAAa;QACzC,IAAI;YACF,0CAA0C;YAC1C,MAAM,WAAW,MAAM,MACrB,CAAC,kEAAkE,EAAE,IAAI,WAAW,EAAE,IAAI,oBAAoB,CAAC;YAGjH,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,MAAM;gBACR,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,UAAU;4BACR,GAAG,KAAK,QAAQ;4BAChB,SAAS,KAAK,QAAQ,IAAI,KAAK,IAAI,IAAI,KAAK,WAAW,IAAI,KAAK,QAAQ,CAAC,OAAO;4BAChF,MAAM,KAAK,IAAI,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,IAAI;4BACtD,OAAO,KAAK,oBAAoB,IAAI,KAAK,wBAAwB,IAAI,KAAK,QAAQ,CAAC,KAAK;4BACxF,SAAS,KAAK,QAAQ,IAAI,KAAK,UAAU,IAAI,KAAK,QAAQ,CAAC,OAAO;4BAClE,aAAa;gCACX,UAAU;gCACV,WAAW;4BACb;wBACF;oBACF,CAAC;gBAED,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,qEAAqE;YACrE,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAC7C,CAAC;IACH;IAEA,MAAM,iBAAiB,CAAC;QACtB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,WAAW,KAAK,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YACnD,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC;QACvB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,IAAM,CAAC;wBACnC,GAAG,GAAG;wBACN,WAAW,MAAM;oBACnB,CAAC;YACH,CAAC;IACH;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,gBAAgB;YACnB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QACA,+CAA+C;QAC/C,wQAAA,CAAA,QAAK,CAAC,IAAI,CAAC;IACb;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,gBAAgB;QAEhB,IAAI;YACF,sDAAsD;YACtD,MAAM,eAAe;gBACnB,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW;gBACjC,UAAU;oBACR,SAAS,SAAS,QAAQ,CAAC,OAAO;oBAClC,MAAM,SAAS,QAAQ,CAAC,IAAI;oBAC5B,OAAO,SAAS,QAAQ,CAAC,KAAK;oBAC9B,SAAS,SAAS,QAAQ,CAAC,OAAO;oBAClC,aAAa,SAAS,QAAQ,CAAC,WAAW;gBAC5C;gBACA,cAAc,SAAS,YAAY,CAAC,WAAW;gBAC/C,iBAAiB,SAAS,eAAe;gBACzC,sBAAsB,SAAS,oBAAoB;gBACnD,oBAAoB,SAAS,kBAAkB,CAAC,WAAW;gBAC3D,YAAY,SAAS,UAAU;gBAC/B,oBAAoB,SAAS,kBAAkB;gBAC/C,kBAAkB,SAAS,gBAAgB;gBAC3C,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;gBAC7B,QAAQ,SAAS,MAAM;gBACvB,WAAW,SAAS,SAAS;gBAC7B,QAAQ,SAAS,MAAM;gBACvB,gBAAgB,SAAS,cAAc;gBACvC,SAAS,SAAS,OAAO;gBACzB,QAAQ,SAAS,MAAM,CAAC,WAAW;gBACnC,UAAU,SAAS,QAAQ;gBAC3B,eAAe,SAAS,aAAa;YACvC;YAEA,QAAQ,GAAG,CAAC,gCAAgC;YAE5C,MAAM,SAAS,MAAM,eAAe,cAAc,MAAM;YAExD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,sDAAsD;YACtD,OAAO,IAAI,CAAC;QAEd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,MAAM,WAAW;QACtC,SAAU;YACR,gBAAgB;QAClB;IACF;IAIA,qBACE,6WAAC,+IAAA,CAAA,UAAe;kBACd,cAAA,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCAEf,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,OAAO,OAAO,CAAC,IAAI;4CAClC,WAAU;;8DAEV,6WAAC,oSAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGxC,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;;sEACZ,6WAAC,8RAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAGlC,6WAAC;oDAAE,WAAU;8DAAmB;;;;;;;;;;;;;;;;;;8CAGpC,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;4CACT,WAAU;;8DAEV,6WAAC,oRAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGlC,6WAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,gBAAgB;4CAC1B,WAAU;sDAET,AAAC,gBAAgB,2BAChB;;kEACE,6WAAC;wDAAI,WAAU;;;;;;oDAAuE;;6EAIxF;;kEACE,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kCAwB7C,6WAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,6WAAC,gIAAA,CAAA,OAAI;;kDACH,6WAAC,gIAAA,CAAA,aAAU;;0DACT,6WAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,8RAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,6WAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,6WAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;;0EACC,6WAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,6WAAC;gEACC,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gEACzD,WAAW,CAAC,uFAAuF,EACjG,OAAO,IAAI,GAAG,mBAAmB,mBACjC;gEACF,aAAY;;;;;;4DAEb,OAAO,IAAI,kBACV,6WAAC;gEAAE,WAAU;;kFACX,6WAAC,wSAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEACtB,OAAO,IAAI;;;;;;;;;;;;;kEAMlB,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAG,WAAU;0EAAoC;;;;;;0EAElD,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;;0FACC,6WAAC;gFAAM,WAAU;0FAA+C;;;;;;0FAGhE,6WAAC;gFACC,MAAK;gFACL,OAAO,SAAS,SAAS,CAAC,IAAI;gFAC9B,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;gFACnE,WAAW,CAAC,uFAAuF,EACjG,MAAM,CAAC,iBAAiB,GAAG,mBAAmB,mBAC9C;gFACF,aAAY;;;;;;4EAEb,MAAM,CAAC,iBAAiB,kBACvB,6WAAC;gFAAE,WAAU;0FAA6B,MAAM,CAAC,iBAAiB;;;;;;;;;;;;kFAItE,6WAAC;;0FACC,6WAAC;gFAAM,WAAU;0FAA+C;;;;;;0FAGhE,6WAAC;gFACC,MAAK;gFACL,OAAO,SAAS,SAAS,CAAC,OAAO;gFACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;gFACtE,WAAW,CAAC,uFAAuF,EACjG,MAAM,CAAC,oBAAoB,GAAG,mBAAmB,mBACjD;gFACF,aAAY;;;;;;4EAEb,MAAM,CAAC,oBAAoB,kBAC1B,6WAAC;gFAAE,WAAU;0FAA6B,MAAM,CAAC,oBAAoB;;;;;;;;;;;;kFAIzE,6WAAC;;0FACC,6WAAC;gFAAM,WAAU;0FAA+C;;;;;;0FAGhE,6WAAC;gFACC,MAAK;gFACL,OAAO,SAAS,SAAS,CAAC,KAAK;gFAC/B,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gFACpE,WAAW,CAAC,uFAAuF,EACjG,MAAM,CAAC,kBAAkB,GAAG,mBAAmB,mBAC/C;gFACF,aAAY;;;;;;4EAEb,MAAM,CAAC,kBAAkB,kBACxB,6WAAC;gFAAE,WAAU;0FAA6B,MAAM,CAAC,kBAAkB;;;;;;;;;;;;kFAIvE,6WAAC;;0FACC,6WAAC;gFAAM,WAAU;0FAA+C;;;;;;0FAGhE,6WAAC;gFACC,MAAK;gFACL,KAAI;gFACJ,OAAO,SAAS,SAAS,CAAC,UAAU,IAAI;gFACxC,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gFACvF,WAAU;gFACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;kEAMpB,6WAAC;;0EACC,6WAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;gFACC,MAAK;gFACL,OAAO;gFACP,UAAU,CAAC;oFACT,oBAAoB,EAAE,MAAM,CAAC,KAAK;oFAClC,oBAAoB,CAAC;gFACvB;gFACA,SAAS,IAAM,qBAAqB;gFACpC,WAAW;gFACX,WAAU;gFACV,aAAY;gFACZ,cAAa;;;;;;4EAGd,mCACC,6WAAC;gFAAI,WAAU;0FACZ,eAAe,MAAM,GAAG,kBACvB;;sGACE,6WAAC;4FAAI,WAAU;;gGACZ,eAAe,MAAM;gGAAC;;;;;;;wFAExB,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,6WAAC;gGAEC,SAAS,IAAM,YAAY;gGAC3B,WAAW,CAAC,kEAAkE,EAC5E,UAAU,mBACN,8BACA,oBACJ;0GAEF,cAAA,6WAAC;oGAAI,WAAU;;sHACb,6WAAC;;8HACC,6WAAC;oHAAE,WAAU;8HAA6B,MAAM,IAAI;;;;;;8HACpD,6WAAC;oHAAE,WAAU;8HAAyB,MAAM,KAAK;;;;;;gHAChD,MAAM,OAAO,kBACZ,6WAAC;oHAAE,WAAU;8HAAyB,MAAM,OAAO;;;;;;;;;;;;sHAGvD,6WAAC;4GAAI,WAAU;;8HACb,6WAAC;oHAAK,WAAW,CAAC,4CAA4C,EAC5D,MAAM,MAAM,KAAK,aACb,gCACA,iCACJ;8HACC,MAAM,MAAM;;;;;;gHAEd,MAAM,UAAU,kBACf,6WAAC;oHAAE,WAAU;;wHACV,MAAM,UAAU;wHAAC;;;;;;;;;;;;;;;;;;;+FA1BrB,MAAM,EAAE;;;;;;mGAkCjB,iCACF,6WAAC;oFAAI,WAAU;;sGACb,6WAAC;;gGAAE;gGAA2B;gGAAiB;;;;;;;sGAC/C,6WAAC;4FACC,MAAK;4FACL,SAAS;gGACP,qBAAqB;4FACrB,iDAAiD;4FACnD;4FACA,WAAU;sGACX;;;;;;;;;;;yGAKH,6WAAC;oFAAI,WAAU;8FACb,cAAA,6WAAC;kGAAE;;;;;;;;;;;;;;;;;;;;;;oEAOZ,+BACC,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;gFAAI,WAAU;;kGACb,6WAAC;wFAAI,WAAU;kGACb,cAAA,6WAAC;4FAAK,WAAU;sGACb,cAAc,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;kGAG7C,6WAAC;;0GACC,6WAAC;gGAAE,WAAU;0GAA6B,cAAc,IAAI;;;;;;0GAC5D,6WAAC;gGAAE,WAAU;0GAAyB,cAAc,KAAK;;;;;;4FACxD,cAAc,OAAO,kBACpB,6WAAC;gGAAE,WAAU;0GAAyB,cAAc,OAAO;;;;;;;;;;;;;;;;;;0FAIjE,6WAAC;gFACC,MAAK;gFACL,SAAS;gFACT,WAAU;0FAEV,cAAA,6WAAC,gRAAA,CAAA,IAAC;oFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0EAKrB,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAE,WAAU;kFAAwB;;;;;;kFAGrC,6WAAC;wEAAI,WAAU;;4EACZ,iCACC,6WAAC,qSAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;0FAErB,6WAAC;gFAAI,WAAU;;oFAAqD;oFAC9D,gBAAgB,MAAM;oFAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQrC,6WAAC;;kEACC,6WAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6WAAC;wDACC,OAAO,SAAS,WAAW;wDAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;wDAChE,MAAM;wDACN,WAAW,CAAC,uFAAuF,EACjG,OAAO,WAAW,GAAG,mBAAmB,mBACxC;wDACF,aAAY;;;;;;oDAEb,OAAO,WAAW,kBACjB,6WAAC;wDAAE,WAAU;;0EACX,6WAAC,wSAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DACtB,OAAO,WAAW;;;;;;;;;;;;;0DAKzB,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;;0EACC,6WAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAoC;;;;;;0EAGrD,6WAAC,kIAAA,CAAA,SAAM;gEACL,OAAO,SAAS,YAAY;gEAC5B,eAAe,CAAC,QAAU,kBAAkB,gBAAgB;;kFAE5D,6WAAC,kIAAA,CAAA,gBAAa;wEAAC,WAAU;kFACvB,cAAA,6WAAC,kIAAA,CAAA,cAAW;4EAAC,aAAY;;;;;;;;;;;kFAE3B,6WAAC,kIAAA,CAAA,gBAAa;kFACX,cAAc,GAAG,CAAC,CAAC,qBAClB,6WAAC,kIAAA,CAAA,aAAU;gFAAkB,OAAO,KAAK,KAAK;0FAC5C,cAAA,6WAAC;oFAAI,WAAU;;sGACb,6WAAC,KAAK,IAAI;4FAAC,WAAU;;;;;;sGACrB,6WAAC;;8GACC,6WAAC;oGAAI,WAAU;8GAAe,KAAK,KAAK;;;;;;8GACxC,6WAAC;oGAAI,WAAU;8GAAyB,KAAK,WAAW;;;;;;;;;;;;;;;;;;+EAL7C,KAAK,KAAK;;;;;;;;;;;;;;;;4DAYhC,OAAO,YAAY,kBAClB,6WAAC;gEAAE,WAAU;;kFACX,6WAAC,wSAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEACtB,OAAO,YAAY;;;;;;;;;;;;;kEAK1B,6WAAC;;0EACC,6WAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,6WAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,MAAK;gEACL,OAAO,SAAS,eAAe;gEAC/B,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gEACpF,WAAU;gEACV,aAAY;;;;;;4DAEb,OAAO,eAAe,kBACrB,6WAAC;gEAAE,WAAU;0EAA6B,OAAO,eAAe;;;;;;;;;;;;kEAIpE,6WAAC;;0EACC,6WAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,6WAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,OAAO,SAAS,oBAAoB;gEACpC,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEACvF,WAAU;gEACV,aAAY;;;;;;4DAEb,OAAO,oBAAoB,kBAC1B,6WAAC;gEAAE,WAAU;0EAA6B,OAAO,oBAAoB;;;;;;;;;;;;kEAIzE,6WAAC;;0EACC,6WAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,6WAAC;gEACC,OAAO,SAAS,kBAAkB;gEAClC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;gEACvE,WAAU;;kFAEV,6WAAC;wEAAO,OAAM;kFAAW;;;;;;kFACzB,6WAAC;wEAAO,OAAM;kFAAqB;;;;;;kFACnC,6WAAC;wEAAO,OAAM;kFAAY;;;;;;kFAC1B,6WAAC;wEAAO,OAAM;kFAAgB;;;;;;;;;;;;4DAE/B,OAAO,kBAAkB,kBACxB,6WAAC;gEAAE,WAAU;0EAA6B,OAAO,kBAAkB;;;;;;;;;;;;kEAIvE,6WAAC;;0EACC,6WAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,6WAAC;gEACC,OAAO,SAAS,MAAM;gEACtB,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK;gEAC3D,WAAU;0EAET,cAAc,GAAG,CAAC,CAAC,uBAClB,6WAAC;wEAA0B,OAAO,OAAO,KAAK;kFAC3C,OAAO,KAAK;uEADF,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;0DAQjC,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDACC,MAAK;wDACL,IAAG;wDACH,SAAS,SAAS,QAAQ;wDAC1B,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,OAAO;wDAC/D,WAAU;;;;;;kEAEZ,6WAAC;wDAAM,SAAQ;wDAAW,WAAU;;0EAClC,6WAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAA4B;;;;;;;;;;;;;;;;;;;;;;;;;0CAQpD,6WAAC,gIAAA,CAAA,OAAI;;kDACH,6WAAC,gIAAA,CAAA,aAAU;;0DACT,6WAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,8RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGhC,6WAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,6WAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,6WAAC;;kEACC,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAG,WAAU;0EAAsC;;;;;;0EACpD,6WAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS;gEACT,UAAU;gEACV,WAAU;0EAET,oCACC;;sFACE,6WAAC,qSAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEAAyB;;iGAI9C;;sFACE,6WAAC,kSAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEAAY;;;;;;;;;;;;;;oDAOzC,+BACC,6WAAC;wDAAI,WAAU;kEACb,cAAA,6WAAC;4DAAE,WAAU;;8EACX,6WAAC,wSAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEACtB;;;;;;;;;;;;kEAKP,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;;kFACC,6WAAC,iIAAA,CAAA,QAAK;wEAAC,WAAU;kFAAoC;;;;;;kFAGrD,6WAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,MAAK;wEACL,OAAO,SAAS,QAAQ,CAAC,WAAW,EAAE,YAAY;wEAClD,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;wEACtE,aAAY;wEACZ,WAAU;;;;;;kFAEZ,6WAAC;wEAAE,WAAU;kFAA6B;;;;;;;;;;;;0EAG5C,6WAAC;;kFACC,6WAAC,iIAAA,CAAA,QAAK;wEAAC,WAAU;kFAAoC;;;;;;kFAGrD,6WAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,MAAK;wEACL,OAAO,SAAS,QAAQ,CAAC,WAAW,EAAE,aAAa;wEACnD,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;wEACvE,aAAY;wEACZ,WAAU;;;;;;kFAEZ,6WAAC;wEAAE,WAAU;kFAA6B;;;;;;;;;;;;;;;;;;kEAI9C,6WAAC;wDAAI,WAAU;kEACb,cAAA,6WAAC;4DAAE,WAAU;;8EACX,6WAAC;8EAAO;;;;;;gEAA4B;;;;;;;;;;;;;;;;;;0DAO1C,6WAAC;;kEACC,6WAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,6WAAC;;0EACC,6WAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAoC;;;;;;0EAGrD,6WAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO,SAAS,QAAQ,CAAC,OAAO;gEAChC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;gEACrE,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,mBAAmB,GAAG,mBAAmB,IAAI;gEACvE,aAAY;;;;;;4DAEb,MAAM,CAAC,mBAAmB,kBACzB,6WAAC;gEAAE,WAAU;;kFACX,6WAAC,wSAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEACtB,MAAM,CAAC,mBAAmB;;;;;;;;;;;;;kEAKjC,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;;kFACC,6WAAC,iIAAA,CAAA,QAAK;wEAAC,WAAU;kFAAoC;;;;;;kFAGrD,6WAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,OAAO,SAAS,QAAQ,CAAC,IAAI;wEAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;wEAClE,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,gBAAgB,GAAG,mBAAmB,IAAI;wEACpE,aAAY;;;;;;oEAEb,MAAM,CAAC,gBAAgB,kBACtB,6WAAC;wEAAE,WAAU;;0FACX,6WAAC,wSAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;4EACtB,MAAM,CAAC,gBAAgB;;;;;;;;;;;;;0EAK9B,6WAAC;;kFACC,6WAAC,iIAAA,CAAA,QAAK;wEAAC,WAAU;kFAAoC;;;;;;kFAGrD,6WAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,OAAO,SAAS,QAAQ,CAAC,KAAK;wEAC9B,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;wEACnE,WAAU;wEACV,aAAY;;;;;;;;;;;;0EAMhB,6WAAC;;kFACC,6WAAC,iIAAA,CAAA,QAAK;wEAAC,WAAU;kFAAoC;;;;;;kFAGrD,6WAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,OAAO,SAAS,QAAQ,CAAC,OAAO;wEAChC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;wEACrE,WAAU;wEACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAexB,6WAAC,gIAAA,CAAA,OAAI;;kDACH,6WAAC,gIAAA,CAAA,aAAU;;0DACT,6WAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,8RAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,6WAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,6WAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;;sEACC,6WAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6WAAC;4DACC,MAAK;4DACL,OAAO,SAAS,UAAU;4DAC1B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC/D,WAAW,CAAC,uFAAuF,EACjG,OAAO,UAAU,GAAG,mBAAmB,mBACvC;;;;;;wDAEH,OAAO,UAAU,kBAChB,6WAAC;4DAAE,WAAU;;8EACX,6WAAC,wSAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEACtB,OAAO,UAAU;;;;;;;;;;;;;8DAKxB,6WAAC;;sEACC,6WAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6WAAC;4DACC,MAAK;4DACL,OAAO,SAAS,kBAAkB;4DAClC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;4DACvE,WAAW,CAAC,uFAAuF,EACjG,OAAO,kBAAkB,GAAG,mBAAmB,mBAC/C;;;;;;wDAEH,OAAO,kBAAkB,kBACxB,6WAAC;4DAAE,WAAU;;8EACX,6WAAC,wSAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEACtB,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAStC,6WAAC,gIAAA,CAAA,OAAI;;kDACH,6WAAC,gIAAA,CAAA,aAAU;;0DACT,6WAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6WAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,6WAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6WAAC;;kEACC,6WAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6WAAC;wDAAI,WAAU;kEACZ,gBAAgB,GAAG,CAAC,CAAC,wBACpB,6WAAC,kIAAA,CAAA,SAAM;gEAEL,MAAK;gEACL,SAAS,SAAS,SAAS,CAAC,QAAQ,CAAC,WAAW,YAAY;gEAC5D,MAAK;gEACL,SAAS,IACP,SAAS,SAAS,CAAC,QAAQ,CAAC,WACxB,cAAc,WACd,WAAW;gEAEjB,WAAU;0EAET;+DAXI;;;;;;;;;;;;;;;;0DAiBb,6WAAC;;kEACC,6WAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gEAC7C,WAAU;gEACV,aAAY;gEACZ,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,cAAc,IAAI,WAAW,WAAW;;;;;;0EAErF,6WAAC,kIAAA,CAAA,SAAM;gEAAC,MAAK;gEAAS,SAAS,IAAM,WAAW;0EAC9C,cAAA,6WAAC,sRAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;4CAKrB,SAAS,SAAS,CAAC,MAAM,GAAG,mBAC3B,6WAAC;;kEACC,6WAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6WAAC;wDAAI,WAAU;kEACZ,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,wBACvB,6WAAC,iIAAA,CAAA,QAAK;gEAAe,SAAQ;gEAAY,WAAU;;oEAChD;kFACD,6WAAC;wEACC,MAAK;wEACL,SAAS,IAAM,cAAc;wEAC7B,WAAU;kFAEV,cAAA,6WAAC,gRAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;+DAPL;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAkBxB,6WAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6WAAC,gIAAA,CAAA,aAAU;;0DACT,6WAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,wRAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAG/B,6WAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAgB;;;;;;;;;;;;kDAI7C,6WAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;4CACpB,OAAO,MAAM,kBACZ,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAE,WAAU;;sEACX,6WAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDACtB,OAAO,MAAM;;;;;;;;;;;;0DAKpB,6WAAC,oJAAA,CAAA,UAAgB;gDACf,YAAW;gDACX,kBAAkB;gDAClB,eAAe;gDACf,UAAU;gDACV,eAAe;gDACf,aAAa;gDACb,WAAU;;;;;;4CAMX,SAAS,MAAM,CAAC,MAAM,GAAG,mBACxB,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAG,WAAU;;4DAAsC;4DAAgB,SAAS,MAAM,CAAC,MAAM;4DAAC;;;;;;;kEAC3F,6WAAC;wDAAI,WAAU;kEACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,6WAAC;gEAAgB,WAAU;;kFACzB,6WAAC;wEAAI,WAAU;kFACb,cAAA,6WAAC;4EACC,KAAK,MAAM,GAAG;4EACd,KAAK,MAAM,IAAI;4EACf,WAAU;;;;;;;;;;;kFAKd,6WAAC;wEAAI,WAAU;;0FACb,6WAAC,kIAAA,CAAA,SAAM;gFACL,MAAK;gFACL,SAAQ;gFACR,MAAK;gFACL,SAAS,IAAM,gBAAgB;gFAC/B,WAAW,MAAM,SAAS,GAAG,2BAA2B;0FAExD,cAAA,6WAAC,sRAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;;;;;;0FAElB,6WAAC,kIAAA,CAAA,SAAM;gFACL,MAAK;gFACL,SAAQ;gFACR,MAAK;gFACL,SAAS,IAAM,OAAO,IAAI,CAAC,MAAM,GAAG,EAAE;0FAEtC,cAAA,6WAAC,oRAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;;;;;;0FAEjB,6WAAC,kIAAA,CAAA,SAAM;gFACL,MAAK;gFACL,SAAQ;gFACR,MAAK;gFACL,SAAS,IAAM,YAAY;0FAE3B,cAAA,6WAAC,gRAAA,CAAA,IAAC;oFAAC,WAAU;;;;;;;;;;;;;;;;;oEAKhB,MAAM,SAAS,kBACd,6WAAC;wEAAI,WAAU;kFACb,cAAA,6WAAC,iIAAA,CAAA,QAAK;4EAAC,WAAU;sFAAiC;;;;;;;;;;;;+DAzC9C;;;;;;;;;;kEAkDd,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;0EAAE;;;;;;0EACH,6WAAC;0EAAE;;;;;;0EACH,6WAAC;0EAAE;;;;;;0EACH,6WAAC;0EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQZ,CAAC,SAAS,MAAM,CAAC,MAAM,GAAG,KAAK,SAAS,SAAS,CAAC,MAAM,GAAG,CAAC,mBAC3D,6WAAC,gIAAA,CAAA,OAAI;;kDACH,6WAAC,gIAAA,CAAA,aAAU;;0DACT,6WAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,+SAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAA2B;;;;;;;0DAGpD,6WAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6WAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,6WAAC;4CAAI,WAAU;;gDAEZ,SAAS,MAAM,CAAC,MAAM,GAAG,mBACxB,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAG,WAAU;;8EACZ,6WAAC,wRAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAY;gEACpB,SAAS,MAAM,CAAC,MAAM;gEAAC;;;;;;;sEAElC,6WAAC;4DAAI,WAAU;;gEACZ,SAAS,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACvC,6WAAC;wEAAgB,WAAU;;0FACzB,6WAAC;gFAAI,WAAU;0FACb,cAAA,6WAAC;oFACC,KAAK,MAAM,GAAG;oFACd,KAAK,MAAM,IAAI;oFACf,WAAU;;;;;;;;;;;0FAGd,6WAAC;gFAAI,WAAU;;kGACb,6WAAC;wFAAE,WAAU;kGAA8C,MAAM,IAAI;;;;;;oFACpE,MAAM,SAAS,kBACd,6WAAC;wFAAK,WAAU;kGAAwB;;;;;;;;;;;;;uEAXpC;;;;;gEAgBX,SAAS,MAAM,CAAC,MAAM,GAAG,mBACxB,6WAAC;oEAAE,WAAU;;wEAAwB;wEACjC,SAAS,MAAM,CAAC,MAAM,GAAG;wEAAE;;;;;;;;;;;;;;;;;;;gDAQtC,SAAS,SAAS,CAAC,MAAM,GAAG,mBAC3B,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAG,WAAU;;8EACZ,6WAAC,kSAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAY;gEACpB,SAAS,SAAS,CAAC,MAAM;gEAAC;;;;;;;sEAExC,6WAAC;4DAAI,WAAU;;gEACZ,SAAS,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBACxC,6WAAC;wEAAgB,WAAU;;0FACzB,6WAAC;gFAAI,WAAU;0FACb,cAAA,6WAAC;oFAAK,WAAU;8FACb,IAAI,IAAI,CAAC,WAAW;;;;;;;;;;;0FAGzB,6WAAC;gFAAK,WAAU;0FAAY,IAAI,IAAI;;;;;;;uEAN5B;;;;;gEASX,SAAS,SAAS,CAAC,MAAM,GAAG,mBAC3B,6WAAC;oEAAE,WAAU;;wEAA6B;wEAAE,SAAS,SAAS,CAAC,MAAM,GAAG;wEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAW1F,6WAAC,gIAAA,CAAA,OAAI;;kDACH,6WAAC,gIAAA,CAAA,aAAU;;0DACT,6WAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,0RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGhC,6WAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6WAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;4CACpB,OAAO,SAAS,kBACf,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAE,WAAU;;sEACX,6WAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDACtB,OAAO,SAAS;;;;;;;;;;;;0DAMvB,6WAAC;;kEACC,6WAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;;kFACC,6WAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,6WAAC,oJAAA,CAAA,UAAgB;wEACf,YAAW;wEACX,kBAAkB,CAAC,UAAY,qBAAqB,iBAAiB;wEACrE,eAAe;wEACf,UAAU;wEACV,eAAe;wEACf,WAAU;;;;;;kFAEZ,6WAAC;wEAAE,WAAU;kFAA6B;;;;;;;;;;;;0EAG5C,6WAAC;;kFACC,6WAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,6WAAC,oJAAA,CAAA,UAAgB;wEACf,YAAW;wEACX,kBAAkB,CAAC,UAAY,qBAAqB,uBAAuB;wEAC3E,eAAe;wEACf,UAAU;wEACV,eAAe;wEACf,WAAU;;;;;;kFAEZ,6WAAC;wEAAE,WAAU;kFAA6B;;;;;;;;;;;;0EAG5C,6WAAC,0JAAA,CAAA,UAAsB;gEACrB,cAAa;gEACb,OAAM;gEACN,aAAY;gEACZ,UAAU;gEACV,kBAAkB,CAAC,UAAY,qBAAqB,iBAAiB;gEACrE,eAAe;gEACf,eAAe,SAAS,SAAS,CAAC,aAAa,IAAI,EAAE;;;;;;0EAGvD,6WAAC,0JAAA,CAAA,UAAsB;gEACrB,cAAa;gEACb,OAAM;gEACN,aAAY;gEACZ,UAAU;gEACV,kBAAkB,CAAC,UAAY,qBAAqB,sBAAsB;gEAC1E,eAAe;gEACf,eAAe,SAAS,SAAS,CAAC,kBAAkB,IAAI,EAAE;;;;;;;;;;;;;;;;;;0DAMhE,6WAAC;;kEACC,6WAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,0JAAA,CAAA,UAAsB;gEACrB,cAAa;gEACb,OAAM;gEACN,aAAY;gEACZ,UAAU;gEACV,kBAAkB,CAAC,UAAY,qBAAqB,0BAA0B;gEAC9E,eAAe;gEACf,eAAe,SAAS,SAAS,CAAC,sBAAsB,IAAI,EAAE;;;;;;0EAGhE,6WAAC,0JAAA,CAAA,UAAsB;gEACrB,cAAa;gEACb,OAAM;gEACN,aAAY;gEACZ,UAAU;gEACV,kBAAkB,CAAC,UAAY,qBAAqB,wBAAwB;gEAC5E,eAAe;gEACf,eAAe,SAAS,SAAS,CAAC,oBAAoB,IAAI,EAAE;;;;;;0EAG9D,6WAAC,0JAAA,CAAA,UAAsB;gEACrB,cAAa;gEACb,OAAM;gEACN,aAAY;gEACZ,UAAU;gEACV,kBAAkB,CAAC,UAAY,qBAAqB,kBAAkB;gEACtE,eAAe;gEACf,eAAe,SAAS,SAAS,CAAC,cAAc,IAAI,EAAE;;;;;;0EAGxD,6WAAC,0JAAA,CAAA,UAAsB;gEACrB,cAAa;gEACb,OAAM;gEACN,aAAY;gEACZ,UAAU;gEACV,kBAAkB,CAAC,UAAY,qBAAqB,sBAAsB;gEAC1E,eAAe;gEACf,eAAe,SAAS,SAAS,CAAC,kBAAkB,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASpE,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;kDAAU;;;;;;kDAGxC,6WAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,UAAU;kDAC7B,6BACC;;8DACE,6WAAC;oDAAI,WAAU;;;;;;gDAAuE;;yEAIxF;;8DACE,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnD", "debugId": null}}]}