'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAppSelector } from '@/store'
import { selectIsAuthenticated, selectUser } from '@/store/slices/authSlice'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { toast } from 'sonner'
import DashboardLayout from '@/components/layout/DashboardLayout'
import {
  useGetPropertyOwnersQuery,
  useDeletePropertyOwnerMutation,
  useVerifyPropertyOwnerMutation
} from '@/store/api/propertyOwnersApi'
import { UserRole } from '@/types'
import { 
  Users,
  Search,
  Filter,
  Download,
  Upload,
  Eye,
  Edit,
  MapPin,
  TrendingUp,
  CheckCircle,
  Plus,
  Building,
  User,
  DollarSign,
  Calendar,
  Trash2,
  Star,
  Award,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Shield,
  AlertCircle,
  Phone,
  Mail,
  FileText,
  Briefcase
} from 'lucide-react'

export default function PropertyOwnersPage() {
  const router = useRouter()
  const isAuthenticated = useAppSelector(selectIsAuthenticated)
  const currentUser = useAppSelector(selectUser)

  // State management
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedFilter, setSelectedFilter] = useState('all')
  const [selectedOwners, setSelectedOwners] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(9)

  // API queries
  const { data: ownersResponse, isLoading, refetch } = useGetPropertyOwnersQuery({
    page: currentPage,
    limit: pageSize,
    search: searchQuery || undefined,
    status: selectedFilter !== 'all' ? selectedFilter : undefined
  })

  // Mutations
  const [deleteOwner] = useDeletePropertyOwnerMutation()
  const [verifyOwner] = useVerifyPropertyOwnerMutation()

  // Authentication check
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, router])

  const canManageOwners = currentUser?.role === UserRole.ADMIN || currentUser?.role === UserRole.SUBADMIN

  if (!isAuthenticated || !canManageOwners) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="bg-white p-8 rounded-lg shadow-lg">
            <h1 className="text-2xl font-bold mb-4 text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mb-4">You don't have permission to access property owner management.</p>
            <Button onClick={() => router.push('/dashboard')} className="bg-emerald-600 hover:bg-emerald-700">
              Go to Dashboard
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Extract data safely
  const owners = Array.isArray(ownersResponse?.data?.data) ? ownersResponse.data.data : []
  const pagination = ownersResponse?.data?.meta?.pagination || {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: pageSize
  }

  // Calculate analytics
  const totalOwners = pagination.totalItems
  const verifiedOwners = owners.filter(owner => owner.verificationStatus === 'verified').length
  const pendingOwners = owners.filter(owner => owner.verificationStatus === 'pending').length
  const totalProperties = owners.reduce((sum, owner) => sum + (owner.properties?.length || 0), 0)

  // Filter owners
  const filteredOwners = owners.filter((owner: any) => {
    const matchesSearch = !searchQuery ||
                         owner.firstName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         owner.lastName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         owner.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         owner.company?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         owner.address?.city?.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesFilter = selectedFilter === 'all' ||
                         (selectedFilter === 'verified' && owner.verificationStatus === 'verified') ||
                         (selectedFilter === 'pending' && owner.verificationStatus === 'pending') ||
                         (selectedFilter === 'rejected' && owner.verificationStatus === 'rejected') ||
                         (selectedFilter === 'developer' && owner.isDeveloper) ||
                         (selectedFilter === 'owner' && owner.isOwner)

    return matchesSearch && matchesFilter
  })

  // Helper functions
  const formatCurrency = (amount: number) => {
    if (amount >= 10000000) {
      return `₹${(amount / 10000000).toFixed(1)}Cr`
    } else if (amount >= 100000) {
      return `₹${(amount / 100000).toFixed(1)}L`
    } else {
      return `₹${amount.toLocaleString()}`
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'verified':
        return <Badge className="bg-green-100 text-green-800 border-green-200"><CheckCircle className="h-3 w-3 mr-1" />Verified</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200"><AlertCircle className="h-3 w-3 mr-1" />Pending</Badge>
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800 border-red-200"><AlertCircle className="h-3 w-3 mr-1" />Rejected</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getRoleBadge = (owner: any) => {
    const roles = []
    if (owner.isOwner) roles.push('Owner')
    if (owner.isDeveloper) roles.push('Developer')
    if (owner.isInvestor) roles.push('Investor')
    
    return roles.map((role, index) => (
      <Badge key={index} variant="outline" className="text-xs mr-1">
        {role === 'Developer' && <Briefcase className="h-3 w-3 mr-1" />}
        {role === 'Owner' && <Building className="h-3 w-3 mr-1" />}
        {role === 'Investor' && <DollarSign className="h-3 w-3 mr-1" />}
        {role}
      </Badge>
    ))
  }

  const handleOwnerSelect = (ownerId: string) => {
    setSelectedOwners(prev => 
      prev.includes(ownerId) 
        ? prev.filter(id => id !== ownerId)
        : [...prev, ownerId]
    )
  }

  const handleSelectAllOwners = (checked: boolean) => {
    if (checked) {
      setSelectedOwners(filteredOwners.map(o => o._id))
    } else {
      setSelectedOwners([])
    }
  }

  const handleDeleteOwner = async (ownerId: string) => {
    if (!confirm('Are you sure you want to delete this property owner? This action cannot be undone.')) return
    try {
      await deleteOwner(ownerId).unwrap()
      toast.success('Property owner deleted successfully!')
      refetch()
    } catch (error) {
      toast.error('Failed to delete property owner')
      console.error('Delete owner error:', error)
    }
  }

  const handleVerifyOwner = async (ownerId: string) => {
    try {
      await verifyOwner({ id: ownerId, status: 'verified' }).unwrap()
      toast.success('Property owner verified successfully!')
      refetch()
    } catch (error) {
      toast.error('Failed to verify property owner')
      console.error('Verify owner error:', error)
    }
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Beautiful Header with Gradient */}
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold mb-2 flex items-center gap-3">
                <Users className="h-8 w-8" />
                Property Owners & Developers
              </h1>
              <p className="text-blue-100">Manage property owners, developers, and their verification status</p>
            </div>
            
            <div className="flex items-center gap-3">
              <Button 
                variant="secondary"
                className="bg-white/20 hover:bg-white/30 text-white border-white/30"
              >
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>
              
              <Button 
                variant="secondary"
                className="bg-white/20 hover:bg-white/30 text-white border-white/30"
              >
                <Upload className="h-4 w-4 mr-2" />
                Import Owners
              </Button>
              
              <Button 
                onClick={() => router.push('/properties/owners/add')}
                className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Owner/Developer
              </Button>
            </div>
          </div>
        </div>

        {/* Advanced Owner Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-blue-200 hover:shadow-lg transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Owners</p>
                  <p className="text-3xl font-bold text-gray-900">{totalOwners}</p>
                  <p className="text-xs text-blue-600 mt-1 flex items-center">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +8% from last month
                  </p>
                </div>
                <div className="p-3 rounded-full bg-blue-100">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-green-200 hover:shadow-lg transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Verified Owners</p>
                  <p className="text-3xl font-bold text-gray-900">{verifiedOwners}</p>
                  <p className="text-xs text-green-600 mt-1 flex items-center">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Verified accounts
                  </p>
                </div>
                <div className="p-3 rounded-full bg-green-100">
                  <Shield className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-yellow-200 hover:shadow-lg transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending Verification</p>
                  <p className="text-3xl font-bold text-gray-900">{pendingOwners}</p>
                  <p className="text-xs text-yellow-600 mt-1 flex items-center">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Awaiting review
                  </p>
                </div>
                <div className="p-3 rounded-full bg-yellow-100">
                  <AlertCircle className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-purple-200 hover:shadow-lg transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Properties</p>
                  <p className="text-3xl font-bold text-gray-900">{totalProperties}</p>
                  <p className="text-xs text-purple-600 mt-1 flex items-center">
                    <Building className="h-3 w-3 mr-1" />
                    Managed properties
                  </p>
                </div>
                <div className="p-3 rounded-full bg-purple-100">
                  <Building className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Bulk Actions Section */}
        {selectedOwners.length > 0 && (
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-blue-600" />
                  <span className="font-medium text-blue-800">
                    {selectedOwners.length} owners selected
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-blue-100 border-blue-300 text-blue-700 hover:bg-blue-200"
                  >
                    <Shield className="h-4 w-4 mr-2" />
                    Bulk Verify
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedOwners([])}
                    className="border-gray-300 text-gray-600 hover:bg-gray-50"
                  >
                    Clear Selection
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Advanced Filters & Search */}
        <Card className="border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center text-blue-700">
              <Filter className="h-5 w-5 mr-2" />
              Owner Filters & Search
            </CardTitle>
            <CardDescription>
              Filter and search through property owners and developers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-6">
              {/* Search */}
              <div className="lg:col-span-2">
                <Label className="text-sm font-medium mb-2 block">Search Owners</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by name, email, company..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 border-blue-200 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Status Filter */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Filter by Status</Label>
                <Select value={selectedFilter} onValueChange={setSelectedFilter}>
                  <SelectTrigger className="border-blue-200 focus:border-blue-500">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Owners</SelectItem>
                    <SelectItem value="verified">Verified</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                    <SelectItem value="developer">Developers</SelectItem>
                    <SelectItem value="owner">Property Owners</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Quick Actions */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Quick Actions</Label>
                <Button
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                  onClick={() => router.push('/properties/owners/add')}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Owner
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Owners Grid */}
        <Card className="border-blue-200">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center text-blue-700">
                  <Users className="h-5 w-5 mr-2" />
                  Property Owners & Developers
                </CardTitle>
                <CardDescription>
                  {filteredOwners.length} of {totalOwners} owners
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={selectedOwners.length === filteredOwners.length && filteredOwners.length > 0}
                  onCheckedChange={handleSelectAllOwners}
                />
                <span className="text-sm text-gray-600">Select All</span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-3"></div>
                  <p className="text-gray-600">Loading owners...</p>
                </div>
              </div>
            ) : filteredOwners.length === 0 ? (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500 mb-2">No owners found</p>
                <p className="text-sm text-gray-400 mb-4">Start by adding your first property owner or developer</p>
                <Button
                  onClick={() => router.push('/properties/owners/add')}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Your First Owner
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredOwners.map((owner: any) => (
                  <Card
                    key={owner._id}
                    className={`border-blue-200 hover:shadow-xl hover:border-blue-300 transition-all duration-300 transform hover:-translate-y-1 ${
                      selectedOwners.includes(owner._id)
                        ? 'ring-2 ring-blue-500 bg-blue-50/50 shadow-lg'
                        : 'hover:bg-white/80'
                    }`}
                  >
                    <CardContent className="p-6">
                      {/* Owner Header */}
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-start gap-3">
                          <Checkbox
                            checked={selectedOwners.includes(owner._id)}
                            onCheckedChange={() => handleOwnerSelect(owner._id)}
                            className="mt-1"
                          />
                          <div className="flex-1">
                            <h3 className="text-lg font-bold text-gray-900 line-clamp-1 mb-1">
                              {owner.firstName} {owner.lastName}
                            </h3>
                            <div className="flex items-center gap-2 mb-2">
                              <div className="flex items-center gap-1 text-sm text-gray-600">
                                <Mail className="h-3 w-3 text-blue-600" />
                                <span>{owner.email}</span>
                              </div>
                            </div>
                            {owner.phone && (
                              <div className="flex items-center gap-1 text-xs text-gray-500 mb-2">
                                <Phone className="h-3 w-3" />
                                <span>{owner.phone}</span>
                              </div>
                            )}
                            <div className="flex items-center gap-2 text-xs text-gray-500">
                              <Calendar className="h-3 w-3" />
                              <span>Joined {new Date(owner.createdAt).toLocaleDateString()}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-col gap-2">
                          {getStatusBadge(owner.verificationStatus)}
                          {owner.company && (
                            <Badge variant="outline" className="text-xs">
                              <Briefcase className="h-3 w-3 mr-1" />
                              {owner.company}
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Role Badges */}
                      <div className="flex flex-wrap gap-1 mb-4">
                        {getRoleBadge(owner)}
                      </div>

                      {/* Location */}
                      {owner.address && (
                        <div className="flex items-center gap-2 mb-4 text-sm text-gray-600">
                          <MapPin className="h-4 w-4 text-blue-600" />
                          <span>{owner.address.city}, {owner.address.state}</span>
                        </div>
                      )}

                      {/* Properties Count */}
                      <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 mb-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-xs font-medium text-blue-700 uppercase tracking-wide">Properties</p>
                            <p className="text-xl font-bold text-blue-900">{owner.properties?.length || 0}</p>
                          </div>
                          <Building className="h-6 w-6 text-blue-600" />
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2 pt-2">
                        <Button
                          size="sm"
                          onClick={() => router.push(`/properties/owners/${owner._id}`)}
                          className="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-md hover:shadow-lg transition-all duration-200"
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                        {owner.verificationStatus === 'pending' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleVerifyOwner(owner._id)}
                            className="border-green-300 text-green-700 hover:bg-green-50 hover:border-green-400 transition-all duration-200"
                          >
                            <Shield className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => router.push(`/properties/owners/${owner._id}/edit`)}
                          className="border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400 transition-all duration-200"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDeleteOwner(owner._id)}
                          className="border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400 transition-all duration-200"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Pagination Controls */}
            {pagination.totalPages > 1 && (
              <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                <div className="flex items-center gap-2">
                  <Label className="text-sm font-medium">Show:</Label>
                  <Select value={pageSize.toString()} onValueChange={(value) => {
                    setPageSize(parseInt(value))
                    setCurrentPage(1)
                  }}>
                    <SelectTrigger className="w-20 border-blue-200">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="6">6</SelectItem>
                      <SelectItem value="9">9</SelectItem>
                      <SelectItem value="12">12</SelectItem>
                      <SelectItem value="18">18</SelectItem>
                    </SelectContent>
                  </Select>
                  <span className="text-sm text-gray-600">
                    Showing {((currentPage - 1) * pageSize) + 1}-{Math.min(currentPage * pageSize, pagination.totalItems)} of {pagination.totalItems} owners
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(1)}
                    disabled={currentPage === 1}
                    className="border-blue-200 text-blue-600 hover:bg-blue-50"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="border-blue-200 text-blue-600 hover:bg-blue-50"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                      let pageNum
                      if (pagination.totalPages <= 5) {
                        pageNum = i + 1
                      } else if (currentPage <= 3) {
                        pageNum = i + 1
                      } else if (currentPage >= pagination.totalPages - 2) {
                        pageNum = pagination.totalPages - 4 + i
                      } else {
                        pageNum = currentPage - 2 + i
                      }

                      return (
                        <Button
                          key={pageNum}
                          variant={currentPage === pageNum ? "default" : "outline"}
                          size="sm"
                          onClick={() => setCurrentPage(pageNum)}
                          className={currentPage === pageNum
                            ? "bg-blue-600 hover:bg-blue-700 text-white"
                            : "border-blue-200 text-blue-600 hover:bg-blue-50"
                          }
                        >
                          {pageNum}
                        </Button>
                      )
                    })}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === pagination.totalPages}
                    className="border-blue-200 text-blue-600 hover:bg-blue-50"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(pagination.totalPages)}
                    disabled={currentPage === pagination.totalPages}
                    className="border-blue-200 text-blue-600 hover:bg-blue-50"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
