'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { User, Mail, Phone, Calendar, AlertCircle, MapPin } from 'lucide-react'

interface PersonalData {
  firstName: string
  lastName: string
  email: string
  phone: string
  dateOfBirth: string
  address: {
    street: string
    city: string
    state: string
    country: string
    pincode: string
  }
  roles: {
    isOwner: boolean
    isDeveloper: boolean
    isInvestor: boolean
  }
}

interface PersonalDetailsProps {
  value: PersonalData
  onChange: (personal: PersonalData) => void
  errors?: {
    firstName?: string[]
    lastName?: string[]
    email?: string[]
    phone?: string[]
    dateOfBirth?: string[]
    address?: {
      street?: string[]
      city?: string[]
      state?: string[]
      country?: string[]
      pincode?: string[]
    }
    roles?: string[]
  }
}

export default function PersonalDetails({ value, onChange, errors }: PersonalDetailsProps) {
  const handleInputChange = (field: string, inputValue: string) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      onChange({
        ...value,
        [parent]: {
          ...value[parent as keyof PersonalData],
          [child]: inputValue
        }
      })
    } else {
      onChange({
        ...value,
        [field]: inputValue
      })
    }
  }

  const handleRoleChange = (role: keyof PersonalData['roles'], checked: boolean) => {
    onChange({
      ...value,
      roles: {
        ...value.roles,
        [role]: checked
      }
    })
  }

  const getSelectedRoles = () => {
    const roles = []
    if (value.roles.isOwner) roles.push('Owner')
    if (value.roles.isDeveloper) roles.push('Developer')
    if (value.roles.isInvestor) roles.push('Investor')
    return roles
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Personal Information
        </CardTitle>
        <CardDescription>
          Basic personal details and contact information
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Name Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name *</Label>
            <Input
              id="firstName"
              placeholder="Enter first name"
              value={value.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              className={errors?.firstName ? 'border-red-500' : ''}
            />
            {errors?.firstName && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.firstName[0]}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name *</Label>
            <Input
              id="lastName"
              placeholder="Enter last name"
              value={value.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              className={errors?.lastName ? 'border-red-500' : ''}
            />
            {errors?.lastName && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.lastName[0]}
              </p>
            )}
          </div>
        </div>

        {/* Contact Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email Address *</Label>
            <div className="relative">
              <Input
                id="email"
                type="email"
                placeholder="Enter email address"
                value={value.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={errors?.email ? 'border-red-500' : ''}
              />
              <Mail className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
            </div>
            {errors?.email && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.email[0]}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number *</Label>
            <div className="relative">
              <Input
                id="phone"
                type="tel"
                placeholder="Enter phone number"
                value={value.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className={errors?.phone ? 'border-red-500' : ''}
                maxLength={15}
              />
              <Phone className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
            </div>
            {errors?.phone && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.phone[0]}
              </p>
            )}
          </div>
        </div>

        {/* Date of Birth */}
        <div className="space-y-2">
          <Label htmlFor="dateOfBirth">Date of Birth *</Label>
          <div className="relative">
            <Input
              id="dateOfBirth"
              type="date"
              value={value.dateOfBirth}
              onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
              className={errors?.dateOfBirth ? 'border-red-500' : ''}
              max={new Date().toISOString().split('T')[0]}
            />
            <Calendar className="absolute right-3 top-3 h-4 w-4 text-muted-foreground pointer-events-none" />
          </div>
          {errors?.dateOfBirth && (
            <p className="text-sm text-red-500 flex items-center gap-1">
              <AlertCircle className="h-4 w-4" />
              {errors.dateOfBirth[0]}
            </p>
          )}
        </div>

        {/* Address Information */}
        <div className="space-y-4">
          <h4 className="font-medium flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Address Information
          </h4>
          
          <div className="space-y-2">
            <Label htmlFor="street">Street Address *</Label>
            <Input
              id="street"
              placeholder="Enter complete street address"
              value={value.address.street}
              onChange={(e) => handleInputChange('address.street', e.target.value)}
              className={errors?.address?.street ? 'border-red-500' : ''}
            />
            {errors?.address?.street && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.address.street[0]}
              </p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city">City *</Label>
              <Input
                id="city"
                placeholder="Enter city"
                value={value.address.city}
                onChange={(e) => handleInputChange('address.city', e.target.value)}
                className={errors?.address?.city ? 'border-red-500' : ''}
              />
              {errors?.address?.city && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.address.city[0]}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="state">State *</Label>
              <Input
                id="state"
                placeholder="Enter state"
                value={value.address.state}
                onChange={(e) => handleInputChange('address.state', e.target.value)}
                className={errors?.address?.state ? 'border-red-500' : ''}
              />
              {errors?.address?.state && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.address.state[0]}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="pincode">Pincode *</Label>
              <Input
                id="pincode"
                placeholder="Enter pincode"
                value={value.address.pincode}
                onChange={(e) => handleInputChange('address.pincode', e.target.value)}
                className={errors?.address?.pincode ? 'border-red-500' : ''}
                maxLength={6}
              />
              {errors?.address?.pincode && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.address.pincode[0]}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="country">Country *</Label>
            <Input
              id="country"
              placeholder="Enter country"
              value={value.address.country}
              onChange={(e) => handleInputChange('address.country', e.target.value)}
              className={errors?.address?.country ? 'border-red-500' : ''}
            />
            {errors?.address?.country && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.address.country[0]}
              </p>
            )}
          </div>
        </div>

        {/* Role Selection */}
        <div className="space-y-4">
          <h4 className="font-medium">Role Selection *</h4>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isOwner"
                checked={value.roles.isOwner}
                onCheckedChange={(checked) => handleRoleChange('isOwner', checked as boolean)}
              />
              <Label htmlFor="isOwner" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Property Owner
              </Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isDeveloper"
                checked={value.roles.isDeveloper}
                onCheckedChange={(checked) => handleRoleChange('isDeveloper', checked as boolean)}
              />
              <Label htmlFor="isDeveloper" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Developer
              </Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isInvestor"
                checked={value.roles.isInvestor}
                onCheckedChange={(checked) => handleRoleChange('isInvestor', checked as boolean)}
              />
              <Label htmlFor="isInvestor" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Investor
              </Label>
            </div>
          </div>
          
          {getSelectedRoles().length > 0 && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Selected roles:</span>
              {getSelectedRoles().map((role) => (
                <Badge key={role} variant="secondary">
                  {role}
                </Badge>
              ))}
            </div>
          )}
          
          {errors?.roles && (
            <p className="text-sm text-red-500 flex items-center gap-1">
              <AlertCircle className="h-4 w-4" />
              {errors.roles[0]}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
