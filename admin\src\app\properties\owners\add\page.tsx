'use client'

import React, { useState } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { toast } from 'sonner'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { useCreatePropertyOwnerMutation } from '@/store/api/propertyOwnersApi'
import {
  ArrowLeft,
  Save,
  User,
  Building,
  MapPin,
  Phone,
  Mail,
  FileText,
  Briefcase,
  DollarSign,
  Calendar,
  Upload,
  Plus,
  X,
  AlertCircle,
  CheckCircle
} from 'lucide-react'

interface OwnerFormData {
  // Personal Information
  firstName: string
  lastName: string
  email: string
  phone: string
  dateOfBirth: string
  
  // Address Information
  address: {
    street: string
    city: string
    state: string
    country: string
    pincode: string
  }
  
  // Role Information
  isOwner: boolean
  isDeveloper: boolean
  isInvestor: boolean
  
  // Business Information
  company?: string
  businessType?: string
  gstNumber?: string
  panNumber?: string
  
  // Bank Details
  bankDetails: {
    accountNumber: string
    ifscCode: string
    bankName: string
    accountHolderName: string
  }
  
  // Developer Specific
  developerDetails?: {
    experience: number
    completedProjects: number
    ongoingProjects: number
    specialization: string[]
    portfolio: string
    certifications: string[]
  }
  
  // Documents
  documents: {
    identityProof?: File[]
    addressProof?: File[]
    businessRegistration?: File[]
    gstCertificate?: File[]
    panCard?: File[]
    bankStatement?: File[]
    experienceCertificate?: File[]
    projectPortfolio?: File[]
  }
}

export default function AddPropertyOwnerPage() {
  const router = useRouter()
  const [createOwner, { isLoading }] = useCreatePropertyOwnerMutation()
  
  const [formData, setFormData] = useState<OwnerFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    address: {
      street: '',
      city: '',
      state: '',
      country: 'India',
      pincode: ''
    },
    isOwner: false,
    isDeveloper: false,
    isInvestor: false,
    company: '',
    businessType: '',
    gstNumber: '',
    panNumber: '',
    bankDetails: {
      accountNumber: '',
      ifscCode: '',
      bankName: '',
      accountHolderName: ''
    },
    developerDetails: {
      experience: 0,
      completedProjects: 0,
      ongoingProjects: 0,
      specialization: [],
      portfolio: '',
      certifications: []
    },
    documents: {}
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [currentStep, setCurrentStep] = useState(1)

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {}

    if (step === 1) {
      if (!formData.firstName.trim()) newErrors.firstName = 'First name is required'
      if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required'
      if (!formData.email.trim()) newErrors.email = 'Email is required'
      if (!formData.phone.trim()) newErrors.phone = 'Phone is required'
      if (!formData.dateOfBirth) newErrors.dateOfBirth = 'Date of birth is required'
    }

    if (step === 2) {
      if (!formData.address.street.trim()) newErrors['address.street'] = 'Street address is required'
      if (!formData.address.city.trim()) newErrors['address.city'] = 'City is required'
      if (!formData.address.state.trim()) newErrors['address.state'] = 'State is required'
      if (!formData.address.pincode.trim()) newErrors['address.pincode'] = 'Pincode is required'
    }

    if (step === 3) {
      if (!formData.isOwner && !formData.isDeveloper && !formData.isInvestor) {
        newErrors.roles = 'At least one role must be selected'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof OwnerFormData],
          [child]: value
        }
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }))
    }
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const handleSubmit = async () => {
    if (!validateStep(1) || !validateStep(2) || !validateStep(3)) {
      toast.error('Please fill in all required fields')
      return
    }

    try {
      await createOwner(formData).unwrap()
      toast.success('Property owner created successfully!')
      router.push('/properties/owners')
    } catch (error: any) {
      toast.error(error?.data?.message || 'Failed to create property owner')
      console.error('Create owner error:', error)
    }
  }

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 4))
    }
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const steps = [
    { number: 1, title: 'Personal Information', icon: User },
    { number: 2, title: 'Address Details', icon: MapPin },
    { number: 3, title: 'Role & Business', icon: Briefcase },
    { number: 4, title: 'Documents & Review', icon: FileText }
  ]

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => router.push('/properties/owners')}
              className="border-blue-200 text-blue-600 hover:bg-blue-50"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Owners
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Add Property Owner/Developer</h1>
              <p className="text-gray-600">Create a new property owner or developer account</p>
            </div>
          </div>
        </div>

        {/* Progress Steps */}
        <Card className="border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => {
                const Icon = step.icon
                const isActive = currentStep === step.number
                const isCompleted = currentStep > step.number
                
                return (
                  <div key={step.number} className="flex items-center">
                    <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                      isCompleted 
                        ? 'bg-green-500 border-green-500 text-white' 
                        : isActive 
                          ? 'bg-blue-500 border-blue-500 text-white' 
                          : 'bg-gray-100 border-gray-300 text-gray-500'
                    }`}>
                      {isCompleted ? (
                        <CheckCircle className="h-5 w-5" />
                      ) : (
                        <Icon className="h-5 w-5" />
                      )}
                    </div>
                    <div className="ml-3">
                      <p className={`text-sm font-medium ${
                        isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                      }`}>
                        Step {step.number}
                      </p>
                      <p className={`text-xs ${
                        isActive ? 'text-blue-500' : isCompleted ? 'text-green-500' : 'text-gray-400'
                      }`}>
                        {step.title}
                      </p>
                    </div>
                    {index < steps.length - 1 && (
                      <div className={`flex-1 h-0.5 mx-4 ${
                        isCompleted ? 'bg-green-500' : 'bg-gray-200'
                      }`} />
                    )}
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Form Content */}
        <Card className="border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center text-blue-700">
              {React.createElement(steps[currentStep - 1].icon, { className: "h-5 w-5 mr-2" })}
              {steps[currentStep - 1].title}
            </CardTitle>
            <CardDescription>
              {currentStep === 1 && "Enter the basic personal information"}
              {currentStep === 2 && "Provide complete address details"}
              {currentStep === 3 && "Select roles and business information"}
              {currentStep === 4 && "Upload documents and review details"}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            {/* Step 1: Personal Information */}
            {currentStep === 1 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="firstName" className="text-sm font-medium mb-2 block">
                    First Name *
                  </Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    placeholder="Enter first name"
                    className={`border-blue-200 focus:border-blue-500 ${errors.firstName ? 'border-red-500' : ''}`}
                  />
                  {errors.firstName && (
                    <p className="text-red-500 text-xs mt-1 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors.firstName}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="lastName" className="text-sm font-medium mb-2 block">
                    Last Name *
                  </Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    placeholder="Enter last name"
                    className={`border-blue-200 focus:border-blue-500 ${errors.lastName ? 'border-red-500' : ''}`}
                  />
                  {errors.lastName && (
                    <p className="text-red-500 text-xs mt-1 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors.lastName}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="email" className="text-sm font-medium mb-2 block">
                    Email Address *
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="Enter email address"
                    className={`border-blue-200 focus:border-blue-500 ${errors.email ? 'border-red-500' : ''}`}
                  />
                  {errors.email && (
                    <p className="text-red-500 text-xs mt-1 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors.email}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="phone" className="text-sm font-medium mb-2 block">
                    Phone Number *
                  </Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="Enter phone number"
                    className={`border-blue-200 focus:border-blue-500 ${errors.phone ? 'border-red-500' : ''}`}
                  />
                  {errors.phone && (
                    <p className="text-red-500 text-xs mt-1 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors.phone}
                    </p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="dateOfBirth" className="text-sm font-medium mb-2 block">
                    Date of Birth *
                  </Label>
                  <Input
                    id="dateOfBirth"
                    type="date"
                    value={formData.dateOfBirth}
                    onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                    className={`border-blue-200 focus:border-blue-500 ${errors.dateOfBirth ? 'border-red-500' : ''}`}
                  />
                  {errors.dateOfBirth && (
                    <p className="text-red-500 text-xs mt-1 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors.dateOfBirth}
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Step 2: Address Information */}
            {currentStep === 2 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <Label htmlFor="street" className="text-sm font-medium mb-2 block">
                    Street Address *
                  </Label>
                  <Textarea
                    id="street"
                    value={formData.address.street}
                    onChange={(e) => handleInputChange('address.street', e.target.value)}
                    placeholder="Enter complete street address"
                    className={`border-blue-200 focus:border-blue-500 ${errors['address.street'] ? 'border-red-500' : ''}`}
                    rows={3}
                  />
                  {errors['address.street'] && (
                    <p className="text-red-500 text-xs mt-1 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors['address.street']}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="city" className="text-sm font-medium mb-2 block">
                    City *
                  </Label>
                  <Input
                    id="city"
                    value={formData.address.city}
                    onChange={(e) => handleInputChange('address.city', e.target.value)}
                    placeholder="Enter city"
                    className={`border-blue-200 focus:border-blue-500 ${errors['address.city'] ? 'border-red-500' : ''}`}
                  />
                  {errors['address.city'] && (
                    <p className="text-red-500 text-xs mt-1 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors['address.city']}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="state" className="text-sm font-medium mb-2 block">
                    State *
                  </Label>
                  <Input
                    id="state"
                    value={formData.address.state}
                    onChange={(e) => handleInputChange('address.state', e.target.value)}
                    placeholder="Enter state"
                    className={`border-blue-200 focus:border-blue-500 ${errors['address.state'] ? 'border-red-500' : ''}`}
                  />
                  {errors['address.state'] && (
                    <p className="text-red-500 text-xs mt-1 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors['address.state']}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="country" className="text-sm font-medium mb-2 block">
                    Country *
                  </Label>
                  <Select value={formData.address.country} onValueChange={(value) => handleInputChange('address.country', value)}>
                    <SelectTrigger className="border-blue-200 focus:border-blue-500">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="India">India</SelectItem>
                      <SelectItem value="USA">United States</SelectItem>
                      <SelectItem value="UK">United Kingdom</SelectItem>
                      <SelectItem value="Canada">Canada</SelectItem>
                      <SelectItem value="Australia">Australia</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="pincode" className="text-sm font-medium mb-2 block">
                    Pincode *
                  </Label>
                  <Input
                    id="pincode"
                    value={formData.address.pincode}
                    onChange={(e) => handleInputChange('address.pincode', e.target.value)}
                    placeholder="Enter pincode"
                    className={`border-blue-200 focus:border-blue-500 ${errors['address.pincode'] ? 'border-red-500' : ''}`}
                  />
                  {errors['address.pincode'] && (
                    <p className="text-red-500 text-xs mt-1 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors['address.pincode']}
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Step 3: Role & Business Information */}
            {currentStep === 3 && (
              <div className="space-y-6">
                {/* Role Selection */}
                <div>
                  <Label className="text-sm font-medium mb-4 block">Select Roles *</Label>
                  {errors.roles && (
                    <p className="text-red-500 text-xs mb-2 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors.roles}
                    </p>
                  )}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-center space-x-2 p-4 border border-blue-200 rounded-lg">
                      <Checkbox
                        id="isOwner"
                        checked={formData.isOwner}
                        onCheckedChange={(checked) => handleInputChange('isOwner', checked)}
                      />
                      <div>
                        <Label htmlFor="isOwner" className="text-sm font-medium cursor-pointer">
                          Property Owner
                        </Label>
                        <p className="text-xs text-gray-500">Owns real estate properties</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 p-4 border border-blue-200 rounded-lg">
                      <Checkbox
                        id="isDeveloper"
                        checked={formData.isDeveloper}
                        onCheckedChange={(checked) => handleInputChange('isDeveloper', checked)}
                      />
                      <div>
                        <Label htmlFor="isDeveloper" className="text-sm font-medium cursor-pointer">
                          Developer
                        </Label>
                        <p className="text-xs text-gray-500">Develops real estate projects</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 p-4 border border-blue-200 rounded-lg">
                      <Checkbox
                        id="isInvestor"
                        checked={formData.isInvestor}
                        onCheckedChange={(checked) => handleInputChange('isInvestor', checked)}
                      />
                      <div>
                        <Label htmlFor="isInvestor" className="text-sm font-medium cursor-pointer">
                          Investor
                        </Label>
                        <p className="text-xs text-gray-500">Invests in properties</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Business Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="company" className="text-sm font-medium mb-2 block">
                      Company Name
                    </Label>
                    <Input
                      id="company"
                      value={formData.company}
                      onChange={(e) => handleInputChange('company', e.target.value)}
                      placeholder="Enter company name"
                      className="border-blue-200 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <Label htmlFor="businessType" className="text-sm font-medium mb-2 block">
                      Business Type
                    </Label>
                    <Select value={formData.businessType} onValueChange={(value) => handleInputChange('businessType', value)}>
                      <SelectTrigger className="border-blue-200 focus:border-blue-500">
                        <SelectValue placeholder="Select business type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="individual">Individual</SelectItem>
                        <SelectItem value="partnership">Partnership</SelectItem>
                        <SelectItem value="private_limited">Private Limited</SelectItem>
                        <SelectItem value="public_limited">Public Limited</SelectItem>
                        <SelectItem value="llp">LLP</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="gstNumber" className="text-sm font-medium mb-2 block">
                      GST Number
                    </Label>
                    <Input
                      id="gstNumber"
                      value={formData.gstNumber}
                      onChange={(e) => handleInputChange('gstNumber', e.target.value)}
                      placeholder="Enter GST number"
                      className="border-blue-200 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <Label htmlFor="panNumber" className="text-sm font-medium mb-2 block">
                      PAN Number
                    </Label>
                    <Input
                      id="panNumber"
                      value={formData.panNumber}
                      onChange={(e) => handleInputChange('panNumber', e.target.value)}
                      placeholder="Enter PAN number"
                      className="border-blue-200 focus:border-blue-500"
                    />
                  </div>
                </div>

                {/* Bank Details */}
                <div>
                  <Label className="text-sm font-medium mb-4 block">Bank Details</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="accountNumber" className="text-sm font-medium mb-2 block">
                        Account Number
                      </Label>
                      <Input
                        id="accountNumber"
                        value={formData.bankDetails.accountNumber}
                        onChange={(e) => handleInputChange('bankDetails.accountNumber', e.target.value)}
                        placeholder="Enter account number"
                        className="border-blue-200 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <Label htmlFor="ifscCode" className="text-sm font-medium mb-2 block">
                        IFSC Code
                      </Label>
                      <Input
                        id="ifscCode"
                        value={formData.bankDetails.ifscCode}
                        onChange={(e) => handleInputChange('bankDetails.ifscCode', e.target.value)}
                        placeholder="Enter IFSC code"
                        className="border-blue-200 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <Label htmlFor="bankName" className="text-sm font-medium mb-2 block">
                        Bank Name
                      </Label>
                      <Input
                        id="bankName"
                        value={formData.bankDetails.bankName}
                        onChange={(e) => handleInputChange('bankDetails.bankName', e.target.value)}
                        placeholder="Enter bank name"
                        className="border-blue-200 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <Label htmlFor="accountHolderName" className="text-sm font-medium mb-2 block">
                        Account Holder Name
                      </Label>
                      <Input
                        id="accountHolderName"
                        value={formData.bankDetails.accountHolderName}
                        onChange={(e) => handleInputChange('bankDetails.accountHolderName', e.target.value)}
                        placeholder="Enter account holder name"
                        className="border-blue-200 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Documents & Review */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <h3 className="text-lg font-semibold text-blue-900 mb-2">Review Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <p><strong>Name:</strong> {formData.firstName} {formData.lastName}</p>
                      <p><strong>Email:</strong> {formData.email}</p>
                      <p><strong>Phone:</strong> {formData.phone}</p>
                    </div>
                    <div>
                      <p><strong>City:</strong> {formData.address.city}</p>
                      <p><strong>State:</strong> {formData.address.state}</p>
                      <p><strong>Roles:</strong> {[
                        formData.isOwner && 'Owner',
                        formData.isDeveloper && 'Developer',
                        formData.isInvestor && 'Investor'
                      ].filter(Boolean).join(', ')}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium mb-4 block">Document Upload (Optional)</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="border-2 border-dashed border-blue-300 rounded-lg p-6 text-center">
                      <Upload className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                      <p className="text-sm text-gray-600 mb-2">Identity Proof</p>
                      <Button variant="outline" size="sm" className="border-blue-300 text-blue-600">
                        Choose Files
                      </Button>
                    </div>

                    <div className="border-2 border-dashed border-blue-300 rounded-lg p-6 text-center">
                      <Upload className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                      <p className="text-sm text-gray-600 mb-2">Address Proof</p>
                      <Button variant="outline" size="sm" className="border-blue-300 text-blue-600">
                        Choose Files
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex items-center justify-between pt-6 border-t border-gray-200">
              <Button
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 1}
                className="border-blue-200 text-blue-600 hover:bg-blue-50"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <div className="flex items-center gap-2">
                {currentStep < 4 ? (
                  <Button
                    onClick={nextStep}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    Next Step
                    <ArrowLeft className="h-4 w-4 ml-2 rotate-180" />
                  </Button>
                ) : (
                  <Button
                    onClick={handleSubmit}
                    disabled={isLoading}
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Creating...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Create Owner
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
